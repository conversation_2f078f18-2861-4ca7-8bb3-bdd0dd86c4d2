package com.turinggear.shr_marina

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaPlayer
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.toComposeRect
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.window.layout.WindowMetricsCalculator
import com.google.zxing.BarcodeFormat
import com.google.zxing.WriterException
import com.google.zxing.qrcode.QRCodeWriter
import com.turinggear.shr_marina.ui.theme.SsaTheme
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

fun ByteArray.toHexString() = joinToString("") { "%02x".format(it) }

fun String.decodeHex(): ByteArray {
    check(length % 2 == 0) { "Must have an even length" }
    return chunked(2)
        .map { it.toInt(16).toByte() }
        .toByteArray()
}

@Throws(WriterException::class)
fun encodeAsBitmap(text: String, width: Int, height: Int): Bitmap {
    val writer = QRCodeWriter()
    val bitMatrix = writer.encode(
        text,
        BarcodeFormat.QR_CODE,
        width,
        height
    )
    val w = bitMatrix.width
    val h = bitMatrix.height
    val pixels = IntArray(w * h)
    for (y in 0 until h) {
        for (x in 0 until w) {
            pixels[y * w + x] = if (bitMatrix[x, y]) android.graphics.Color.BLACK else android.graphics.Color.WHITE
        }
    }
    val bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
    bitmap.setPixels(pixels, 0, w, 0, 0, w, h)
    return bitmap
}

class MainActivity : ComponentActivity() {
    @SuppressLint("ResourceType")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // zc-339a
        // 隐藏的api,从下边链接发现的
        // [flutter_hardware_interaction/FlutterHardwareInteractionPlugin.java at 596283fc346c49017f68d2cbd6c487a5ae0023c6 · hisan-web/flutter_hardware_interaction](https://github.com/hisan-web/flutter_hardware_interaction/blob/596283fc346c49017f68d2cbd6c487a5ae0023c6/android/src/main/java/com/hisanweb/flutter_hardware_interaction/FlutterHardwareInteractionPlugin.java#L74)
        this.sendBroadcast(Intent("hide.systemui"))
        this.sendBroadcast(Intent("com.zc.close_gesture"))

        var loopPlayer = MediaPlayer.create(<EMAIL>, R.raw.loop_hengdian)
        loopPlayer.isLooping = true
        loopPlayer.seekTo(0)
        loopPlayer.start()

        setContent {
            val configuration = LocalConfiguration.current
            val windowMetrics = remember(configuration) {
                WindowMetricsCalculator.getOrCreate()
                    .computeCurrentWindowMetrics(this)
            }
            val windowDpSize = with(LocalDensity.current) {
                windowMetrics.bounds.toComposeRect().size.toDpSize()
            }
            val isPortrait = windowDpSize.width < windowDpSize.height

            SsaTheme {
                // A surface container using the 'background' color from the theme
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colors.background
                ) {
                    // 日期行
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 8.dp)
                            .height(8.dp)
                            .zIndex(100f),
                        horizontalArrangement = Arrangement.End,
                        verticalAlignment = Alignment.Top
                    ) {
                        Box(Modifier.weight(1f),
                            contentAlignment = Alignment.TopEnd,
                        ) {

                            var currentDateAndTime = "2022"
//                            val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            val sdf = SimpleDateFormat("yyyy-MM-dd")
                            currentDateAndTime = sdf.format(Date())

                            Text(
                                text = currentDateAndTime,
                                modifier = Modifier
                                    .pointerInput(Unit) {
                                        detectTapGestures(
                                            // 双击退出全屏用于调试
                                            onDoubleTap = {
                                                <EMAIL>(Intent("show.systemui"))
                                                <EMAIL>(Intent("com.zc.open_gesture"))
                                            },
                                            // 单击全屏
                                            onTap = {
                                                <EMAIL>(Intent("hide.systemui"))
                                                <EMAIL>(Intent("com.zc.close_gesture"))
                                            }
                                        )
                                    },
                                style = MaterialTheme.typography.h6.copy(fontWeight = FontWeight.SemiBold)
                            )
                        }
                    }
                    Box(contentAlignment = Alignment.Center) {
//                        if (isPortrait) {
                        Image(
                            painter = painterResource(id = R.raw.bg),
                                contentDescription = null,
                            )
//                        }
                        Column(
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
//                            Text(
//                                text = "微信扫码还船",
//                                modifier = Modifier.padding(bottom = 15.dp),
//                                style = MaterialTheme.typography.h6.copy(fontWeight = FontWeight.SemiBold)
//                            )

                            var hmac by rememberSaveable { mutableStateOf("") }
                            var qrcode by rememberSaveable { mutableStateOf("") }

                            if (qrcode.isNotEmpty()) {
                                Image(
                                    bitmap = encodeAsBitmap(
                                        text = qrcode,
                                        width = 452,
                                        height = 452
                                    ).asImageBitmap(),
                                    contentDescription = null,
                                    // 背景图偏
                                    modifier = Modifier.offset(x=-25.dp)
                                )
                            }

                            LaunchedEffect(Unit) {
                                while (true) {
                                    val key = "90811c841aeceb4c7533cde7a50e7c80"
                                    val msg =
                                        "date=${System.currentTimeMillis() / 1000}" // "date=1658425310" // ce09c4df73be0192ad28617b10ac8fdd
                                    val mac = Mac.getInstance("HmacMD5")
                                    val sks = SecretKeySpec(key.decodeHex(), "HmacMD5")
                                    mac.init(sks)
                                    val hmacmd5 = mac.doFinal(msg.toByteArray())
                                    hmac = hmacmd5.toHexString()
                                    qrcode = "https://daogx.daohezhixing.com/return_boat?$msg&hmac=$hmac"
                                    delay(60 * 1000)
                                }
                            }
                        }
//                        Text(
//                            text = "1、请使用微信扫描屏幕上方二维码打开小程序。\n2、小程序扫码还船后自动结束订单。",
//                            modifier = Modifier.padding(top = 640.dp),
//                            color = Color(0xFFDD683F),
//                            style = MaterialTheme.typography.h5.copy(fontWeight = FontWeight.SemiBold)
//                        )
                    }
                }
            }
        }
    }


}

@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    SsaTheme {
        Text(text = "Hello Android!")
    }
}