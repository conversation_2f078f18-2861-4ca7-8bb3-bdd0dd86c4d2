# ssa-app

## 开发
对于高版本的Android Studio会提示JDK版本错误,比如
```
 Your build is currently configured to use incompatible Java 21.0.3 and
 Gradle 8.0. Cannot sync the project.
 
 We recommend upgrading to Gradle version 8.9.
 
 The minimum compatible Gradle version is 8.5.
 
 The maximum compatible Gradle JVM version is 19.
 
 Possible solutions:
  - Upgrade to Gradle 8.9 and re-sync
  - Upgrade to Gradle 8.5 and re-sync
```
可以修改默认的JDK:
```
File ->
 settings ->
    build, execution , Development ->
      build tools ->
        gradle ->
```
本项目选择Zulu发行的JDK 17.

## 一个发布流程

### 构建单个包

1. 升级大屏软件版本: 进入kiosk, `../gradlew bumpMinor`.

2. 构建单个景区: 在kiosk/, `make yqh`.

3. 找到apk: 在kiosk/, `make ls`找到apks文件上传到admin或者提供给SSA景区工具adb安装.

### 构建单个包然后发布到下载页面

1. 可选,升级版本

2. 在handheld目录 `make waterpark`

3. 在handheld目录, `make upload_landing_page_waterpark` 上传

### 构建所有包然后手动上传admin供自动更新

1. 进入kiosk, `../gradlew bumpMinor`.

2. 进入car, `../gradlew bumpMinor`.

3. 在项目根目录, `make clean yqh copy`.

4. 在项目根目录, 找到apks/下文可以在admin上传.

### 构建所有包然后发布到下载页面

首先执行`构建所有包`, 然后检查apks目录后, `make upload_landing_page_waterpark`.

### 构建所有包然后上传oss tmp/apks目录供SSA景区工具使用

一般是新景区安装时候用到脚本工具

首先执行`构建所有包`, 然后检查apks目录后, `make upload_device_tools_yqh`.

## 有用的命令

```
	openssl pkcs8 -in platform.pk8 -inform DER -outform PEM -out platform.priv.pem -nocrypt
	openssl pkcs12 -export -in platform.x509.pem -inkey platform.priv.pem -out platform.pk12 -name key0
	keytool -importkeystore -destkeystore os.jks -srckeystore platform.pk12 -srcstoretype PKCS12 -srcstorepass 12345678 -alias key0

	adb shell pm list packages -f | grep ssa
```

查看apk版本

```
$ANDROID_HOME/build-tools/28.0.3/aapt dump badging test.apk | grep "versionName" | sed -e "s/.*versionName='//" -e "s/' .*//"
```

android studio自带模拟器解决无网络问题
```
emulator -avd kiosk711 -dns-server 8.8.8.8
```
