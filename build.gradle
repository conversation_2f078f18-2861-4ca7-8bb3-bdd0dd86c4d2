buildscript {
    ext {
        compose_version = '1.1.1'
    }
    repositories {
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath "com.github.alexfu:androidautoversion:3.3.0"
    }
}// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '7.1.3' apply false
    id 'com.android.library' version '7.1.3' apply false
    id 'org.jetbrains.kotlin.android' version '1.6.10' apply false
    id 'org.jetbrains.kotlin.plugin.serialization' version '1.6.10' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
