plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlinx-serialization'
}

android {
    compileSdk 33

    defaultConfig {
        minSdk 25
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled = true
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion compose_version
        kotlinCompilerVersion '1.6.10'
    }
    flavorDimensions += "version"
    productFlavors {
        yqh {
            dimension = "version"
        }
        shiyuan {
            dimension = "version"
        }
        waterpark {
            dimension = "version"
        }
        jiuzhouwa {
            dimension = "version"
        }
        haituo {
            dimension = "version"
        }
        cssh {
            dimension = "version"
        }
        hdboat {
            dimension = "version"
        }
        main {
            dimension = "version"
        }

        csshboat {
            dimension = "version"
        }

        shg {
            dimension = "version"
        }

        lihuboat {
            dimension = "version"
        }

        fhxdboat {
            dimension = "version"
        }
    }

}

dependencies {

    implementation 'androidx.core:core-ktx:1.9.0'
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.material:material:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.android.material:material:1.6.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.3.3'
    implementation 'com.squareup.okhttp3:okhttp:5.0.0-alpha.9'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'

    def ktor_version = '2.0.3'
    implementation("io.ktor:ktor-client-okhttp:$ktor_version")
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation("io.ktor:ktor-client-logging-jvm:$ktor_version")

    implementation 'org.hashids:hashids:1.0.3'
}
