/**
 * Author          : <PERSON><PERSON><PERSON><PERSON>
 * Contact         : <EMAIL>
 * Github Username : muddassir235
 *
 * ConnectionChecker.kt
 *
 * Class used to check connectivity of the application to the internet. It works as follows
 *
 * - Every five seconds the app tries to ping google.com
 *    - If google.com loads within 2 seconds the app is marked as CONNECTED
 *    - If google.com takes more than 2 seconds to load the connection is deemed SLOW.
 *    - If google.com doesn't load the app is marked as DISCONNECTED
 */
package com.turinggear.ssa_shared.util

import android.annotation.SuppressLint
import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.security.SecureRandom
import java.security.cert.X509Certificate
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSession
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

// 初始为未知, 注意 RECONNECTED 状态是个逻辑状态, 从掉线改为连接时候通知
enum class ConnectionState {
    UNKNOWN, CONNECTED, RECONNECTED, DISCONNECTED
}

const val POLLING_INTERVAL: Long = 5000

//interface ConnectivityListener {
//    fun onConnectionState(state: ConnectionState)
//}

//class ConnectionChecker(
//    private val lifecycleOwner: LifecycleOwner?,
//    private val url: String = "https://www.aliyun.com",
//    private val leastLifecycleState: Lifecycle.State = Lifecycle.State.RESUMED
//) {
//    var connectivityListener: ConnectivityListener? = null
//        set(value) {
//            if (value != null) {
//                checkConnection(lifecycleOwner, url, leastLifecycleState) {
//                    value.onConnectionState(it)
//                }
//            }
//            field = value
//        }
//}

//fun checkConnection(
//    lifecycleOwner: LifecycleOwner?,
//    url: String = "https://www.aliyun.com",
//    stopOnConnected: Boolean = false,
//    leastLifecycleState: Lifecycle.State = Lifecycle.State.RESUMED,
//) {
//    if (lifecycleOwner == null) throw java.lang.IllegalArgumentException("Unable to find lifecycle scope.")
//
//    checkConnection(lifecycleOwner, url, leastLifecycleState, stopOnConnected) {
//        (lifecycleOwner as? ConnectivityListener)?.onConnectionState(it)
//    }
//}

fun checkConnection(
    lifecycleOwner: LifecycleOwner?,
    url: String = "https://www.aliyun.com",
    leastLifecycleState: Lifecycle.State = Lifecycle.State.RESUMED,
    stopOnConnected: Boolean = false,
    onConnectionState: (connectionState: ConnectionState) -> Unit,
) {
    val scope = lifecycleOwner?.lifecycleScope
        ?: throw java.lang.IllegalArgumentException("Unable to find lifecycle scope.")

    when (leastLifecycleState) {
        Lifecycle.State.RESUMED -> scope.launchWhenResumed {
            checkConnection(url, stopOnConnected, onConnectionState)
        }
        Lifecycle.State.STARTED -> scope.launchWhenStarted {
            checkConnection(url, stopOnConnected, onConnectionState)
        }
        Lifecycle.State.CREATED -> scope.launchWhenCreated {
            checkConnection(url, stopOnConnected, onConnectionState)
        }
        else -> throw IllegalArgumentException(
            "leastLifecycleState should be one of CREATED, STARTED, RESUMED"
        )
    }
}

fun createClient(): OkHttpClient {
    val trustAllCerts = arrayOf<TrustManager>(
        @SuppressLint("CustomX509TrustManager")
        object : X509TrustManager {
            @SuppressLint("TrustAllX509TrustManager")
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
            }

            @SuppressLint("TrustAllX509TrustManager")
            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
            }

            override fun getAcceptedIssuers(): Array<X509Certificate> {
                return arrayOf()
            }
        }
    )

    val sslContext: SSLContext = SSLContext.getInstance("SSL")
    sslContext.init(null, trustAllCerts, SecureRandom())
    val newBuilder = OkHttpClient.Builder()
    newBuilder.sslSocketFactory(sslContext.socketFactory, trustAllCerts[0] as X509TrustManager)
    newBuilder.hostnameVerifier(hostnameVerifier = { _: String?, _: SSLSession? -> true })

    return newBuilder.build()
}

private suspend fun checkConnection(
    url: String,
    stopOnConnected: Boolean,
    onConnectionState: (connectionState: ConnectionState) -> Unit
) {
    val client: OkHttpClient = createClient()

    withContext(Dispatchers.IO) {
        // 首次连接是未知状态, 第一次检查会确定网络状态
        var currentState = ConnectionState.UNKNOWN
        while (true) {
            // Log.e("******** Calling", "Connection " + url)
            val request: Request = Request.Builder().url(url)
                .method("HEAD", null).build()

            val response: Response? = try {
                client.newCall(request).execute()
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }

            val responseCode = response?.code ?: 500
            response?.close()

            Log.i("ConnectionChecker", "check resp $responseCode")

            withContext(Dispatchers.Main) {
                val connectionState = if (!responseCode.toString().startsWith("2")) {
                    ConnectionState.DISCONNECTED
                } else {
                    ConnectionState.CONNECTED
                }

                if (currentState == ConnectionState.UNKNOWN) {
                    // 第一次检查网络, 仅初始化自己不做处理
                    currentState = connectionState
                    // 第一次如果是连接状态,通知
                    if (connectionState == ConnectionState.CONNECTED) {
                        onConnectionState(ConnectionState.CONNECTED)
                    }
                } else if (connectionState != currentState) {
                    // 当状态改变
                    // 当新的状态是connected,那么其实是一个reconnected,通知为重连状态,但实际自己存储认为connected
                    if (connectionState == ConnectionState.CONNECTED) {
                        onConnectionState(ConnectionState.RECONNECTED)
                    } else {
                        onConnectionState(connectionState)
                    }
                    currentState = connectionState
                } else {
                    // 当状态不变
                    // Log.e("ConnectionChecker", "NO Change")
                }
            }

            if (stopOnConnected && currentState == ConnectionState.CONNECTED) {
                Log.i("ConnectionChecker", "connected, stop check")
                break
            }

            kotlinx.coroutines.delay(POLLING_INTERVAL)
        }
    }
}