package com.turinggear.ssa_shared.util

import com.turinggear.ssa_shared.model.Ticket

const val MAX_TICKET_STRING_LENGTH = 64

// 黑名单,去掉非法字符
fun cleanScannedQrcode1(sequenceString: String): String {
//            BARCODE = sequenceString.replace("\n", "")
//            BARCODE = sequenceString.replace("\n", "").trimStart('\u0000')

    // [support](https://support.exactonline.com/community/s/article/All-All-DNO-Content-urlcharacters?language=en_GB)
    val UNSAFE_CHARACTORS = setOf(';',  '/',  '?', ':', '@', '=', '&',
        '"', '<', '>', '#', '%', '{', '}', '|', '\\', '^', '~', '[', ']', '`'
    )
    val processed = sequenceString.replace(Regex("[\\p{C}]"), "") // 去掉不可见字符
        .filterNot { it in UNSAFE_CHARACTORS }
    return processed
}

// 白名单,只允许合法字符
fun cleanScannedQrcode(sequenceString: String): String {
    val whitelistRegex = Regex("[^0-9A-Za-z._-]")
    return sequenceString.replace(whitelistRegex, "").take(MAX_TICKET_STRING_LENGTH)
}

fun isValidCodeForOldFormat(input: String): Boolean {
    // 对于旧格式
    // 字母 数字 . - _
    val regex = "^[0-9A-Za-z._-]+$".toRegex()
    return regex.matches(input)
}

class TicketQrcodeParser {
    companion object {
        fun guessFormat(code: String): Int {
            if (code.length > MAX_TICKET_STRING_LENGTH) {
                return TicketQrcode.INVALID_FORMAT
            }
            // 尝试旧的格式
            val array = code.split(".")
            if (array.count() == 3) {
                val goodId = array[0]
                val orderNo = array[1]
                val value = array[2]
                if (goodId.toIntOrNull() == null) {
                    return TicketQrcode.INVALID_FORMAT
                }
                if (!isValidCodeForOldFormat(orderNo)) {
                    return TicketQrcode.INVALID_FORMAT
                }

                if (value.toIntOrNull() != null) {
                    return TicketQrcode.FORMAT_ID
                } else if (isValidCodeForOldFormat(value)) {
                    return TicketQrcode.FORMAT_NO
                }
            } else if (array.count() == 1) {
                val spited = code.split("-")
                if (spited.count() == 2) {
                    return TicketQrcode.FORMAT_V2;
                } else if (spited.count() == 1) {
                    // 另一种格式
                    return TicketQrcode.FORMAT_PAPER
                }
            }

            return TicketQrcode.INVALID_FORMAT
        }

        fun parse(code: String): TicketQrcode? {
            val format = guessFormat(code)
            if (format == TicketQrcode.INVALID_FORMAT) {
                return null
            }
            when (format) {
                TicketQrcode.FORMAT_ID -> {
                    val array = code.split(".")
                    val ticket = TicketQrcode()
                    ticket.code = code
                    ticket.goodId = array[0]
                    ticket.orderNo = array[1]
                    ticket.ticketId = array[2]
                    ticket.format = format
                    return ticket
                }
                TicketQrcode.FORMAT_NO -> {
                    val array = code.split(".")
                    val ticket = TicketQrcode()
                    ticket.code = code
                    ticket.goodId = array[0]
                    ticket.orderNo = array[1]
                    ticket.ticketNo = array[2]
                    ticket.format = format
                    return ticket
                }
                TicketQrcode.FORMAT_V2 -> {
                    val array = code.split("-")
                    val ticket = TicketQrcode()
                    ticket.code = code
                    ticket.codeType = array[0]
                    ticket.codeValue = array[1]
                    ticket.format = format
                    return ticket
                }
                TicketQrcode.FORMAT_PAPER-> {
                    val ticket = TicketQrcode()
                    ticket.code = code
                    ticket.format = format
                    return ticket
                }
                else -> {
                    val ticket = TicketQrcode()
                    ticket.code = code
                    ticket.format = format
                    return ticket
                }
            }
        }
    }
}

class TicketQrcode {
    var code = ""
    // TODO: 类型不统一
    var ticketId = ""
    var ticketNo = ""
    var orderNo = ""
    var goodId = ""
    var codeType = ""
    var codeValue = ""
    var format = INVALID_FORMAT

    companion object {
        // 1.2024062714382307556351232.34
        val FORMAT_ID = 1
        // 1.2024062714382307556351232.2iS3m0PplhtVjftp9IQCibvIce2
        val FORMAT_NO = 2
        // 2iS3m0PplhtVjftp9IQCibvIce2
        val FORMAT_PAPER = 3
        // T-123123j
        val FORMAT_V2 = 4

        val INVALID_FORMAT = -1
    }

    fun getIdentifier(): String {
        when (format) {
            FORMAT_ID -> {
                return ticketId
            }
            FORMAT_NO -> {
                return ticketNo
            }
            FORMAT_PAPER -> {
                return code
            }
            FORMAT_V2 -> {
                return code
            }
            else -> {
                return ""
            }
        }
    }
}

fun formatTicketQrcode(goodId: Int?, orderNo: String?, ticket: Ticket, format: Int): String {
//    Log.e("XXX", "formatTicketQrcode: $goodId, $orderNo, $ticket, $format")
    var qrcode: String
    when (format) {
        TicketQrcode.FORMAT_ID -> {
            qrcode = "$goodId.$orderNo.${ticket.id}"
        }
        TicketQrcode.FORMAT_NO -> {
            qrcode = "$goodId.$orderNo.${ticket.ticket_no}"
        }
        TicketQrcode.FORMAT_V2 -> {
            qrcode = ticket.ticket_no
        }
        else -> {
            // 默认id
            qrcode = "$goodId.$orderNo.${ticket.id}"
        }
    }

    return qrcode
}