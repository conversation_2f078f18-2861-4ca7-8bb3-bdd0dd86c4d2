package com.turinggear.ssa_shared.util

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException

class ShellCommander {

    companion object {
        private const val TAG = "ShellCommander"
        private const val SYSTEM_SH_PATH = "/system/bin/sh"

        // Enum to represent the two su strategies
        enum class SuStrategy {
            SU_C,       // Uses "su -c command"
            SU_0_SH_C   // Uses "su 0 /system/bin/sh -c command"
        }

        // Lazily determined strategy. This block will execute only once when activeStrategy is first accessed.
        private val activeStrategy: SuStrategy by lazy {
            determineBestSuStrategy()
        }

        /**
         * Determines the best su strategy by testing "su -c ls".
         * If "su -c ls" executes successfully (exit code 0), SU_C strategy is chosen.
         * Otherwise, SU_0_SH_C strategy is chosen as a fallback.
         * This method is called only once due to the lazy initialization of activeStrategy.
         */
        fun determineBestSuStrategy(): SuStrategy {
            val testCommand = "ls" // Simple command to test basic execution
            Log.d(TAG, "Determining su strategy. Testing with: su -c $testCommand")

            var process: Process? = null
            try {
                // Construct ProcessBuilder for "su -c ls"
                val processBuilder = ProcessBuilder("su", "-c", testCommand)
                process = processBuilder.start()

                // It's important to close the output stream of the process if not used.
                try {
                    process.outputStream.close()
                } catch (e: IOException) {
                    Log.w(TAG, "determineBestSuStrategy: Warning: Failed to close test process outputStream: ${e.message}")
                }

                // Consume streams to prevent the process from blocking.
                // For a simple "ls", output is small, but this is good practice.
                val stdInputReader = process.inputStream.bufferedReader()
                val stdErrorReader = process.errorStream.bufferedReader()
                var stdOutputTest: String? = null
                var stdErrorTest: String? = null

                try {
                    stdOutputTest = stdInputReader.readText()
                    stdErrorTest = stdErrorReader.readText()
                } catch (e: IOException) {
                     Log.w(TAG, "determineBestSuStrategy: IOException while consuming test process streams: ${e.message}")
                } finally {
                    try { stdInputReader.close() } catch (e: IOException) { /* ignore */ }
                    try { stdErrorReader.close() } catch (e: IOException) { /* ignore */ }
                }

                val exitCode = process.waitFor() // Wait for the test command to complete.

                if (exitCode == 0) {
                    Log.i(TAG, "Test 'su -c $testCommand' successful (Exit Code: $exitCode). Using SU_C strategy.")
                    if (stdOutputTest?.isNotBlank() == true) Log.d(TAG, "Test 'su -c $testCommand' STDOUT: $stdOutputTest")
                    if (stdErrorTest?.isNotBlank() == true) Log.d(TAG, "Test 'su -c $testCommand' STDERR: $stdErrorTest") // May contain warnings
                    return SuStrategy.SU_C
                } else {
                    Log.w(TAG, "Test 'su -c $testCommand' failed (Exit Code: $exitCode). Defaulting to SU_0_SH_C strategy.")
                    if (stdOutputTest?.isNotBlank() == true) Log.w(TAG, "Test 'su -c $testCommand' STDOUT (on failure): $stdOutputTest")
                    if (stdErrorTest?.isNotBlank() == true) Log.w(TAG, "Test 'su -c $testCommand' STDERR (on failure): $stdErrorTest")
                    return SuStrategy.SU_0_SH_C
                }
            } catch (e: IOException) {
                Log.e(TAG, "IOException during su strategy test ('su -c $testCommand'): ${e.message}. Defaulting to SU_0_SH_C.", e)
                return SuStrategy.SU_0_SH_C
            } catch (e: SecurityException) {
                Log.e(TAG, "SecurityException during su strategy test ('su -c $testCommand'): ${e.message}. Defaulting to SU_0_SH_C.", e)
                return SuStrategy.SU_0_SH_C
            } catch (e: InterruptedException) {
                Log.e(TAG, "Su strategy test ('su -c $testCommand') interrupted: ${e.message}. Defaulting to SU_0_SH_C.", e)
                Thread.currentThread().interrupt() // Restore interrupted status
                return SuStrategy.SU_0_SH_C
            } catch (e: Exception) {
                Log.e(TAG, "Generic exception during su strategy test ('su -c $testCommand'): ${e.message}. Defaulting to SU_0_SH_C.", e)
                return SuStrategy.SU_0_SH_C
            } finally {
                process?.destroy() // Ensure the test process is cleaned up.
            }
        }

        /**
         * Gets the arguments for ProcessBuilder based on the determined active strategy.
         * @param command The actual command string to be executed by the shell.
         * @return A list of strings representing the command and its arguments for ProcessBuilder.
         */
        internal fun getProcessBuilderArgs(command: String): List<String> {
            return when (activeStrategy) {
                SuStrategy.SU_C -> {
                    // Use "su -c <command>"
                    // The <command> string will be parsed by the shell invoked by su.
                    listOf("su", "-c", command)
                }
                SuStrategy.SU_0_SH_C -> {
                    // Use "su 0 /system/bin/sh -c <command>"
                    listOf("su", "0", SYSTEM_SH_PATH, "-c", command)
                }
            }
        }
    }

    /**
     * Runs a shell command as root using the determined su strategy.
     *
     * @param command The command string to execute. For example, "mount -o rw,remount /system".
     * @return True if the command executed successfully (exit code 0), false otherwise.
     */
    suspend fun runAsRoot(command: String): Boolean = withContext(Dispatchers.IO) {
        var process: Process? = null
        var success = false

        // The 'command' parameter is the actual command string for sh -c or su -c.
        // Example: "mount -o rw,remount /dev/block/platform/fe330000.sdhci/by-name/system /system"

        try {
            val processBuilderArgs = getProcessBuilderArgs(command) // Get args from companion object
            Log.d(TAG, "Attempting to execute with root. Using strategy: $activeStrategy. Full command: ${processBuilderArgs.joinToString(" ")}")

            val processBuilder = ProcessBuilder(processBuilderArgs)
            
            // Optional: redirect error stream to standard output if you want to read them combined
            // processBuilder.redirectErrorStream(true)

            process = processBuilder.start()

            // Close the process's output stream (stdin for the spawned process)
            // as we are passing the command directly via arguments.
            try {
                process.outputStream.close()
            } catch (e: IOException) {
                Log.w(TAG, "Warning: Failed to close process outputStream: ${e.message}")
            }

            // Read the standard output and standard error streams from the process.
            // This is crucial to prevent the process from blocking.
            val stdInputReader = process.inputStream.bufferedReader()
            val stdErrorReader = process.errorStream.bufferedReader()

            var stdOutput = ""
            var stdError = ""
            
            // Reading streams. For long-running commands or large outputs,
            // consider asynchronous stream reading or a different approach.
            try {
                // These can block if the output is continuous and doesn't end.
                // For most utility commands, this should be fine.
                stdOutput = stdInputReader.readText()
                stdError = stdErrorReader.readText()
            } finally {
                // Ensure streams are closed even if readText() throws an error (e.g., if interrupted)
                try { stdInputReader.close() } catch (e: IOException) { /* Log or ignore */ }
                try { stdErrorReader.close() } catch (e: IOException) { /* Log or ignore */ }
            }
            
            val exitCode = process.waitFor()

            if (exitCode == 0) {
                Log.i(TAG, "Command executed successfully with strategy $activeStrategy. Exit Code: $exitCode")
                if (stdOutput.isNotBlank()) {
                    Log.i(TAG, "Standard Output:\n$stdOutput")
                }
                // It's possible for commands to output to stderr even on success (e.g., warnings)
                if (stdError.isNotBlank()) {
                    Log.w(TAG, "Standard Error (on success):\n$stdError")
                }
                success = true
            } else {
                Log.e(TAG, "Command failed with strategy $activeStrategy. Exit Code: $exitCode")
                if (stdOutput.isNotBlank()) {
                    Log.e(TAG, "Standard Output (on failure):\n$stdOutput")
                }
                if (stdError.isNotBlank()) {
                    Log.e(TAG, "Standard Error (on failure):\n$stdError")
                }
                success = false
            }

        } catch (e: IOException) {
            Log.e(TAG, "IOException during command execution: ${e.message}", e)
            success = false
        } catch (e: SecurityException) {
            // This can happen if 'su' is not permitted or ProcessBuilder creation is restricted.
            Log.e(TAG, "SecurityException during command execution: ${e.message}", e)
            success = false
        } catch (e: InterruptedException) {
            Log.e(TAG, "Command execution interrupted: ${e.message}", e)
            Thread.currentThread().interrupt() // Restore the interrupted status
            success = false
        } catch (e: Exception) {
            // Catch-all for any other unexpected exceptions
            Log.e(TAG, "Generic exception during command execution: ${e.message}", e)
            success = false
        } finally {
            // Ensure the process is destroyed to free up system resources.
            process?.destroy()
        }
        return@withContext success
    }
}
