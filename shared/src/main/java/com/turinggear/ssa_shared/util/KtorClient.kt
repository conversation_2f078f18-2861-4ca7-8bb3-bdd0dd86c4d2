package com.turinggear.ssa_shared.util

import android.util.Log
import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.util.*
import io.ktor.util.cio.*
import io.ktor.utils.io.*
import kotlinx.serialization.json.Json
import java.io.File

@OptIn(InternalAPI::class)
suspend fun HttpClient.downloadFile(file: File, url: String, callback: suspend (boolean: Boolean) -> Unit) {
    val call = this.request {
        url(url)
        method = HttpMethod.Get
    }
    if (!call.status.isSuccess()) {
        callback(false)
    }
    call.content.copyAndClose(file.writeChannel())
    return callback(true)
}

object KtorClient {
    fun httpClient(withAuth: Boolean = true): HttpClient {
        return HttpClient(OkHttp) {
            expectSuccess = true
            if (withAuth) {
                defaultRequest {
                    url {
                        protocol = URLProtocol.HTTPS
                    }
                    header("MachineNo", SerialNumber.hashedSerial())
                }
            }
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                    encodeDefaults = true // data class有默认值的字段不忽略
                })
            }
            install(Logging) {
                logger = object : Logger {
                    override fun log(message: String) {
                        Log.d("Network Message", "log: $message")
                    }
                }
                level = LogLevel.ALL
            }
            engine {
                config {

                    // 临时禁止证书校验
                    sslSocketFactory(
                        DisableCert.unsafeSslContext.socketFactory,
                        DisableCert.trustAllCertificates[0]
                    )

                    /*
                    // 设置 SSL Pinning
                    val certificatePinner = CertificatePinner.Builder().add(
                        "ssa-api-test.turinggear.com",
                        "sha256/dLCOtJr2Vzw57FZGYcDkHhX+MevALZfOXFiyHJt7QrA="
                    ).build()
                    certificatePinner(certificatePinner)
                    */
                }
            }
        }
    }
}