package com.turinggear.ssa_shared.util

import android.annotation.SuppressLint
import android.os.Build
import com.turinggear.ssa_shared.BuildConfig
import com.turinggear.ssa_shared.toHexString
import org.hashids.Hashids
import java.lang.reflect.Method

@SuppressLint("HardwareIds")
@Suppress("DEPRECATION")
object SerialNumber {

    @SuppressLint("HardwareIds")
    @Suppress("DEPRECATION")
    fun hashedSerial(): String {
        val hexString = originalSerial().toByteArray().toHexString()
        val hashids = Hashids("541f98322eaea41f2b2e3d023972f098", 15)
        return hashids.encodeHex(hexString)
    }

    fun originalSerial(): String {
        var serial: String
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (Build.MODEL == "V2s_STCN") {
                try {
                    val c = Class.forName("android.os.SystemProperties")
                    val get: Method = c.getMethod("get", String::class.java)
                    val sunmiSerial = get.invoke(c, "ro.sunmi.serial") as String?
                    serial = sunmiSerial?: "NOT_SUPPORTED"
                } catch (e: Exception) {
                    e.printStackTrace()
                    serial = "NOT_SUPPORTED"
                }
            } else {
                serial = "NOT_SUPPORTED"
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            serial = "NOT_SUPPORTED"
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            serial = Build.getSerial()
        } else if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N_MR1) {
            serial = Build.SERIAL
        } else {
            serial = "unknown1"
        }

//        if (OSUtils.isEmulator()) {
//            serial = "e.emulator." + Build.SERIAL
//            if (serial.length > 20) {
//                serial = serial.substring(0, 20)
//            }
//        }
        return serial
    }
}