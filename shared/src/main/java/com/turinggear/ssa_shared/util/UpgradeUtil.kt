package com.turinggear.ssa_shared.util

import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import android.widget.Toast
import androidx.core.content.FileProvider
import java.io.File

// TODO: 检查文件是否有效
class UpgradeUtil {
    companion object {
        private fun boardName(): String {
            return when(Build.MODEL) {
                "AIS906-RK3288" -> {
                    "s906"
                }
                "ZC-339" -> {
                    "zc339a"
                }
                "rk3288" -> {
                    "rk3288"
                }
                else -> {
                    "rk3288"
                }
            }
        }

        private fun softwareName(package_name: String): String {
            return when (package_name) {
                "com.turinggear.ssa_car" -> {
                    "car"
                }
                "com.turinggear.ssa_kiosk" -> {
                    "kiosk"
                }
                "com.turinggear.ssa_hand" -> {
                    "handheld"
                }
                "com.turinggear.ssa_console" -> {
                    "console"
                }
                else -> {
                    ""
                }
            }
        }

        fun downloadedFilePath(context: Context, package_name: String): String {
            var download_dir = context.filesDir

            var local_filename = "${softwareName(package_name)}-release.apk"
            var local_path = "$download_dir/$local_filename"

            return local_path
        }

        private fun systemAppFilePath(package_name: String): String {
            if (package_name == "com.turinggear.ssa_console") {
                var console_app_filename = "console-${boardName()}-release.apk"
                return "/system/priv-app/$console_app_filename"
            } else {
                var local_filename = "${softwareName(package_name)}-release.apk"
                return "/system/priv-app/$local_filename"
            }
        }

//        suspend fun downloadAndInstall(context: Context, oss_url: String, package_name: String, as_system_app: Boolean, with_console: Boolean = false): Boolean {
//            // 从 *发布页* 下载App, 注意要有console
//            // 要求执行过 make copy 和 make upload_landing_page_XXX
//            var ret: Unit? = null
//
//            var download_dir = context.filesDir
//
//            val board_name = boardName()
//            val software_name = softwareName(package_name)
//
//            var console_app_filename = "console-${board_name}.apk"
//            var oss_filename = "$software_name.apk"
//            var local_filename = "$software_name-release.apk"
//
//            var local_path = downloadedFilePath(context, package_name)
//
//            var ok = true
//
//            if (as_system_app) {
//                OSUtils.remount()
//            }
//
//            if (as_system_app and with_console) {
//                ret = OSUtils.downloadApp(context, "$oss_url/releases/$console_app_filename", "$download_dir/$console_app_filename", true)
//                if (ret == null) {
//                    return false
//                }
//
//                ok = ok and File("/system/priv-app/$console_app_filename").exists()
//            }
//
//            ret = OSUtils.downloadApp(context, "$oss_url/releases/$oss_filename", local_path, as_system_app)
//            if (ret == null) {
//                return false
//            }
//            if (as_system_app) {
//                ok = ok and File("/system/priv-app/$local_filename").exists()
//            }
//
//            // is install to /system
//            return ok
//        }

        // 默认下载页面的下载地址
        fun defaultApkUrl(oss_url: String, package_name: String): String {
            // remote
            val board_name = boardName()
            val software_name = softwareName(package_name)
            var oss_filename = ""
            if (software_name == "console") {
                oss_filename = "console-${board_name}.apk"
            } else {
                oss_filename = "$software_name.apk"
            }

            val url = "$oss_url/releases/$oss_filename"

            return url
        }

        // 下载到预设路径
        suspend fun download(context: Context, package_name: String, url: String): Boolean {
            // local
            var local_path = downloadedFilePath(context, package_name)

            Log.d("XXX", "downloader: downloading $url -> $local_path")
            val apkFile = File(local_path)
            KtorClient.httpClient().downloadFile(apkFile, url) {
                Log.d("XXX", "downloader: downloaded")
            }

            return File(local_path).exists()
        }

        //  静默安装 需要root
        suspend fun installAsSystemApp(context: Context, package_name: String, removeExisting: Boolean = false): Boolean {
            Log.e("XXX", "installAsSystemApp")
            OSUtils.remount()

            var src = downloadedFilePath(context, package_name)
            val dest = systemAppFilePath(package_name)

            ShellCommander().runAsRoot("cp $src $dest")
            ShellCommander().runAsRoot("chmod 644 $dest")

            return File(dest).exists()
        }

        // 弹出安装界面,需要交互
        fun installInteractively(context: Context, package_name: String): Boolean {
            var filepath = downloadedFilePath(context, package_name)
            val apkFile = File(filepath)

            val uri = FileProvider.getUriForFile(
                context.applicationContext,
                "${context.packageName}.provider",
                apkFile
            )
            val intent = Intent(Intent.ACTION_INSTALL_PACKAGE).apply {
                setDataAndType(uri, "application/vnd.android.package-archive")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            context.startActivity(intent)

            return true
        }

        // 静默安装 无需特殊权限
        // 在lihuboat rk3328会卡死
        // [java - android 7: NullPointerException on APK installing with Runtime.getRuntime().exec - Stack Overflow](https://stackoverflow.com/questions/46036877/android-7-nullpointerexception-on-apk-installing-with-runtime-getruntime-exec)
        fun installByPM(context: Context, package_name: String): Boolean {
            var path = downloadedFilePath(context, package_name)
            val cmd = "pm install -r -i ${context.packageName} $path"
            val p = Runtime.getRuntime().exec(cmd)
            p.waitFor()

            return true
        }

        // 使用PackageInstaller, 对于大屏仍然会OOM, 不再实现
    }
}