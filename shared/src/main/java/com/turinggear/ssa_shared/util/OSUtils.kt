package com.turinggear.ssa_shared.util

import android.Manifest
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.media.AudioManager
import android.os.Build
import android.telephony.SubscriptionManager
import android.util.Log
import android.widget.Toast
import androidx.core.app.ActivityCompat
import java.io.File
import java.net.NetworkInterface
import java.util.*

class OSUtils {
    companion object
    {
        fun isConsoleAppInstalled(ctx: Context): Boolean {
            val mPackageManager = ctx.getPackageManager();
            //获得所有已经安装的包信息
            val infos = mPackageManager.getInstalledPackages(0);
            for (info in infos)
            {
                if(info.packageName == "com.turinggear.ssa_console"){
                    return true;
                }
            }

            return false
        }

        // 找到该设备上需要保活的包,一个设备只会有car或者kiosk其一
        fun findInstalledPackageName(ctx: Context): String? {
            val mPackageManager = ctx.getPackageManager();
            //获得所有已经安装的包信息
            val infos = mPackageManager.getInstalledPackages(0);
            for (info in infos)
            {
                if(arrayOf("com.turinggear.ssa_car", "com.turinggear.ssa_kiosk").contains(info.packageName) ){
                    return info.packageName;
                }
            }

            return null
        }

        fun cmd(cmd: Array<String>) {
            try {
                val process = Runtime.getRuntime()
                    .exec(cmd)
                process.waitFor()
            } catch (e: Exception) {
                Log.e("TAG", e.message, e)
            }
        }

        fun checkRootStatus(): Boolean {
            val paths = arrayOf(
                "/system/app/Superuser.apk", "/sbin/su", "/system/bin/su", "/system/xbin/su",
                "/data/local/xbin/su", "/data/local/bin/su", "/system/sd/xbin/su",
                "/system/bin/failsafe/su", "/data/local/su", "/su/bin/su"
            )
            for (path in paths) {
                if (File(path).exists()) return true
            }
            return false
        }

        suspend fun remount() {
            var mountCommand = when(Build.MODEL) {
                "AIS906-RK3288" -> {
                    "mount -o rw,remount /dev/block/platform/ff0f0000.dwmmc/by-name/system /system"
                }
                "ZC-339" -> {
                    "mount -o rw,remount /dev/block/platform/fe330000.sdhci/by-name/system /system"
                }
                "rk3288" -> {
                    "mount -o rw,remount /dev/block/platform/ff0f0000.dwmmc/by-name/system /system"
                }
                else -> {
                    ""
                }
            }

            if (mountCommand.isNotEmpty()) {
                ShellCommander().runAsRoot(mountCommand)
            }
        }

        // 安装为system app后，自动更新也仍为system app
        fun isSystemApp(ctx: Context, packageName: String = "com.turinggear.ssa_car"): Boolean {
            val mPackageManager = ctx.getPackageManager();
            //获得所有已经安装的包信息
            val infos = mPackageManager.getInstalledPackages(0);
            for (info in infos)
            {
                if(arrayOf(packageName).contains(info.packageName) ){
                    val ai = mPackageManager.getApplicationInfo(info.packageName, 0);
                    if ((ai.flags and ApplicationInfo.FLAG_SYSTEM) != 0) {
                        return true
                    }
                }
            }

            return false
        }

        fun reboot() {
            try {
                Runtime.getRuntime().exec("reboot");
            }
            catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun getIPAddress(useIPv4: Boolean): String? {
            try {
                val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
                for (intf in interfaces) {
                    val addrs = Collections.list(intf.inetAddresses)
                    for (addr in addrs) {
                        if (!addr.isLoopbackAddress) {
                            val sAddr = addr.hostAddress
                            // Check if IPv4 address
                            val isIPv4 = sAddr.indexOf(':') < 0
                            if (useIPv4) {
                                if (isIPv4) return sAddr
                            } else {
                                if (!isIPv4) {
                                    val delim = sAddr.indexOf('%') // drop ip6 zone suffix
                                    return if (delim < 0) sAddr.toUpperCase() else sAddr.substring(0, delim).toUpperCase()
                                }
                            }
                        }
                    }
                }
            } catch (ex: Exception) {
                // Ignore
            }
            return ""
        }

        fun adjustVolume(ctx: Context) {
            try {
                val audioManager: AudioManager  =
                    ctx.getSystemService(Context.AUDIO_SERVICE) as AudioManager;
                audioManager.adjustVolume(AudioManager.ADJUST_RAISE, AudioManager.FLAG_PLAY_SOUND);
            } catch (e: Exception) {
                Log.e("TAG", e.message, e)
            }
        }


        fun getICCID(mContext: Context): String? {
            val sm = SubscriptionManager.from(mContext)

// it returns a list with a SubscriptionInfo instance for each simcard
// there is other methods to retrieve SubscriptionInfos (see [2])
            val sis = if (ActivityCompat.checkSelfPermission(
                    mContext,
                    Manifest.permission.READ_PHONE_STATE
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                // TODO: Consider calling
                //    ActivityCompat#requestPermissions
                // here to request the missing permissions, and then overriding
                //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                //                                          int[] grantResults)
                // to handle the case where the user grants the permission. See the documentation
                // for ActivityCompat#requestPermissions for more details.
                null
            } else {
                sm.activeSubscriptionInfoList
            }

            if (sis == null) {
                return ""
            }

            val si = sis[0]

// getting first SubscriptionInfo
//            val si = sis[0]
            val iccId = si.iccId

            return iccId
        }

        fun clearExoCache(context: Context, name: String = "exo") {
            val cacheDir = File(context.cacheDir, name)
            if (cacheDir.isDirectory) {
                cacheDir.deleteRecursively()
            }
        }

        fun isEmulator(): Boolean {
            val buildProperties = listOf(
                Build.FINGERPRINT.startsWith("generic"),
                Build.FINGERPRINT.startsWith("unknown"),
                Build.MODEL.contains("google_sdk"),
                Build.MODEL.contains("Emulator"),
                Build.MODEL.contains("Android SDK built for x86"),
                Build.MANUFACTURER.contains("Genymotion"),
                Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"),
                "google_sdk" == Build.PRODUCT
            )

            val propertyChecks = listOf(
                System.getProperty("ro.kernel.qemu") == "1",
                System.getProperty("ro.product.device") == "generic",
                System.getProperty("ro.product.model") == "sdk",
                System.getProperty("ro.product.brand") == "generic"
            )

            return buildProperties.any { it } || propertyChecks.any { it }
        }
    }
}
