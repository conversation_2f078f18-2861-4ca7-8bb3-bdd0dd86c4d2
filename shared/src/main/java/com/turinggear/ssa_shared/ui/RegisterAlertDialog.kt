package com.turinggear.ssa_shared.ui

import androidx.compose.foundation.layout.padding
import androidx.compose.material.AlertDialog
import androidx.compose.material.Button
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import kotlin.system.exitProcess

@Composable
fun RegisterAlertDialog(modifier: Modifier = Modifier, code: String = "", error: String? = null) {
    var title = "错误"
    var text = error ?: ""
    if (code.isNotEmpty()) {
        title = "识别码：$code"
        text = "\n请将识别码告诉工作人员，后台激活权限。\n\n激活后，请手动重启应用。"
    }
    AlertDialog(
        onDismissRequest = { },
        title = {
            Text(title)
        },
        text = {
            Text(text)
        },
        confirmButton = {
            Button(
                onClick = {
                    exitProcess(-1)
                },
                modifier = Modifier
                    .padding(vertical = 14.dp)
                    .padding(horizontal = 12.dp)
            ) {
                Text("重启", style = MaterialTheme.typography.h6)
            }
        },
        modifier = modifier
    )
}