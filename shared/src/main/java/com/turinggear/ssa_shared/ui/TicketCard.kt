package com.turinggear.ssa_shared.ui

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.turinggear.ssa_shared.model.Good

@Composable
fun TicketCard(onClick: () -> Unit, modifier: Modifier = Modifier, good: Good? = null) {
    Column(
        modifier = modifier
            .fillMaxHeight()
            .clickable(onClick = onClick),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        good?.let {
            Box(Modifier.weight(1f)) {}
            Box(
                modifier = Modifier
                    .weight(6f)
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp),
                contentAlignment = Alignment.Center
            ) {
                val title = good.name + "\n" + "(${good.price}元)"
                Text(
                    text = title,
                    color = MaterialTheme.colors.onBackground.copy(0.9f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.caption
                )
            }
            Box(
                modifier = Modifier
                    .weight(4f)
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp)
//                    .padding(top = 4.dp)
                    .padding(bottom = 4.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "点击购票",
                    modifier = Modifier
                        .border(
                            width = 0.5.dp,
                            color = MaterialTheme.colors.onSecondary.copy(0.2f),
                            shape = RoundedCornerShape(4.dp)
                        )
                        .padding(vertical = 5.dp)
                        .padding(horizontal = 8.dp),
                    color = MaterialTheme.colors.onSecondary,
                    style = MaterialTheme.typography.overline.copy(fontWeight = FontWeight.Bold),
                    fontSize = 12.sp
                )
            }
        }
    }
}
