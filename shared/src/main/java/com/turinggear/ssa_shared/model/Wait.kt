package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class ResponseWait(
    val code: Int,
    val msg: String,
    val data: List<Wait>? = null
)

@Serializable
data class Wait(
    val id: Int,
    val sort_order: Int,
    val name: String,
    val waiting: Int
)

@Serializable
data class ResponseWaitDirection(
    val code: Int,
    val msg: String,
    val data: JsonElement
)