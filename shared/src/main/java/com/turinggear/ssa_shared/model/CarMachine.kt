package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class CarMachineResponse (
    val code: Int,
    val msg: CarMachine? = null
)

@Serializable
data class TicketsConsumedStats (
    val today: Int,
    val week: Int,
    val month: Int,
)

@Serializable
data class DriverDetail(
    val id: Int,
    val name: String?,
    val phone: String?,
    val real_name: String?,
    val status: Int,
)

@Serializable
data class CarMachine (
    val id: Int,
    val serial: String?,
    val vehicle_serial: String?,
    val phone: String?,
    val route_sn: String?,
    val type: Int,
    val status: Int,
    val version_code: Int?,
    val note: String?,
    val route_id: Int?,
    val driver_id: Int?,
    val driver: DriverDetail?,
    val is_test: Int?,
    // optional
    val station_id: Int? = null,
    val check_ticket_count: TicketsConsumedStats? = null
)