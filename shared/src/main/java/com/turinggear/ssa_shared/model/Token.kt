package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class ResponseToken(
    val code: Int,
    val msg: String,
    val data: Token
)

@Serializable
data class User(
    val real_name: String,
    val name: String,
    val permissions: List<String>?
)

@Serializable
data class Token(
    var access_token: String,
    val token_type: String,
    val expires_in: Int,
    val user: User? = null
)