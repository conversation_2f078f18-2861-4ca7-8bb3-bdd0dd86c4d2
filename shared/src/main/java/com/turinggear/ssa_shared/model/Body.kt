package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class BodyForRegister(
    val serial: String,
    val type: Int,
    val random: String
)

@Serializable
data class BodyForRefund(
    val order: String,
    val refunded: String
)

@Serializable
data class BodyForOrderMake(
    val pay_platform: Int,
    val num: Int
)

@Serializable
data class BodyForOrderPay(
    val order: String,
    val code: String?
)