package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Serializable
data class Poll(
    val code: Int,
    val msg: String,
    val data: PollData? = null
)

@Serializable
data class PollData(
    val machineInfo: MachineInfo? = null,
    val stationInfo: StationInfo? = null,
    val machineStaticConfig: MachineStaticConfig? = null,
    val scenicAreaConfig: ScenicAreaConfig? = null,
    val appVersion: AppVersion?=null
)

@Serializable
data class StationInfo(
    val id: Int? = null,
    val name: String? = null,
    val status: Int? = null,
    val screen_saver_transverse: String? = null,
    val screen_saver_vertical: String? = null,
    val ticket_rules: String? = null
)

fun Poll.isClosingTime(): Boolean {
    var isClosingTime = false
    this.data?.scenicAreaConfig?.sell_ticket_time?.let {
        val now = LocalDateTime.now()
        val array = it.split("-")
        val dateString = DateTimeFormatter.ofPattern("yyyy-MM-dd ").format(now)
        val startString = dateString + array.first()
        val endString = dateString + array.last()
        val pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val startDate = LocalDateTime.parse(startString, pattern)
        val endDate = LocalDateTime.parse(endString, pattern)
        isClosingTime = now.isBefore(startDate) or now.isAfter(endDate)
    }
    this.data?.scenicAreaConfig?.stop_ticket_times?.let { stop_ticket_times ->
        stop_ticket_times.forEach {
            val now = LocalDateTime.now()
            val array = it.split("-")
            val dateString = DateTimeFormatter.ofPattern("yyyy-MM-dd ").format(now)
            val startString = dateString + array.first()
            val endString = dateString + array.last()
            val pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            val startDate = LocalDateTime.parse(startString, pattern)
            val endDate = LocalDateTime.parse(endString, pattern)
            val isBreakingTime = now.isAfter(startDate) and now.isBefore(endDate)
            if (isBreakingTime) {
                isClosingTime = isBreakingTime
            }
        }
    }
    return isClosingTime
}
