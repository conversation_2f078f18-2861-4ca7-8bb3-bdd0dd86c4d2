package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class ResponseStation(
    val code: Int,
    val msg: String,
    val data: List<ResponseStationData>
)

@Serializable
data class ResponseStationsForOneRoute(
    val code: Int,
    val msg: String,
    val data: List<Station>
)

@Serializable
data class ResponseStationData(
    val id: Int,
    val name: String,
    val route_image: String?,
    val stations: List<Station>
)

@Serializable
data class Station(
    val id: Int,
    val name: String,
    val status: Int,
    val route_sort_order: Int
)