package com.turinggear.ssa_car.model

import kotlinx.serialization.Serializable

@Serializable
data class LocationEvent (
    val event_type: Int = 20,
    // 目前每次上报版本号
    val version_code: Int,

    val gps_status: Int,
    val speed: String = "",
    val longitude: String = "",
    val latitude: String = "",
    val altitude: String = "",
    val velocity: String = "",
    val acceleration: String = "",
    val bearing: String = "",
    val satellites_used: Int = 0,
    val satellites_count: Int = 0,
    // gps 获取的原始时间
    val gps_time: Long = 0,
    val note: String = "",
)

@Serializable
data class TicketConsumptionEvent (
    val event_type: Int = 21,
    val qrcode: String,
    val result: Int,
    val msg: String = "",
)
