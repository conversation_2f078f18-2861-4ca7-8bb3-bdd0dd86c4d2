package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class Login(
    val code: Int,
    val msg: String,
    val data: LoginData? = null
)

@Serializable
data class LoginData(
    val id: Int? = null,
    val name: String? = null,
    val phone: String? = null,
    val real_name: String? = null,
    val role: Int? = null,
    val created_at: String? = null
)