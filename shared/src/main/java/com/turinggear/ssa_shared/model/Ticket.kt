package com.turinggear.ssa_shared.model

import androidx.compose.ui.graphics.Color
import kotlinx.serialization.Serializable

// 无限次
val UNLIMITED_MAX_USES = 999
// 纸质票
val UNCERTAIN_MAX_USES = 99999

@Serializable
data class ResponseTicketList(
    val code: Int,
    val msg: String,
    val data: List<Ticket>? = null
)

@Serializable
data class ResponseTicket(
    val code: Int,
    val msg: String,
    val data: Ticket? = null
)

@Serializable
data class Ticket(
    val id: Int,
    val ticket_no: String,
    val use: Int,
    val use_max: Int,
    var status: Int,
    val created_at: String,
    val goods: TicketGoods? = null,
    val order: TicketOrder? = null,
    val machine: String? = null,
    val station: String? = null,
)

@Serializable
data class TicketGoods(
    val id: Int,
    val category: Int,
    val name: String,
    val description: String,
    val price: String,
    val status: Int,
    val metadata: TicketGoodMetaData? = null
)

@Serializable
data class TicketGoodMetaData (
    val route_id: String,

    val seats: Int? = null,
    val boat_type: String? = null,
)

@Serializable
data class TicketOrder(
    val id: Int,
    val order_no: String,
    val total: String,
    val refunded: String,
    val status: Int,
    val financial_status: Int,
    val ticket_status: Int,
    val pay_platform: Int? = null
)

fun ticketStatusStringFrom(ticket: Ticket): String {
    val x = "${ticket.use_max-ticket.use}/${ticket.use_max}"
    if (ticket.use_max == UNCERTAIN_MAX_USES) {
        return "可用"
    }

    return when (ticket.status) {
        10 -> "可用（$x）"
        20 -> "核销完毕（$x）"
        30 -> "已退款"
        40 -> "已过期"
        else -> "未知"
    }
}

fun ticketStatusColorFrom(ticket: Ticket): Color {
    return when (ticket.status) {
        10 -> Color(0xFF53A949)
        else -> Color.Red
    }
}