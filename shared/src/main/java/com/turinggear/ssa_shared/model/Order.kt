package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class ResponseOrder(
    val code: Int,
    val msg: String,
    val data: Order
)

@Serializable
data class Order(
    val id: Int,
    val order_no: String,
    val subtotal: String,
    val total: String,
    val status: Int,
    var financial_status: Int,
    val ticket_status: Int,
    val created_at: String
)