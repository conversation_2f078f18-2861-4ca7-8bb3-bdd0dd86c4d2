package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class Startup(
    val code: Int,
    val msg: String,
    val data: StartupData? = null
)

@Serializable
data class StartupData(
    val machineInfo: MachineInfo? = null,
    val machineStaticConfig: MachineStaticConfig? = null,
    val scenicAreaConfig: ScenicAreaConfig? = null,
    val appVersion: AppVersion?=null
)

@Serializable
data class MachineInfo(
    val id: Int? = null,
    val route_id: Int? = null,
    val serial: String? = null,
    val vehicle_serial: String? = null,
    val type: Int? = null,
    val status: Int? = null,
    val station_name: String? = null,
    val station_id: Int? = null,
    // car 事件上报配置
    val report_location_enabled: Int? = null,
    val report_location_interval: Long? = null,
    val report_photo_enabled: Int? = null,
    val report_photo_interval: Long? = null
)

@Serializable
data class MachineStaticConfig(
    // 估计等待时间,示例10分钟 "00:10:00"
    val eta: String? = null,
    val screen_title: String? = null,
    val screen_subtitle: String? = null,
    // 乘客须知
    val screen_passenger_rules: String? = null,
    // 购票须知
    val ticket_rules: String? = null,
    val map1_url: String? = null,
    val map2_url: String? = null,
    val map3_url: String? = null,
    val map4_url: String? = null,
    val video3_url: String? = null,
    val video4_url: String? = null,
    val car_title: String? = null,
    val mobile_title: String? = null,

    val return_boat_url: String? = null,
)

@Serializable
data class ScenicAreaConfig(
//    val name: String? = null,
//    val desc: String? = null,
    val message: String? = null,
    // 运营时间 示例: 06:00:00-18:00:00
    val operate_time: String? = null,
    // 售票时间 示例: 05:00:00-15:00:00
    val sell_ticket_time: String? = null,
    val stop_ticket_times: List<String>? = null,
//    val date: String? = null,
//    val use_begin: String? = null,
//    val updated_at: String? = null,
//    val created_at: String? = null,
//    val id: Int? = null

    // 隐藏值
    val app_reboot_delay: Int? = null,
)
@Serializable
data class AppVersion (
    val id: Int,
    val version: String,
    val code: Int,
    val down_url: String,
    // release note
    val desc: String,
    // app 类型
    val type: Int,
    val status: Int
)
