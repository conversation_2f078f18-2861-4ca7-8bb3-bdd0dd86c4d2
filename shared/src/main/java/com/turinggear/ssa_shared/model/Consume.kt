package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class Consume(
    val code: Int,
    val msg: String,
    val data: ConsumeData? = null
)

@Serializable
data class ConsumeData(
    val id: Int,
    val ticket_no: String,
    val use: Int,
    val use_max: Int,
    val status: Int,
    val start_date: String,
    val end_date: String,
    val created_at: String,
    val goods: ConsumeDataGoods,
    val order: ConsumeDataOrder
)

@Serializable
data class ConsumeDataGoods(
    val name: String,
    val description: String,
    val price: String,
    val status: Int
)

@Serializable
data class ConsumeDataOrder(
    val id: Int,
    val order_no: String,
    val total: String,
    val refunded: String,
    val status: Int,
    val financial_status: Int,
    val ticket_status: Int
)