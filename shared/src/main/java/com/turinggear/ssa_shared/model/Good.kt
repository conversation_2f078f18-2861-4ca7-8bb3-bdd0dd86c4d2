package com.turinggear.ssa_shared.model

import kotlinx.serialization.Serializable

@Serializable
data class ResponseGoods(
    val code: Int,
    val msg: String,
    val data: ResponseGoodsData
)

@Serializable
data class ResponseGood(
    val code: Int,
    val msg: String,
    val data: Good
)

@Serializable
data class ResponseGoodsData(
    val data: List<Good>
)

@Serializable
data class Good(
    val id: Int,
    val category: Int,
    val name: String,
    val price: String,
    val sort_order: Int,
    val status: Int,
    val metadata: TicketGoodMetaData?
)
