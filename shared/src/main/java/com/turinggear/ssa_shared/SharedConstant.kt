package com.turinggear.ssa_shared

var API_BASE = "" // 在各module里赋值

// 注意: url尾部不要带/

fun apiHealthCheck()          : String { return "$API_BASE/api/health" }
fun apiRegisterMachine()      : String { return "$API_BASE/api/register_machine" }
// api for ticket machine
fun apiTicketLogin()          : String { return "$API_BASE/ticket/v1/auth/login" }
fun apiTicketGoods()          : String { return "$API_BASE/ticket/v1/goods" }
fun apiTicketOrders()         : String { return "$API_BASE/ticket/v1/orders" }
fun apiTicketOrdersPay()      : String { return "$API_BASE/ticket/v1/orders/pay" }
fun apiTicketTickets(orderNo: String)        : String { return "$API_BASE/ticket/v1/orders/$orderNo/tickets" }
fun apiTicketTicketDetail()        : String { return "$API_BASE/ticket/v1/machines/ticket" }
fun apiTicketTicketsForNationId()        : String { return "$API_BASE/ticket/v1/machines/tickets_by_tourist_id" }
fun apiTicketPassengerGroups()        : String { return "$API_BASE/ticket/v1/passenger_groups" }
fun apiTicketResentTickets()  : String { return "$API_BASE/ticket/v1/machines/tickets_send_notice" }
fun apiTicketRoutes()         : String { return "$API_BASE/ticket/v1/routes" }
fun apiTicketRoutesForStation(stationId: Int)         : String { return "$API_BASE/ticket/v1/stations/$stationId/routes" }
fun apiTicketConsume()        : String { return "$API_BASE/ticket/v1/machines/consume" }
fun apiTicketRefund()         : String { return "$API_BASE/ticket/v1/machines/refund" }
fun apiTicketRefundMoney()    : String { return "$API_BASE/ticket/v1/machines/ticket_refund_money" }
fun apiTicketCall()           : String { return "$API_BASE/ticket/v1/machines/call" }
fun apiTicketDirectionRecord(): String { return "$API_BASE/ticket/v1/machines/direction_record" }
fun apiTicketReportEvents()   : String { return "$API_BASE/ticket/v1/machines/report_events" }
fun apiTicketStartup()        : String { return "$API_BASE/ticket/v1/machines/startup" }
fun apiTicketPoll()           : String { return "$API_BASE/ticket/v1/machines/poll" }
// api for porpoise
fun apiPorpoiseBoatOrderCreate()        : String { return "$API_BASE/ticket/v1/porpoise_boat/order_create" }
fun apiPorpoiseBoatOrderUpdate()        : String { return "$API_BASE/ticket/v1/porpoise_boat/order_update" }
// api for car
fun apiCarMe()                : String { return "$API_BASE/car/v1/machines/me" }
fun apiCarConsume()           : String { return "$API_BASE/car/v1/machines/consume" }
fun apiCarWaiting()           : String { return "$API_BASE/car/rc/stations/waiting" }
fun apiCarLogin()             : String { return "$API_BASE/car/v1/machines/login" }
fun apiCarLogout()            : String { return "$API_BASE/car/v1/machines/logout" }
fun apiCarStartup()           : String { return "$API_BASE/car/v1/machines/startup" }
fun apiCarReportEvents()           : String { return "$API_BASE/car/v1/machines/report_events" }
fun apiCarRoutes()            : String { return "$API_BASE/car/v1/routes" }
fun apiCarRoutesChange()      : String { return "$API_BASE/car/v1/routes/change_bind" }
fun apiCarWaitingDirection()  : String { return "$API_BASE/car/v1/routes/waiting_direction" }

enum class PAYMENTSTATE {
    UNSTARTED, USER_TAPPED, MAKING_ORDER, USERPAYING, SUCCEED, FAILED
}

enum class SCANTYPE {
    NONE, CALL_CAR, CHECK_TICKET, WECHAT_PAY, REPRINT_TICKET
}

val CATEGORY_BUS = 10
val CATEGORY_TRAIN = 20

val GOOD_CATEGORY_MAP = mapOf(
    10 to "电瓶车",
    20 to "小火车",
    30 to "无人游船",
    40 to "动力帆船",
    )
val PAY_PLATFORM_MAP = mapOf(
    10 to "微信",
    20 to "支付宝",
    30 to "现金",
    40 to "微信*",
    50 to "换票",
    60 to "合作方付款",
)
