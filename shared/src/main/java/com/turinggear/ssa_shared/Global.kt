package com.turinggear.ssa_shared

import android.content.Context
import androidx.activity.ComponentActivity
import androidx.compose.material.ScaffoldState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.turinggear.ssa_car.model.LocationEvent
import com.turinggear.ssa_shared.model.*
import kotlinx.serialization.json.Json

// 全局变量
var BARCODE by mutableStateOf("")
var CURRENT_STATION_NAME by mutableStateOf("")
var CURRENT_STATION_ID: Int? by mutableStateOf(null)
var SHOW_POPUP by mutableStateOf(false)
var SHOW_REPRINT by mutableStateOf(false)
var SHOW_DEBUGVIEW by mutableStateOf(false)
var SELECTED_GOOD: Good? by mutableStateOf(null)
var SELECTED_AMOUNT by mutableStateOf(1)
var SELECTED_DIRECTION_STATION_ID: String? by mutableStateOf(null)
var FIRST_STATION_FOR_SELECTED_GOOD: Station? by mutableStateOf(null)
var LAST_STATION_FOR_SELECTED_GOOD: Station? by mutableStateOf(null)
var FIRST_STATION_FOR_CALL_CAR: Station? by mutableStateOf(null)
var LAST_STATION_FOR_CALL_CAR: Station? by mutableStateOf(null)
var SCANNED_TICKET_ID_CALL_CAR: String? by mutableStateOf(null)

// [打印机状态] 24：正常，56：缺纸，16：有纸但异常，48：缺纸又异常，-1：未连接
var PRINTER_1_STATUS by mutableStateOf(-1)
var PRINTER_1_STATUS_STRING by mutableStateOf("")
var PRINTER_2_STATUS by mutableStateOf(-1)
var PRINTER_2_STATUS_STRING by mutableStateOf("")

var RANDOM_REGISTER_CODE by mutableStateOf("")
var SHOW_ALERT_FOR_REGISTER by mutableStateOf(false)
var REGISTER_ERROR_STRING: String? by mutableStateOf(null)
var STARTUP: Startup? by mutableStateOf(null)
var CURRENT_CAR: CarMachine? by mutableStateOf(null) // car polling
var CURRENT_LOCATION: LocationEvent? by mutableStateOf(null)
var GLOBAL_POLL: Poll? by mutableStateOf(null)
var SCAFFOLD_STATE: ScaffoldState? by mutableStateOf(null)
var SCAN_TYPE by mutableStateOf(SCANTYPE.NONE)
var PAYMENT_STATE by mutableStateOf(PAYMENTSTATE.UNSTARTED)
var TheMainActivity: ComponentActivity? = null
var ApplicationContext: Context? = null
var ColorPaletteIndex by mutableStateOf(0)
var HasTriedToRegisterAfterLaunched = false
var IsPortrait by mutableStateOf(false)
var IsNetworkConnected by mutableStateOf(false)
var GlobalGpsStatus by mutableStateOf(10)
var LocalNetworkConnected: Boolean? by mutableStateOf(null)
var NetworkConnectedCount by mutableStateOf(0)
var StatusCodeFromHealthApi: Int? by mutableStateOf(null)
var StatusCodeFromBaidu: Int? by mutableStateOf(null)
var IsClosingTime by mutableStateOf(false)
var ReprintTickets: List<Ticket>? by mutableStateOf(null)
var TicketsForExchange: List<Ticket>? by mutableStateOf(null)
var SHOW_DIALOG_FOR_LOGIN by mutableStateOf(false)
var SHOW_DIALOG_FOR_ROUTES by mutableStateOf(false)
var SELECTED_ROUTE_ID: Int? by mutableStateOf(null)
var SELECTED_ROUTE_NAME: String? by mutableStateOf(null)
var RESPONSE_CONSUME: Consume? by mutableStateOf(null)
var DRIVER_LOGIN_PHONE by mutableStateOf("")
var DRIVER_LOGIN_NAME by mutableStateOf("")
var DRIVER_LOGIN_ID: Int? by mutableStateOf(null)
var ACCESS_TOKEN by mutableStateOf("")
var CURRENT_USER: User? by mutableStateOf(null)
var SHOW_REFUND by mutableStateOf(false)
var SCANNED_GOOD: Good? by mutableStateOf(null)
var SCANNED_TICKET: Ticket? by mutableStateOf(null)
var IsLoadingConsuming by mutableStateOf(false)
var CurrentVideoUrlForScreensaver by mutableStateOf("")
var CurrentBatchTotalSeats by mutableStateOf(0)

val JsonFormat = Json {
    ignoreUnknownKeys = true
    isLenient = true
}
fun ByteArray.toHexString(): String {
    return this.joinToString("") {
        java.lang.String.format("%02x", it)
    }
}
