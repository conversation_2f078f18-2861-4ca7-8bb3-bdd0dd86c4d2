pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
//        jcenter() {
//            content {
//                includeModule("com.danikula", "videocache")
//            }
//        }
    }
}
rootProject.name = "ssa"
include ':kiosk'
include ':car'
include ':handheld'
include ':console'
include ':shared'
include ':marina'
