.PHONY: shiyuan yqh test main jiuzhouwa

clean:
	rm -rf apks
	./gradlew clean

# 构建apk
shiyuan yqh waterpark main jiuzhouwa cssh shg hdboat csshboat lihuboat fhxdboat: clean
	make -C kiosk $@ copy
	make -C handheld $@ copy
	make -C console $@ copy

	# 无人船
	@if [ "$@" != "hdboat" ] && [ "$@" != "csshboat" ] && [ "$@" != "lihuboat" ] && [ "$@" != "fhxdboat" ]; then make -C car $@ copy; fi

# upload_* 上传apks目录包到下载页面
upload_%:
	@-make --no-print-directory -C car $@
	@make --no-print-directory -C kiosk $@
	@make --no-print-directory -C handheld $@
	@make --no-print-directory -C console $@
