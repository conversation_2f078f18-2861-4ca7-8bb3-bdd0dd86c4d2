plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlinx-serialization'
}

apply plugin: 'com.github.alexfu.androidautoversion'

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.turinggear.ssa_car"
        minSdk 25
        targetSdk 33
        versionName androidAutoVersion.versionName
        versionCode androidAutoVersion.versionCode

        setProperty("archivesBaseName", "car-$versionName-b$versionCode")

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion compose_version
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    signingConfigs {
        release {
            storeFile file('s906.jks')
            storePassword '12345678'
            keyAlias = 'key0'
            keyPassword '12345678'
        }
        debug {
            storeFile file('s906.jks')
            storePassword '12345678'
            keyAlias = 'key0'
            keyPassword '12345678'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    flavorDimensions += "version"
    productFlavors {
        yqh {
            dimension = "version"
            resValue "string", "app_name", "雁栖湖/车载"
        }
        shiyuan {
            dimension = "version"
            resValue "string", "app_name", "世园/车载"
        }
        waterpark {
            dimension = "version"
            resValue "string", "app_name", "水上公园/车载"
        }
        jiuzhouwa {
            dimension = "version"
            resValue "string", "app_name", "九州洼/车载"
        }
        cssh {
            dimension = "version"
            resValue "string", "app_name", "尚湖/车载"
        }
        main {
            dimension = "version"
            resValue "string", "app_name", "测试/车载"
        }

        shg {
            dimension = "version"
            resValue "string", "app_name", "山海关/车载"
    
            }
        // add flavor here
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.9.0'
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.material:material:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.1'
    implementation 'androidx.activity:activity-compose:1.5.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"

    implementation 'androidx.compose.material:material-icons-extended:1.1.1'
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.5.1"
    implementation "io.coil-kt:coil-compose:2.0.0"
    implementation 'org.hashids:hashids:1.0.3'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'

    def ktor_version = '2.0.3'
    implementation "io.ktor:ktor-client-okhttp:$ktor_version"
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation "io.ktor:ktor-client-logging-jvm:$ktor_version"

    implementation 'io.sentry:sentry-android:5.7.3'
    implementation project(path: ':shared')
}
