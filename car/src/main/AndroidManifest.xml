<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.turinggear.ssa_car"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />


    <uses-permission android:name="android.permission.ACCESS_SUPERUSER"/>
    <uses-permission android:name="android.permission.FACTORY_TEST"/>
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES"/>


    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Carssa">
        <receiver
            android:name=".GpsDeviceStatusReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.turinggear.ssa_car.bc.GPS_DEVICE_STATUS" />
            </intent-filter>

        </receiver>

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/Theme.Carssa">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />

                <!-- launcher app -->
                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name=".service.ScheduleService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name=".service.PollingService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name=".service.GuardService"
            android:exported="true"
            android:process=":remote">
            <intent-filter>
                <action
                    android:name="com.turinggear.ssa_car.service.GuardService"
                    android:exported="true" />
            </intent-filter>
        </service> <!-- download package -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="io.sentry.dsn"
            android:value="https://<EMAIL>/6352080" />
    </application>

</manifest>