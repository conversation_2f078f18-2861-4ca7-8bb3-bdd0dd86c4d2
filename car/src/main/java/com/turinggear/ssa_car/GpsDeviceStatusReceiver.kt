package com.turinggear.ssa_car

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.turinggear.ssa_shared.GlobalGpsStatus

class GpsDeviceStatusReceiver : BroadcastReceiver() {
    private val TAGS: String = "GpsDeviceStatusReceiver"

    override fun onReceive(context: Context, intent: Intent) {
        val status = intent.getIntExtra("status", 10)
//        Log.e(TAGS, "bc ${status}")
        GlobalGpsStatus = status
    }
}