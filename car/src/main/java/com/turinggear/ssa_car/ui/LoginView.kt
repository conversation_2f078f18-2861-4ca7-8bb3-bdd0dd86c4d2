package com.turinggear.ssa_car.ui

import android.content.Context
import android.view.Gravity
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_car.*
import com.turinggear.ssa_car.ui.theme.CarssaTheme
import com.turinggear.ssa_car.viewmodel.LoginViewModel
import com.turinggear.ssa_shared.DRIVER_LOGIN_ID
import com.turinggear.ssa_shared.DRIVER_LOGIN_NAME
import com.turinggear.ssa_shared.DRIVER_LOGIN_PHONE
import com.turinggear.ssa_shared.SHOW_DIALOG_FOR_LOGIN
import kotlinx.coroutines.launch

@Composable
fun LoginView(vm: LoginViewModel = viewModel()) {
    var phone by rememberSaveable { mutableStateOf("") }
    val focusManager = LocalFocusManager.current
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    Card(Modifier.clip(RoundedCornerShape(20.dp))) {
        Column(
            Modifier
                .fillMaxWidth(fraction = 0.9f)
                .fillMaxHeight(fraction = 0.6f),
            verticalArrangement = Arrangement.spacedBy(
                55.dp,
                alignment = Alignment.CenterVertically
            ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            OutlinedTextField(
                value = phone,
                onValueChange = { phone = it },
                modifier = Modifier
                    .width(320.dp)
                    .height(86.dp),
                textStyle = MaterialTheme.typography.h4,
                keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                label = {
                    Text("手机号", style = MaterialTheme.typography.h5)
                }
            )
            OutlinedButton(
                onClick = {
                    focusManager.clearFocus()
                    scope.launch {
                        val result = vm.loginWith(phone)
                        Toast.makeText(
                            context,
                            result,
                            Toast.LENGTH_SHORT
                        ).apply{
                            setGravity(Gravity.CENTER, 0, 0)
                        }.show()
                        if (result == "签到成功") {
                            SHOW_DIALOG_FOR_LOGIN = false
                            val sharedPref = context.getSharedPreferences(
                                BuildConfig.APPLICATION_ID,
                                Context.MODE_PRIVATE
                            )
                            val edit = sharedPref.edit()
                            edit.putString("DRIVER_LOGIN_PHONE", DRIVER_LOGIN_PHONE)
                            edit.putString("DRIVER_LOGIN_NAME", DRIVER_LOGIN_NAME)
                            DRIVER_LOGIN_ID?.let {
                                edit.putInt("DRIVER_LOGIN_ID", it)
                            }
                            edit.apply()
                        }
                    }
                },
                modifier = Modifier
                    .width(320.dp)
                    .height(86.dp)
            ) {
                Text(
                    text = "签到",
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 6.dp),
                    style = MaterialTheme.typography.h4
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LoginPreview() {
    CarssaTheme {
        LoginView()
    }
}