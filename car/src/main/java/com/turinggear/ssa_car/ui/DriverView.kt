package com.turinggear.ssa_car.ui

import android.annotation.SuppressLint
import android.content.Context
import android.media.MediaPlayer
import android.view.Gravity
import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_car.BuildConfig
import com.turinggear.ssa_car.util.RequestManager
import com.turinggear.ssa_car.viewmodel.MediaPlayerViewModel
import com.turinggear.ssa_car.viewmodel.RoutesViewModel
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.R
import kotlinx.coroutines.launch

fun clearLoginedUserInPref(context: Context): Boolean {
    val sharedPref = context.getSharedPreferences(
        BuildConfig.APPLICATION_ID,
        Context.MODE_PRIVATE
    )

    val edit = sharedPref.edit()
    edit.putString("DRIVER_LOGIN_PHONE", "")
    edit.putString("DRIVER_LOGIN_NAME", "")
    edit.putInt("DRIVER_LOGIN_ID", 0)

    return true
}

@SuppressLint("CommitPrefEdits")
@Composable
fun DriverView() {
    val context = LocalContext.current
    val mediaPlayerViewModel: MediaPlayerViewModel = viewModel()

    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 20.dp)
    ) {
        val carNumber = STARTUP?.data?.machineInfo?.vehicle_serial ?: ""
        var textColor = MaterialTheme.colors.onSurface.copy(0.2f)
        if (DRIVER_LOGIN_PHONE.isNotEmpty()) {
            textColor = MaterialTheme.colors.onSurface
        }
        val routeId = STARTUP?.data?.machineInfo?.route_id
        if (SELECTED_ROUTE_NAME.isNullOrEmpty()) {
            routeId.let { theRouteId ->
                val vm: RoutesViewModel = viewModel()
                val routesFlow = vm.routesFlow.collectAsState()
                val routes = routesFlow.value ?: listOf()
                routes.forEach {
                    if (it.id == theRouteId) {
                        SELECTED_ROUTE_ID = it.id
                        SELECTED_ROUTE_NAME = it.name
                    }
                }
            }
        }

        val enableWelcomeSound = context.resources.getBoolean(R.bool.enable_welcome_sound)
        Column(
            modifier = Modifier
                .padding(horizontal = 10.dp)
                .padding(vertical = 30.dp)
                .fillMaxHeight()
                .fillMaxWidth(fraction = 0.6f)
                .clickable(enabled = enableWelcomeSound) {
                    val rid = context.resources.getIdentifier(
                        "welcome",
                        "raw",
                        context.packageName
                    )
                    if (rid != 0) {
                        mediaPlayerViewModel.play(context, rid)
                    }
                },
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            val routeTitle = SELECTED_ROUTE_NAME ?: "(请选择线路)"
            Text(text = "车辆编号：$carNumber", color = textColor, style = MaterialTheme.typography.h5)
            Text(text = "线路名称：$routeTitle", color = textColor, style = MaterialTheme.typography.h5)

            if (DRIVER_LOGIN_NAME == "") {
                Text(text = "司机姓名：未登录", color = Color.Red, style = MaterialTheme.typography.h5)
                clearLoginedUserInPref(context)
            } else {
                Text(text = "司机姓名：$DRIVER_LOGIN_NAME", color = textColor, style = MaterialTheme.typography.h5)
            }

            val showDriverPerformance = context.resources.getBoolean(R.bool.show_driver_performance)
            if (showDriverPerformance && CURRENT_CAR?.driver != null && CURRENT_CAR?.check_ticket_count != null) {
                Text(text = "验票次数:", color = textColor, style = MaterialTheme.typography.h5)
                val size = 24.sp
                val text = buildAnnotatedString {
                    withStyle(style = SpanStyle(fontSize = size)) {
                        append("今日 ")
                    }
                    withStyle(style = SpanStyle( color = Color.Red, fontSize = size)) {
                        append(CURRENT_CAR?.check_ticket_count!!.today.toString())
                    }
                    withStyle(style = SpanStyle(fontSize = size)) {
                        append(" 本周 ")
                    }
                    withStyle(style = SpanStyle( color = Color.Red, fontSize = size)) {
                        append(CURRENT_CAR?.check_ticket_count!!.week.toString())
                    }
                    withStyle(style = SpanStyle(fontSize = size)) {
                        append(" 本月 ")
                    }
                    withStyle(style = SpanStyle( color = Color.Red, fontSize = size)) {
                        append(CURRENT_CAR?.check_ticket_count!!.month.toString())
                    }
                }
                Text(text = text)
            }

            if (enableWelcomeSound) {
                Text("点击本区域播放提醒语音")
            }
        }
        Divider(
            thickness = 0.5.dp,
            modifier = Modifier
                .fillMaxHeight()
                .width(0.5.dp)
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            OutlinedButton(
                onClick = { SHOW_DIALOG_FOR_ROUTES = true }, modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 10.dp)
                    .padding(start = 20.dp)
                    .weight(1f)
            ) {
                Text(text = "选择线路", style = MaterialTheme.typography.h5)
            }
            Divider(modifier = Modifier.padding(start = 20.dp))
            Button(
                onClick = { SHOW_DIALOG_FOR_LOGIN = true }, modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 10.dp)
                    .padding(start = 20.dp)
                    .weight(1f)
            ) {
                Text(text = "签到", style = MaterialTheme.typography.h5)
            }
            Divider(modifier = Modifier.padding(start = 20.dp))
            val scope = rememberCoroutineScope()
            OutlinedButton(
                onClick = {
                    scope.launch {
                        val (code, x) = RequestManager.logout(DRIVER_LOGIN_PHONE)
                        Toast.makeText(
                            context,
                            x,
                            Toast.LENGTH_SHORT
                        ).apply{
                            setGravity(Gravity.CENTER, 0, 0)
                        }.show()
                        if (code == 200) {
                            DRIVER_LOGIN_PHONE = ""
                            DRIVER_LOGIN_NAME = ""
                            DRIVER_LOGIN_ID = null
                            val sharedPref =
                                context.getSharedPreferences(BuildConfig.APPLICATION_ID, 0)
                            sharedPref.edit().remove("DRIVER_LOGIN_PHONE").apply()
                            sharedPref.edit().remove("DRIVER_LOGIN_NAME").apply()
                            sharedPref.edit().remove("DRIVER_LOGIN_ID").apply()
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 10.dp)
                    .padding(start = 20.dp)
                    .weight(1f),
                enabled = DRIVER_LOGIN_PHONE.isNotEmpty()
            ) {
                Text(text = "签退", style = MaterialTheme.typography.h5)
            }
        }
    }
}