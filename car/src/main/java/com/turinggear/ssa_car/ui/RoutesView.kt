package com.turinggear.ssa_car.ui

import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.MaterialTheme
import androidx.compose.material.RadioButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_car.util.RequestManager
import com.turinggear.ssa_car.viewmodel.RoutesViewModel
import com.turinggear.ssa_shared.SELECTED_ROUTE_ID
import com.turinggear.ssa_shared.SELECTED_ROUTE_NAME
import kotlinx.coroutines.launch

@Composable
fun RoutesView() {
    val vm: RoutesViewModel = viewModel()
    val routesFlow = vm.routesFlow.collectAsState()
    val routes = routesFlow.value ?: listOf()
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    Card(Modifier.clip(RoundedCornerShape(20.dp))) {
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .aspectRatio(1f),
            verticalArrangement = Arrangement.Center
        ) {
            routes.forEach {
                Row(
                    Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (it.id == SELECTED_ROUTE_ID),
                            onClick = {
                                scope.launch {
                                    val (code, msg) = RequestManager.changeBindingRoute(it.id)
                                    if (code == 200) {
                                        SELECTED_ROUTE_ID = it.id
                                        SELECTED_ROUTE_NAME = it.name
                                    }
                                    Toast
                                        .makeText(context, msg, Toast.LENGTH_SHORT)
                                        .show()
                                }
                            }
                        )
                        .padding(horizontal = 40.dp)
                        .padding(vertical = 20.dp)
                        .alpha(if (it.id == SELECTED_ROUTE_ID) 1f else 0.5f),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (it.id == SELECTED_ROUTE_ID),
                        onClick = {
                            scope.launch {
                                val (code, msg) = RequestManager.changeBindingRoute(it.id)
                                if (code == 200) {
                                    SELECTED_ROUTE_ID = it.id
                                    SELECTED_ROUTE_NAME = it.name
                                }
                                Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
                            }
                        }
                    )
                    Text(
                        text = it.name,
                        style = MaterialTheme.typography.h5,
                        modifier = Modifier.padding(start = 10.dp)
                    )
                }
            }
        }
    }
}