package com.turinggear.ssa_car.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Close
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_car.ui.theme.WechatGreen
import com.turinggear.ssa_shared.RESPONSE_CONSUME

@Composable
fun TicketConsumeView(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .border(
                1.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colors.onSurface.copy(alpha = 0.03f))
    ) {
        Box(modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(),
            contentAlignment = Alignment.TopEnd) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .padding(horizontal = 30.dp)
                    .padding(bottom = 4.dp),
                verticalArrangement = Arrangement.spacedBy(
                    16.dp,
                    alignment = Alignment.CenterVertically
                )
            ) {

                var numberString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var timeString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var typeString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var statusString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var textColor = MaterialTheme.colors.onSurface.copy(0.2f)
                var statusColor = textColor

                RESPONSE_CONSUME?.let { c ->
                    c.data?.let {
                        textColor = MaterialTheme.colors.onSurface
                        statusColor = WechatGreen
                        typeString = it.goods.name
                        numberString = it.ticket_no
                        timeString = it.created_at
                        statusString = when (it.status) {
                            10 -> {
                                "可用（${it.use_max-it.use}/${it.use_max}）"
                            }
                            20 -> {
                                "核销完毕（${it.use_max-it.use}/${it.use_max}）"
                            }
                            30 -> {
                                "已退款"
                            }
                            40 -> {
                                "已过期"
                            }
                            else -> {
                                "未知"
                            }
                        }
                    }
                }

                Text(
                    text = "订单编号：$numberString",
                    color = textColor,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    style = MaterialTheme.typography.h6
                )
                Text(
                    text = "购票时间：$timeString",
                    color = textColor,
                    style = MaterialTheme.typography.h6
                )
                Text(
                    text = "购票种类：$typeString",
                    color = textColor,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    style = MaterialTheme.typography.h6
                )
                Text(
                    text = "当前状态：$statusString",
                    color = statusColor,
                    style = MaterialTheme.typography.h6
                )
            }
            RESPONSE_CONSUME?.let {
                it.data?.let {
                    TextButton(
                        onClick = {
                            RESPONSE_CONSUME = null
                        },
                        modifier = Modifier
                            .padding(all = 10.dp)
                            .size(40.dp)
                            .aspectRatio(1f),
                        colors = ButtonDefaults.textButtonColors(backgroundColor = Color.Transparent),
                        contentPadding = PaddingValues(all = 0.dp)
                    ) {
                        Icon(
                            Icons.Outlined.Close,
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            tint = MaterialTheme.colors.onSurface.copy(0.4f)
                        )
                    }
                }
            }
        }
    }
}