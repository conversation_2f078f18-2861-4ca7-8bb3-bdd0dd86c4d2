package com.turinggear.ssa_car.ui

import android.media.MediaPlayer
import android.view.Gravity
import android.widget.Toast
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.GridCells
import androidx.compose.foundation.lazy.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_car.ui.theme.CarssaTheme
import com.turinggear.ssa_car.viewmodel.MediaPlayerViewModel
import com.turinggear.ssa_car.viewmodel.WaitDirectionModel
import com.turinggear.ssa_car.viewmodel.WaitViewModel
import com.turinggear.ssa_shared.CURRENT_CAR
import com.turinggear.ssa_shared.R
import java.time.Instant

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun WaitView() {

    val vm: WaitViewModel = viewModel()
    val waitsFlow = vm.waitsFlow.collectAsState()
    val waits = waitsFlow.value ?: listOf()

    val waitDirectionModel: WaitDirectionModel = viewModel()
    val waitsDirectionFlow = waitDirectionModel.waitsDirectionFlow.collectAsState()
    val waitsDirection = waitsDirectionFlow.value ?: ""

    val cellCount = if (waits.count() > 8) 6 else 4
    val padding = if (waits.count() > 8) 20.dp else 40.dp
    val context = LocalContext.current
    val mediaPlayerViewModel: MediaPlayerViewModel = viewModel()

    var state by remember {
        mutableStateOf(true)
    }
    val value by animateIntAsState(
        targetValue = if (state) 0 else 1000,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1000),
            repeatMode = RepeatMode.Reverse
        )
    )
    var timestamp: Long? by remember { mutableStateOf(null) }
    if (timestamp == null) {
        timestamp = Instant.now().epochSecond
    } else {
        timestamp?.let {
            // 10秒钟更新一次人数
            if (Instant.now().epochSecond - it >= 10) {
                timestamp = Instant.now().epochSecond
                vm.update()
                waitDirectionModel.update()
            }
        }
    }
    LaunchedEffect(true) {
        state = !state
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = padding)
            .padding(vertical = 10.dp),
        verticalArrangement = Arrangement.spacedBy(25.dp, alignment = Alignment.CenterVertically),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(contentAlignment = Alignment.Center) {
            var text = "各站点等待人数"
            if (waitsDirection.isNotEmpty()) {
                text += "\n\n${waitsDirection}"
            }
            Text(text = text,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.h6)
            Text(text = value.toString(), color = Color.Transparent)
        }
        LazyVerticalGrid(
            cells = GridCells.Fixed(cellCount),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            items(waits.count()) { i ->
                val wait = waits[i]
                Column(
                    modifier = Modifier
                        .padding(vertical = 5.dp)
                        .then(if(CURRENT_CAR?.station_id == wait.id) Modifier.background(Color.LightGray).border(5.dp, Color.Gray) else Modifier)
                        .clickable {
                            val enableStationSound = context.resources.getBoolean(R.bool.enable_station_sound)
                            if (enableStationSound) {
                                val rid = context.resources.getIdentifier(
                                    "station_${wait.id}",
                                    "raw",
                                    context.packageName
                                )
                                if (rid != 0) {
                                    mediaPlayerViewModel.play(context, rid)
                                } else {
                                    Toast.makeText(
                                        context,
                                        "错误: 无此声音素材",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                        },
                    verticalArrangement = Arrangement.SpaceEvenly,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "${wait.waiting}",
                        modifier = Modifier.padding(vertical = 10.dp),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.h5
                    )
                    Text(
                        text = wait.name,
                        modifier = Modifier.padding(bottom = 10.dp),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.h6
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun WaitPreview() {
    CarssaTheme {
        WaitView()
    }
}