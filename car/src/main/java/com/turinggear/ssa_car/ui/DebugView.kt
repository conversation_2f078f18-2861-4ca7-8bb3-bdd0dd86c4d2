package com.turinggear.ssa_car.ui

import android.content.ComponentName
import android.content.Intent
import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Close
import androidx.compose.material.icons.outlined.PlusOne
import androidx.compose.material.icons.outlined.PowerSettingsNew
import androidx.compose.material.icons.outlined.SystemUpdateAlt
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_car.MainActivity
import com.turinggear.ssa_shared.util.OSUtils
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.SHOW_DEBUGVIEW
import com.turinggear.ssa_shared.TheMainActivity
import kotlinx.coroutines.delay

@Composable
fun DebugView() {

    val inputNumber = remember { mutableStateOf("") }
    val showHiddenArea = remember { mutableStateOf(false) }
    val context = LocalContext.current

    LaunchedEffect(key1 = true) {
        // 20s 自动关闭
        delay(20 * 1000)
        SHOW_DEBUGVIEW = false
        ApplicationContext?.let {
            Toast.makeText(it, "调试界面已经自动关闭", Toast.LENGTH_SHORT).show()
        }
    }

    Box(
        modifier = Modifier
            .clickable(enabled = false, onClick = { })
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.8f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            Modifier
                .width(320.dp)
                .aspectRatio(0.7f)
                .clip(RoundedCornerShape(8.dp))
                .background(MaterialTheme.colors.background),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(fraction = 1 / 11f),
                contentAlignment = Alignment.BottomEnd
            ) {
                IconButton(
                    onClick = {
                        SHOW_DEBUGVIEW = false
                    }
                ) {
                    Icon(
                        Icons.Outlined.Close,
                        contentDescription = null,
                        tint = MaterialTheme.colors.onBackground.copy(0.5f)
                    )
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxHeight(fraction = 1 / 10f),
                contentAlignment = Alignment.TopCenter
            ) {
                val title = "调试页面"
                Text(
                    text = title,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold)
                )
            }
            TextButton(onClick = {
                OSUtils.adjustVolume(context)
            }) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Outlined.PlusOne,
                        contentDescription = null
                    )
                    Text(text = "增加音量", modifier = Modifier.padding(start = 8.dp))
                }
            }
            TextButton(onClick = {
                ApplicationContext?.let {
                    // 更新版本
                    if (OSUtils.isConsoleAppInstalled(it)) {
                        // 如果守护App存在则开启与守护App的通信,静默安装
                        if (TheMainActivity is MainActivity) {
                            (TheMainActivity as MainActivity).updateVersion(true)
                        }
                    } else {
                        Log.e("XXX", "console app not found")
                        // 否则弹窗安装
                        if (TheMainActivity is MainActivity) {
                            (TheMainActivity as MainActivity).updateVersion(false)
                        }
                    }
                }
            }) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Outlined.SystemUpdateAlt,
                        contentDescription = null
                    )
                    Text(text = "更新软件", modifier = Modifier.padding(start = 8.dp))
                }
            }
            Divider()

            // 密码键盘行
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 6.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                val numbers = (0..9).toMutableList()
//                numbers.shuffle()

                for (i in numbers.chunked(3)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        for (number in i) {
                            TextButton(onClick = {
                                if (inputNumber.value.length < 6) {
                                    inputNumber.value += number.toString()
                                }
                                if ((inputNumber.value.length == 6) && (inputNumber.value == "220405")) {
                                    showHiddenArea.value = true
                                }
                            }) {
                                Text(text = number.toString())
                            }
                        }
                    }
                }
            }

            if (showHiddenArea.value) {
                Button(onClick = {
                    val intent = Intent()
                    intent.component = ComponentName("com.android.launcher3", "com.android.launcher3.Launcher")
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    ApplicationContext?.startActivity(intent)
                }) {
                    Text(text = "显示桌面")
                }
                Divider()
                TextButton(onClick = {
                    ApplicationContext?.startActivity(Intent(Intent.ACTION_REBOOT))
                }) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Outlined.PowerSettingsNew,
                            contentDescription = null
                        )
                        Text(text = "重启系统", modifier = Modifier.padding(start = 8.dp))
                    }
                }
                Divider()
                Text(text = "IP地址: ${OSUtils.getIPAddress(true)}", modifier = Modifier.padding(8.dp))
            }
        }

    }

}
