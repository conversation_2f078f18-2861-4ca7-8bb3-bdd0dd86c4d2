package com.turinggear.ssa_car.hw;

public class Wiegand {
    public static char WG_26_MODE = 1;
    public static char WG_34_MODE = 2;
    public static char WG_66_MODE = 3;
    public static char WG_PERIOD_MODE = 4;
    public static char WG_PULSE_MODE = 5;

    public Wiegand() {
    }

    public long read(){
        return wiegandRead();
    }

    public int write(char mode, char[] buf){
        if(buf == null)
            return -1;
        if(buf.length == 25 || buf.length == 33 || buf.length == 65)
            return wiegandWrite(mode, buf);
        return -1;
    }

    //mode :WG_26_MODE = 1;WG_34_MODE = 2;WG_66_MODE = 3;WG_PERIOD_MODE = 4;WG_PULSE_MODE = 5
    //buf: 比如：unsigned char data[25] = "000000011101101011111010\0";
    public static native int wiegandWrite(char mode, char[] buf);

    //返回-1为错误，设备节点未打开或读错误
    public static native long wiegandRead();

    static {
        System.loadLibrary("wiegand");
    }
}
