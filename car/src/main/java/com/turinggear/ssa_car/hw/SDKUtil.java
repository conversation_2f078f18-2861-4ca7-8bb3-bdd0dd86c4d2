package com.turinggear.ssa_car.hw;

import static com.turinggear.ssa_car.hw.SystemDateTime.TAG;

import android.content.Context;
import android.content.Intent;
import android.os.SystemClock;
import android.provider.Settings;
import android.util.Log;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.security.InvalidParameterException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class SDKUtil {
    public static final int BOTH = 0x01, EITHER = 0x00;

    private Context mContext = null;
    private static SDKUtil sdk = null;
    private IBobyInfraredListener mBIListener = null;
    private IICCardListener mICCardListener = null;
    private IWiegandListener mWiegandListener = null;
    private int mLightMode = 0x00;
    private final int mBaundRate = 9600;
    private final String mDevicePath = "ttyS1";
    private final String mWiegandPath = "wiegand";
    private ICCardReadThread mICCardReadThread = null;
    private BodyInfraredThread mBodyInfraredThread = null;
    private WiegandReadThread mWiegandReadThread = null;
    private boolean isWorking = true;
    private InputStream mInputStream = null;
    private SerialPort mComPort = null;
    private Wiegand mWiegand = null;
    private static boolean mWhiteStatus = false, mGreenStatus = false, mRelayStatus = false;

    private SDKUtil(){}


    public static SDKUtil getInstance(Context context){
        if(sdk == null){
            return getInstance(context, null, null, null);
        }
        return sdk;
    }

    public static SDKUtil getInstance(Context context, IBobyInfraredListener bobyInfraredListener, IICCardListener icCardListener){
        if(sdk == null){
            return getInstance(context, bobyInfraredListener, icCardListener, null);
        }
        return sdk;
    }

    public static SDKUtil getInstance(Context context, IBobyInfraredListener bobyInfraredListener, IICCardListener icCardListener, IWiegandListener mWiegandListener ){
        if(sdk == null){
            sdk = new SDKUtil();
            sdk.setContext(context);
            if(bobyInfraredListener != null)
                sdk.setBobyInfraredListener(bobyInfraredListener);
            if(icCardListener != null)
                sdk.setICCardListenerListener(icCardListener);
            if(mWiegandListener != null)
                sdk.setWiegandListener(mWiegandListener);
        }
        return sdk;
    }

    private void setBobyInfraredListener(IBobyInfraredListener biListener){
        if(biListener != null) {
            mBIListener = biListener;
            mBodyInfraredThread = new BodyInfraredThread();
            mBodyInfraredThread.start();
        }
    }

    private void setICCardListenerListener(IICCardListener icCardListener){
        if(icCardListener != null) {
            mICCardListener = icCardListener;
            mICCardReadThread = new ICCardReadThread();
            mICCardReadThread.start();
        }
    }

    private void setWiegandListener(IWiegandListener wiegandListener){
        if(wiegandListener != null) {
            mWiegandListener = wiegandListener;
            mWiegandReadThread = new WiegandReadThread();
            mWiegandReadThread.start();
        }
    }

    private void setContext(Context context){
        mContext = context;
    }

    public void setLightMode(int lightMode){
        if(  -1 < lightMode || lightMode > 1)
            return;
        mLightMode = lightMode;
    }

    public void release() {
        isWorking = false;
        closeWhiteLight();
        closeGreenLight();
        try{
            if(mInputStream != null)
                mInputStream.close();
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
        try{
            if(mComPort != null)
                mComPort.close();
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
    }

    public void openWhiteLight(){
        if(!mWhiteStatus) {
            Intent it = new Intent("com.android.intent.OpenWhiteFlashlight");
            mContext.sendBroadcast(it);
        }
        if(mLightMode == EITHER){
            closeGreenLight();
        }
        mWhiteStatus = true;
    }

    public void closeWhiteLight(){
        if(mWhiteStatus) {
            Intent it = new Intent("com.android.intent.CloseWhiteFlashlight");
            mContext.sendBroadcast(it);
        }
        mWhiteStatus = false;
    }

    public void openGreenLight() {
        if(!mGreenStatus) {
            Intent it = new Intent("com.android.intent.OpenGreenFlashlight");
            mContext.sendBroadcast(it);
        }
        if(mLightMode == EITHER){
            closeWhiteLight();
        }
        mGreenStatus = true;
    }

    public void closeGreenLight() {
        if(mGreenStatus) {
            Intent it = new Intent("com.android.intent.CloseGreenFlashlight");
            mContext.sendBroadcast(it);
        }
        mGreenStatus = false;
    }

    public void openRelay() {
        if(!mRelayStatus) {
            Intent it = new Intent("com.android.intent.OpenRelay");
            mContext.sendBroadcast(it);
        }
        mRelayStatus = true;
    }

    public void closeRelay() {
        if(mRelayStatus) {
            Intent it = new Intent("com.android.intent.CloseRelay");
            mContext.sendBroadcast(it);
        }
        mRelayStatus = false;
    }


    public void enableStatusbar() {
        Intent it = new Intent("com.android.intent.EnableStatusbar");
        mContext.sendBroadcast(it);
    }

    public void disableStatusbar() {
        Intent it = new Intent("com.android.intent.DisableStatusbar");
        mContext.sendBroadcast(it);
    }

    public void enableNavigation() {
        Intent it = new Intent("com.android.intent.EnableNavigation");
        mContext.sendBroadcast(it);
    }

    public void disableNavigation() {
        Intent it = new Intent("com.android.intent.DisableNavigation");
        mContext.sendBroadcast(it);
    }

    public void wiegandWrite(char mode, char[] buf){
        try {
            File file = new File("/dev/" + mWiegandPath);
            if (!file.exists()) {
                Log.e("SDKUtil","韦根节点不存在,无法写韦根数据！");
                return;
            }
            if(mWiegand == null){
                mWiegand = new Wiegand();
            }
            mWiegand.write(mode, buf);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void onBodyInfraredEvent(Boolean arg){
        if(mBIListener != null)
            mBIListener.onBobyInfraredEvent(arg);
    }

    private void onWiegandReadEvent(Long arg){
        if(mWiegandListener != null)
            mWiegandListener.onWiegandReadEvent(arg);
    }

    private void onICCardEvent(String arg){
        if(mBIListener != null)
            mICCardListener.onICCardReadEvent(arg);
    }

    class BodyInfraredThread extends Thread{
        public void run(){
            int lastStatus = -1;
            while (isWorking){
                try{
                    sleep(1000 * 1);
                }
                catch (Exception ex){
                    ex.printStackTrace();
                }
                try{
                    GPIOPort port = new GPIOPort(170);
                    if(port != null) {
                        //port.enable_in();
                        final int val = port.read();
                        if(lastStatus != val) {
                            lastStatus = val;
                            if(val == 1) {
                                onBodyInfraredEvent(true);
                            }
                            else {
                                onBodyInfraredEvent(false);
                            }
                        }
                    }
                }
                catch (Exception ex){
                    ex.printStackTrace();
                }
            }
        }
    }

    private class WiegandReadThread extends Thread {
        @Override
        public void run() {
            super.run();
            try{
                sleep(1000 * 1);
            }
            catch (Exception ex){
                ex.printStackTrace();
            }
            try {
                File file = new File("/dev/" + mWiegandPath);
                if (!file.exists()) {
                    Log.e("SDKUtil","韦根节点不存在,无法读韦根数据！");
                    return;
                }
                mWiegand = new Wiegand();
            }
            catch (Exception e){
                e.printStackTrace();
            }
            while (isWorking && mWiegand != null) {
                try {
                   long ret = mWiegand.read();
                    if(ret != -1)
                        onWiegandReadEvent(ret);
                }
                catch (Exception e) {
                    e.printStackTrace();
                }
                try{
                    sleep(100 * 1);
                }
                catch (Exception ex){
                    ex.printStackTrace();
                }
            }
        }
    }

    private class ICCardReadThread extends Thread {
        @Override
        public void run() {
            super.run();
            try{
                sleep(1000 * 1);
            }
            catch (Exception ex){
                ex.printStackTrace();
            }
            try {
                File file = new File("/dev/" + mDevicePath);
                if (!file.exists()) {
                    throw new InvalidParameterException();
                }
                mComPort = new SerialPort(file, mBaundRate, 0, 8,1 );
                mInputStream = mComPort.getInputStream();
            }
            catch (Exception e){
                e.printStackTrace();
            }
            while (isWorking) {
                int size = 0;
                try {
                    byte[] buf = new byte[256];	//32
                    if (mInputStream == null) {
                        System.out.println("mInputStream为空，退出");
                        return;
                    }
                    System.out.println("等待刷卡>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                    size = mInputStream.read(buf);
                    System.out.println("读取数据长度：" + size);
                    if (size > 0) {
                        StringBuffer sb = new StringBuffer();
                        for(int i = 0; i < size ; i++) {
                            sb.append(byteToHex(buf[i]));
                        }
                        String mRecvString = sb.toString();
                        onICCardEvent(mRecvString);
                    }
                }
                catch (Exception e) {
                    e.printStackTrace();
                }
                try{
                    sleep(100 * 1);
                }
                catch (Exception ex){
                    ex.printStackTrace();
                }
            }
        }
    }

    private String byteToHex(final byte b) {
        StringBuffer buf = new StringBuffer();
        char[] hexChars = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
        int high = ((b & 0xf0) >> 4);
        int low = (b & 0x0f);
        buf.append(hexChars[high]);
        buf.append(hexChars[low]);
        return buf.toString();
    }

    /**
     * 作用：重启设备
     */
    public void rebootDevice(){
        try {
            Runtime.getRuntime().exec("reboot");// 重启设备
        }
        catch (Exception e) {
            Log.e("TAG", e.getMessage(), e);
        }
    }

    /**
     * 作用：启动多个无障碍服务
     */
    public boolean startAccessibilityServices(List<ServiceVO> mServiceList){
        if(mServiceList == null || mServiceList.size() == 0){
            return false;
        }
        int accessibilityEnabled = 0;
        DataOutputStream dos = null;
        try {
            Process process = Runtime.getRuntime().exec("su");// 申请su权限
            dos = new DataOutputStream(process.getOutputStream());
            if(dos != null) {
                for(ServiceVO vo : mServiceList ){
                    dos.write(("settings put secure enabled_accessibility_services " + vo.getPkgName() + "/" + vo.getServiceName() + " \n").getBytes());
                    dos.flush();
                }
                dos.write("settings put secure accessibility_enabled 1 \n".getBytes());
                dos.flush();
                dos.writeBytes("exit \n");
                dos.flush();
                dos.close();
                dos = null;
                process.waitFor();
                process.destroy();
            }
        }
        catch (Exception e) {
            Log.e("TAG", e.getMessage(), e);
        }
        finally {
            try {
                if (dos != null) {
                    dos.close();
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            accessibilityEnabled = Settings.Secure.getInt(mContext.getApplicationContext().getContentResolver(), Settings.Secure.ACCESSIBILITY_ENABLED);
            Log.v(TAG, "无障碍开启状态为 " + (accessibilityEnabled == 1 ? " 开启" : "关闭"));
        }
        catch (Settings.SettingNotFoundException e) {
            Log.e(TAG, "Error finding setting, default accessibility to not found: " + e.getMessage());
        }
        return accessibilityEnabled == 1;
    }

    public boolean startAccessibilityService(String pkgName, String serviceName){
        int accessibilityEnabled = 0;
        DataOutputStream dos = null;
        try {
            Process process = Runtime.getRuntime().exec("su");// 申请su权限
            dos = new DataOutputStream(process.getOutputStream());
            if(dos != null) {
                dos.write(("settings put secure enabled_accessibility_services " + pkgName + "/" + serviceName + " \n").getBytes());
                dos.flush();
                dos.write("settings put secure accessibility_enabled 1 \n".getBytes());
                dos.flush();
                dos.writeBytes("exit \n");
                dos.flush();
                dos.close();
                dos = null;
            }
            process.waitFor();
            process.destroy();
        }
        catch (Exception e) {
            Log.e("TAG", e.getMessage(), e);
        }
        finally {
            try {
                if (dos != null) {
                    dos.close();
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            accessibilityEnabled = Settings.Secure.getInt(mContext.getApplicationContext().getContentResolver(), Settings.Secure.ACCESSIBILITY_ENABLED);
            Log.v(TAG, "无障碍开启状态为 " + (accessibilityEnabled == 1 ? " 开启" : "关闭"));
        }
        catch (Settings.SettingNotFoundException e) {
                Log.e(TAG, "Error finding setting, default accessibility to not found: " + e.getMessage());
        }
        return accessibilityEnabled == 1;
    }

    /**
     * 作用：设置DPI
     */
    public void adjustDpi(int dpi){
        try {
            Runtime.getRuntime().exec("su 0 wm density " + dpi);//设置DPI
        }
        catch (Exception e) {
            Log.e("TAG", e.getMessage(), e);
        }
    }

    /**
     * 作用：设置系统时间
     * @param year 年，1971-2049
     * @param month 月，1-12
     * @param day 日，正常1、3、5、7、8、10、12为31天；2月28天；4,6，9,11为30天；闰年2月为29天，
     * @param hour 时，0-23
     * @param minute 分，0-59
     * @param second 秒，0-59
     */
    public void setSystemDateTime(int year, int month, int day, int hour, int minute, int second){
        if(1971 > year  || year > 2049){
            Log.e("TAG", "year参数错误：正确范围1971-2049");
            return;
        }
        year = year % 100;
        if(1 > month  || month > 12){
            Log.e("TAG", "month参数错误：正确范围1-12");
            return;
        }
        int daysOfMonth = getMonthOfDay(year, month);
        if(1 > day  || day > daysOfMonth){
            Log.e("TAG", "day参数错误：：正确范围1-" + daysOfMonth);
            return;
        }
        if(0 > hour  || hour > 23){
            Log.e("TAG", "hour参数错误");
            return;
        }
        if(0 > minute  || minute > 59 ){
            Log.e("TAG", "minute参数错误");
            return;
        }
        if(0 > second  || second > 59 ){
            Log.e("TAG", "second参数错误");
            return;
        }
        DataOutputStream dos = null;
        try {
            Process process = Runtime.getRuntime().exec("su");// 申请su权限
            dos = new DataOutputStream(process.getOutputStream());
            String mSettingDt = String.format("%02d%02d%02d%02d%02d.%02d",month,day,hour,minute,year,second);
            String command = "/system/bin/date " + mSettingDt + " \n";
            dos.write(command.getBytes(Charset.forName("utf-8")));
            dos.flush();
            command = "hwclock -w --utc \n";
            dos.write(command.getBytes(Charset.forName("utf-8")));
            dos.flush();
            dos.writeBytes("exit\n");
            dos.flush();
            dos.close();
            dos = null;
            process.waitFor();
            process.destroy();
        }
        catch (Exception e) {
            Log.e("TAG", e.getMessage(), e);
        }
        finally {
            try {
                if (dos != null) {
                    dos.close();
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public int getMonthOfDay(int year,int month) {
        int day = 0;
        if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {
            day = 29;
        } else {
            day = 28;
        }
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            case 2:
                return day;
        }
        return 0;
    }

}
