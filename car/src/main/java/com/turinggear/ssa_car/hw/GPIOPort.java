package com.turinggear.ssa_car.hw;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;

public class GPIOPort {
    private static String sys_gpio_path = "/sys/class/gpio";
    private static String export_path = "/sys/class/gpio/export";
    private static String unexport_path = "/sys/class/gpio/unexport";
    private static final String TAG = "GPIOPort";
    private int gpio_id = -1;

    public GPIOPort(int bank, int nr) {
        this.gpio_id = (bank - 1) * 32 + nr;
        this.unexport(this.gpio_id);
        int ret = this.export(this.gpio_id);
    }

    public GPIOPort(int id) {
        this.gpio_id = id;
    }

    private int export() {
        return this.gpio_id < 0 ? -1 : this.export(this.gpio_id);
    }

    private int unexport() {
        return this.gpio_id < 0 ? -1 : this.unexport(this.gpio_id);
    }

    public int enable_out() {
        return this.gpio_id < 0 ? -1 : this.enable_out(this.gpio_id);
    }

    public int enable_in() {
        return this.gpio_id < 0 ? -1 : this.enable_in(this.gpio_id);
    }

    public int read() {
        return this.gpio_id < 0 ? -1 : this.read(this.gpio_id);
    }

    public int write(int value) {
        return this.gpio_id < 0 ? -1 : this.write(this.gpio_id, value);
    }

    private int export(int id) {
        try {
            File file = new File(export_path);
            FileOutputStream fos = new FileOutputStream(file);
            new String();
            byte[] bytes = String.valueOf(id).getBytes();
            fos.write(bytes);
            fos.close();
            return 0;
        } catch (Exception var5) {
            var5.printStackTrace();
            return -1;
        }
    }

    private int unexport(int id) {
        try {
            File file = new File(unexport_path);
            FileOutputStream fos = new FileOutputStream(file);
            new String();
            byte[] bytes = String.valueOf(id).getBytes();
            fos.write(bytes);
            fos.close();
            return 0;
        } catch (Exception var5) {
            var5.printStackTrace();
            return -1;
        }
    }

    private int enable_out(int id) {
        try {
            String out_path = sys_gpio_path + "/gpio" + id + "/direction";
            File file = new File(out_path);
            FileOutputStream fos = new FileOutputStream(file);
            byte[] bytes = (new String("out")).getBytes();
            fos.write(bytes);
            fos.close();
            return 0;
        } catch (Exception var6) {
            var6.printStackTrace();
            return -1;
        }
    }

    private int enable_in(int id) {
        try {
            String in_path = sys_gpio_path + "/gpio" + id + "/direction";
            File file = new File(in_path);
            FileOutputStream fos = new FileOutputStream(file);
            byte[] bytes = (new String("in")).getBytes();
            fos.write(bytes);
            fos.close();
            return 0;
        } catch (Exception var6) {
            var6.printStackTrace();
            return -1;
        }
    }

    private int read(int id) {
        boolean var2 = true;

        try {
            String in_path = sys_gpio_path + "/gpio" + id + "/value";
            BufferedReader reader = new BufferedReader(new FileReader(in_path));
            String data = reader.readLine();
            reader.close();
            int ret = Integer.parseInt(data);
            return ret;
        } catch (Exception var6) {
            var6.printStackTrace();
            return -1;
        }
    }

    private int write(int id, int bon) {
        try {
            String in_path = sys_gpio_path + "/gpio" + id + "/value";
            File file = new File(in_path);
            FileOutputStream fos = new FileOutputStream(file);
            byte[] bytes = String.valueOf(0 == bon ? 0 : 1).getBytes();
            fos.write(bytes);
            fos.close();
            return 0;
        } catch (Exception var7) {
            var7.printStackTrace();
            return -1;
        }
    }
}
