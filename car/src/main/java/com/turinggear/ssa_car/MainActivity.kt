package com.turinggear.ssa_car

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.*
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.text.format.DateFormat
import android.util.Log
import android.view.KeyEvent
import android.widget.Toast
import android.widget.Toast.LENGTH_LONG
import android.widget.Toast.LENGTH_SHORT
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import coil.compose.rememberAsyncImagePainter
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.turinggear.ssa_car.model.LocationEvent
import com.turinggear.ssa_car.service.*
import com.turinggear.ssa_car.ui.*
import com.turinggear.ssa_car.ui.theme.CarssaTheme
import com.turinggear.ssa_car.ui.theme.WechatGreen
import com.turinggear.ssa_car.util.*
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.ui.RegisterAlertDialog
import com.turinggear.ssa_shared.util.ConnectionState
import com.turinggear.ssa_shared.util.OSUtils
import com.turinggear.ssa_shared.util.UpgradeUtil
import com.turinggear.ssa_shared.util.checkConnection
import com.turinggear.ssa_shared.util.cleanScannedQrcode
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import java.net.URLEncoder
import java.time.Instant
import java.util.Calendar
import java.util.Locale
import com.turinggear.ssa_shared.R

@SuppressLint("HardwareIds")
@Suppress("DEPRECATION")
class MainActivity : ComponentActivity() {

    lateinit var hardwareManager: IHardwareManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        TheMainActivity = this
        ApplicationContext = this.applicationContext
        API_BASE = this.applicationContext?.getString(R.string.api_endpoint) ?: ""

        this.hardwareManager = HardwareManager(this)
        this.hardwareManager.init()

        checkConnection(this, url = apiHealthCheck(), Lifecycle.State.CREATED, stopOnConnected = true) {
            when (it) {
                // 初始
                ConnectionState.CONNECTED -> {
                    Log.e("ConnectionChecker", "Connected")
                    IsNetworkConnected = true
                }
                // 重连
                ConnectionState.RECONNECTED -> {
                    Log.e("ConnectionChecker", "ReConnected")
//                    finish()
//                    startActivity(this.intent)
                    <EMAIL>()
                    IsNetworkConnected = true
                }
                // 断开
                else -> {
                    Log.e("ConnectionChecker", it.toString())
                    IsNetworkConnected = false
                }
            }
        }

        // 监控网络连接情况
//        application.registerReceiver(
//            broadcastReceiver,
//            IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
//        )

        application.registerReceiver(GpsDeviceStatusReceiver(), IntentFilter("com.turinggear.ssa_car.bc.GPS_DEVICE_STATUS"))

        val sharedPref = this.getSharedPreferences(BuildConfig.APPLICATION_ID, Context.MODE_PRIVATE)
        sharedPref.getString("DRIVER_LOGIN_PHONE", "")?.let {
            DRIVER_LOGIN_PHONE = it
        }
        sharedPref.getString("DRIVER_LOGIN_NAME", "")?.let {
            DRIVER_LOGIN_NAME = it
        }
        sharedPref.getInt("DRIVER_LOGIN_ID", 0).let {
            DRIVER_LOGIN_ID = it
        }

        setContent {
            CarssaTheme {
                Box {
                    MainView()
                    if (SHOW_ALERT_FOR_REGISTER) {
                        RegisterAlertDialog(
                            code = RANDOM_REGISTER_CODE,
                            error = REGISTER_ERROR_STRING
                        )
                    }
                }
            }
        }

        this.hardwareManager.fullScreen()

        Log.e("ssa", if (OSUtils.isSystemApp(this@MainActivity)) "system app" else "user app")

        // 第一次安装(未注册)的弹窗这里合并处理
        // 如果为系统App 弹窗 注册设备
        // 如果非系统App 弹窗 安装为系统App,  等下次启动再注册
        if (!HasTriedToRegisterAfterLaunched) {
            lifecycleScope.launch {
                if (OSUtils.isSystemApp(this@MainActivity)) {
//                if(true) { //debug
                    RequestManager.registerDevice()
                } else {
                    val builder = AlertDialog.Builder(this@MainActivity)
                    builder.setTitle("下载新App并安装为系统App?")
                    builder.setCancelable(false)
                    builder.setMessage("1. 需要耐心等待下载完成\n2.卸载时候点击_确定_\n3.卸载完成后需要手动_重启_设备")
                    builder.setPositiveButton(android.R.string.yes) { dialog, which ->
                        Toast.makeText(applicationContext, "开始下载,请等待", LENGTH_LONG).show()

//                        OSUtils.remount()
                        downloadAndInstallAsSystemApp()
                    }
                    builder.setNegativeButton(android.R.string.no) { dialog, which ->
                        Toast.makeText(applicationContext,
                            android.R.string.no, Toast.LENGTH_SHORT).show()
                    }
                    builder.show()
                }
            }
        }

        // 更新版本
        if (OSUtils.isConsoleAppInstalled(this)) {
            // 如果守护App存在则开启与守护App的通信,静默安装
            APPUtil.startLocalGuardService(this)
            APPUtil.startRemoteGuardService(this)

            updateVersion(true)
        } else {
            Log.e("XXX", "console app not found")
            // 否则弹窗安装
            updateVersion(false)
        }

        // 启动定时循环任务
        val intent = Intent(this, ScheduleService::class.java)
        intent.putExtra("cmd", "event_report_loop")
        intent.putExtra("delay", 8_000L) // 8s
        this.startService(intent)

        startPollingService()
    }

    fun startPollingService() {
        val intent = Intent(this, PollingService::class.java)
        intent.putExtra("cmd", "polling")
        this.startService(intent)
    }

    fun downloadAndInstallAsSystemApp() {
        lifecycleScope.launch {
            // 从 *发布页* 下载App, 注意要有console
            // 要求执行过 make copy 和 make upload_landing_page_XXX
            val oss_url = ApplicationContext?.getString(R.string.oss_url)

            if (oss_url == null) {
                return@launch
            }

            coroutineScope {
                launch {
                    val url = UpgradeUtil.defaultApkUrl(oss_url, packageName)
                    var ret = UpgradeUtil.download(this@MainActivity, packageName, url)
                    if (ret) {
                        ret = UpgradeUtil.installAsSystemApp(this@MainActivity, packageName)
                    }
                    if (!ret) {
                        Toast.makeText(applicationContext, "下载失败", LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(applicationContext, "安装成功", LENGTH_SHORT).show()

                        val builder = AlertDialog.Builder(this@MainActivity)
                        builder.setTitle("重启系统生效")
                        builder.setCancelable(true)
                        builder.setPositiveButton(android.R.string.yes) { dialog, which ->
                            OSUtils.reboot()
                        }
                        builder.show()
                    }
                    return@launch
                }
            }
        }
    }

    fun updateVersion(isSilent: Boolean) {
        lifecycleScope.launch {
            var appVersion = STARTUP?.data?.appVersion
            if (appVersion == null) {
                appVersion = RequestManager.startup(BuildConfig.VERSION_CODE)
//                appVersion = RequestManager.startup(1) //debug
            }
            if (appVersion == null) {
                Toast.makeText(applicationContext, "无需更新", LENGTH_LONG).show()
            }
            appVersion?.let {
                if (!it.down_url.endsWith(".apk", ignoreCase = true)) {
                    return@let
                }
                val message = "开始到版本(${it.code})的更新"
                Toast.makeText(applicationContext, message, LENGTH_LONG).show()

                //

                /*
                等待GuardService启动完成, 防止下载速度过快, 不是非常必须, 因为不加也没有出现过问题

                加了*2s*延时后

                2022-04-20 08:32:11.701 1563-1563/? E/XXX: com.turinggear.ssa_console create
                2022-04-20 08:32:15.448 1551-1551/? E/XXX: com.turinggear.ssa_car create
                2022-04-20 08:32:15.451 1563-1563/? E/XXX: com.turinggear.ssa_console bind
                2022-04-20 08:32:15.453 1551-1551/? E/XXX: com.turinggear.ssa_car bind
                2022-04-20 08:32:15.465 1551-1551/? D/XXX: 收到com.turinggear.ssa_console消息：name=com.turinggear.ssa_console
                2022-04-20 08:32:15.472 1563-1563/? D/XXX: 收到com.turinggear.ssa_car消息：name=com.turinggear.ssa_car
                2022-04-20 08:32:16.372 1443-1443/? E/XXX: downloading
                2022-04-20 08:32:16.375 1443-1443/? D/XXX: downloaded
                 */
                delay(2000)

                Log.e("XXX", "downloading")
                val url = it.down_url
                var ret = false
                try {
                    ret = UpgradeUtil.download(this@MainActivity, packageName, url)
                    Log.e("XXX", "downloaded")
                    if (ret) {
                        if (isSilent) {
                            ret = UpgradeUtil.installByPM(this@MainActivity, packageName)
                        } else {
                            ret = UpgradeUtil.installInteractively(this@MainActivity, packageName)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("XXX", "error ${e.localizedMessage}")
                    Toast.makeText(this@MainActivity, e.localizedMessage, LENGTH_SHORT).show()

                    ret = false
                }
                Log.e("XXX", "download result $ret")
                if (ret) {
                    Toast.makeText(this@MainActivity, "更新成功", LENGTH_SHORT).show()
                }
            }
        }
    }

    // 监控扫码
    private var keyboardInputBuffer = ""
    private var isProcessingTicket = false

    @SuppressLint("RestrictedApi")
    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        // 如果正在处理票号，忽略新的输入事件
        if (isProcessingTicket) {
            return true
        }

        if (event.action != KeyEvent.ACTION_UP) {
            keyboardInputBuffer += event.unicodeChar.toChar()
            return true
        }
        if (event.keyCode == KeyEvent.KEYCODE_ENTER) {
//            Log.e("XXX", "BARCODE raw:" + barcodeString)
//            Log.e("XXX", "BARCODE raw:" + barcodeString.toByteArray().joinToString("") { "%02x".format(it) })
//            BARCODE = barcodeString.replace("\n", "")
//            BARCODE = barcodeString.replace("\n", "").replace("\u0000", "")
//            Log.e("BARCODE", BARCODE)
            val processed = cleanScannedQrcode(keyboardInputBuffer)
//            Log.e("BARCODE final:", processed)
            // 比如二维码尾部带多个换行会多次触发, 这里过滤掉
            if (processed.length == 0) {
                keyboardInputBuffer = "" // 清空无效输入
                return true
            }

            // 清空输入缓冲区
            keyboardInputBuffer = ""

            // 检查是否为有效的扫码
            if (processed.isNotEmpty()) {
                // 立即设置处理状态，确保快速返回
                isProcessingTicket = true
                BARCODE = processed

                // 异步处理票号验证
                processTicket(processed)
            }
        }
        return super.dispatchKeyEvent(event)
    }

    /**
     * 异步处理票号验证
     * 从 dispatchKeyEvent 中分离出来，确保 dispatchKeyEvent 快速返回
     */
    private fun processTicket(barcode: String) {
        lifecycleScope.launch {
            try {
                val msg = RequestManager.consumeTicketWith(barcode)
                var result = 0

                // 根据响应结果处理音频、硬件和UI
                if (RESPONSE_CONSUME == null) {
                    result = 0
                    var rid = R.raw.consume_fail
                    if (msg != null) {
                        rid = when {
                            msg.contains("二维码无效") -> R.raw.consume_invalidcode

                            msg.contains("多次票不允许多人") -> R.raw.consume_multiride
                            msg.contains("重新在此设备登录") -> R.raw.consume_relogin
                            msg.contains("此票不可用") -> R.raw.consume_used
                            msg.contains("次数已用完") -> R.raw.consume_used
                            msg.contains("已经过期") -> R.raw.consume_expired
                            msg.contains("未在有效期内") -> R.raw.consume_inactice
                            msg.contains("已经退款") -> R.raw.consume_refunded
                            else -> R.raw.consume_fail
                        }
                    }

                    // 并行执行音频播放和显示消息
                    launch {
                        MediaManager.mediaPlayer?.release()
                        MediaManager.mediaPlayer = MediaPlayer.create(<EMAIL>, rid)
                        MediaManager.mediaPlayer?.start()
                    }
                } else {
                    RESPONSE_CONSUME?.let {
                        if (it.code == 200) {
                            result = 1

                            // 并行执行硬件操作和音频播放
                            launch {
                                hardwareManager.flashGreenLight()
                            }
                            launch {
                                MediaManager.mediaPlayer?.release()
                                MediaManager.mediaPlayer = MediaPlayer.create(<EMAIL>, R.raw.consume_succ)
                                MediaManager.mediaPlayer?.start()
                            }
                        }
                    }
                }

                // 显示消息（与音频/硬件操作并行）
                msg?.let {
                    launch {
                        SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                        SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(it)
                    }
                }

                // 事件报告（与其他操作并行）
                launch {
                    try {
                        RequestManager.reportEvents(data = LocationEvent(
                            event_type = 50,
                            gps_status = 0,
                            version_code = BuildConfig.VERSION_CODE,
                            note = URLEncoder.encode(barcode, "UTF-8")
                                    + "---" + msg
                                    + "---" + result,
                        ))
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Error reporting events: ${e.localizedMessage}")
                        e.printStackTrace()
                    }
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error processing ticket: ${e.localizedMessage}")
                e.printStackTrace()
            } finally {
                // 确保无论成功还是失败都重置状态
                isProcessingTicket = false
            }
        }
    }

//    private val broadcastReceiver = object : BroadcastReceiver() {
//        override fun onReceive(context: Context?, intent: Intent?) {
//            when (intent?.action) {
//                "android.net.conn.CONNECTIVITY_CHANGE" -> {
//                    val cm =
//                        <EMAIL>(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
//                    val activeNetwork: android.net.NetworkInfo? = cm.activeNetworkInfo
//                    val isConnected: Boolean = activeNetwork?.isConnectedOrConnecting == true
//                    if (isConnected and !IsNetworkConnected) {
//                        finish()
//                        startActivity(getIntent())
//                    }
//                    IsNetworkConnected = isConnected
//
//                }
//            }
//        }
//    }

    override fun onDestroy() {
        super.onDestroy()
        this.hardwareManager.deinit()
    }
}

@Composable
fun MainView() {
    val scaffoldState = rememberScaffoldState()
    SCAFFOLD_STATE = scaffoldState
    Scaffold(
        scaffoldState = scaffoldState,
        topBar = {
            TopAppBar(
                title = {
                    val carTitle = STARTUP?.data?.machineStaticConfig?.car_title ?: ""
                    Text(
                        "$carTitle Ver ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                        style = MaterialTheme.typography.h5
                    )
                },
                modifier = Modifier.height(70.dp),
                navigationIcon = {
                    Image(
                        painter = rememberAsyncImagePainter(R.drawable.logo),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .padding(start = 5.dp)
                            .size(50.dp)
                            .clip(RoundedCornerShape(3.dp))
                    )
                },
                actions = {
                    IconButton(onClick = { }, enabled = false) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                text = "网络未连接",
                                color = if (IsNetworkConnected) Color.Transparent else Color.Yellow,
                                style = MaterialTheme.typography.h6
                            )
                            Spacer(Modifier.width(2.dp))
                            Icon(
                                if (IsNetworkConnected) Icons.Filled.Wifi else Icons.Filled.WifiOff,
                                contentDescription = null,
                                modifier = Modifier.size(26.dp),
                                tint = if (IsNetworkConnected) Color.Green else Color.Yellow
                            )
                            Spacer(Modifier.width(15.dp))
                        }
                    }
                    IconButton(onClick = {
                        // 打开设置
                        if (GlobalGpsStatus == GPS_DISABLED) {
                            (TheMainActivity as MainActivity).hardwareManager.exitFullScreen()

                            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                            TheMainActivity?.startActivity(intent);
                        }
                    }, enabled = true) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                if (GlobalGpsStatus == GPS_FIX)
                                    Icons.Filled.LocationOn
                                else if (GlobalGpsStatus == GPS_NO_FIX)
                                    Icons.Filled.Search
                                else if (GlobalGpsStatus == GPS_DISABLED)
                                    Icons.Filled.LocationDisabled
                                else
                                    Icons.Filled.LocationOff,
                                contentDescription = null,
                                modifier = Modifier.size(26.dp),
                                tint = if (GlobalGpsStatus == GPS_FIX) Color.Green else Color.Yellow
                            )
                        }
                    }
                    IconButton(onClick = {
                        SHOW_DEBUGVIEW = true
                    }) {
                        Icon(
                            Icons.Filled.Menu,
                            contentDescription = null,
                            modifier = Modifier.size(26.dp)
                        )
                    }
                }
            )
        },
        snackbarHost = {
            SnackbarHost(hostState = it,
                modifier = Modifier.fillMaxSize(),
                snackbar = { data ->
                    Snackbar(
                        modifier = Modifier
                            .padding(20.dp)
                            .clickable { it.currentSnackbarData?.dismiss() },
                        backgroundColor = Color(0xFFDD8B32)
                    ) {
                        Text(
                            text = data.message,
                            modifier = Modifier.padding(20.dp),
                            style = MaterialTheme.typography.h4
                        )
                    }
                })
        },
        content = {
            MainContentView()
        }
    )
}

@SuppressLint("HardwareIds")
@Suppress("DEPRECATION")
@Composable
fun MainContentView() {
    Column(
        Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(fraction = 0.35f)
                .padding(all = 30.dp)
                .border(
                    1.dp,
                    MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    RoundedCornerShape(20.dp)
                )
                .clip(RoundedCornerShape(20.dp))
                .background(MaterialTheme.colors.onSurface.copy(alpha = 0.03f)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            var use: Int? = null
            var useMax: Int? = null
            var useColor = WechatGreen
            var title = "准备就绪"
            var titleColor = MaterialTheme.colors.onSurface
            var textColor = MaterialTheme.colors.onSurface.copy(0.2f)
            var textDecoration = TextDecoration.None

            RESPONSE_CONSUME?.let { c ->
                c.data?.let {
                    use = it.use
                    useMax = it.use_max
                    if (it.status != 10) {
                        useColor = Color.Red
                        titleColor = useColor
                        textDecoration = TextDecoration.LineThrough
                    }
                    title = when (it.status) {
                        10 -> {
                            "检票成功"
                        }
                        20 -> {
                            if (it.use_max > 1) {
                                "已用完"
                            } else {
                                "已使用"
                            }
                        }
                        30 -> {
                            "已退款"
                        }
                        40 -> {
                            "已过期"
                        }
                        else -> {
                            "未知"
                        }
                    }

                    textColor = MaterialTheme.colors.onSurface
                    useColor = WechatGreen
                    titleColor = useColor
                    title = "检票成功"
                    textDecoration = TextDecoration.None
                }
            }
            Text(
                text = title,
                color = titleColor,
                modifier = Modifier.padding(top = 25.dp),
                style = MaterialTheme.typography.h4
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(fraction = 0.85f)
                    .padding(top = 10.dp)
                    .padding(bottom = 20.dp),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(fraction = 0.3f)
                        .fillMaxHeight(fraction = 0.9f)
                        .border(
                            1.dp,
                            MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                            RoundedCornerShape(8.dp)
                        )
                        .clip(RoundedCornerShape(8.dp))
                        .background(MaterialTheme.colors.onSurface.copy(alpha = 0.03f)),
                    verticalArrangement = Arrangement.spacedBy(
                        10.dp,
                        alignment = Alignment.CenterVertically
                    ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = if (use != null) "${useMax?.minus(use ?: 0)}" else "",
                        color = useColor,
                        style = MaterialTheme.typography.h2.copy(
                            fontWeight = FontWeight.Bold,
                            textDecoration = textDecoration
                        )
                    )
                    Text(
                        text = "剩余次数",
                        color = textColor,
                        style = MaterialTheme.typography.h4.copy(textDecoration = textDecoration)
                    )
                    Text(
                        text = if (useMax != null) "(共 $useMax 次)" else "",
                        color = useColor,
                        style = MaterialTheme.typography.h5.copy(textDecoration = textDecoration)
                    )
                }
                TicketConsumeView(
                    modifier = Modifier
                        .fillMaxWidth(fraction = 0.7f)
                        .fillMaxHeight(fraction = 0.9f)
                )
            }
            Text(
                text = "请扫描车票二维码乘车",
                style = MaterialTheme.typography.h6
            )
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(fraction = 0.5f)
                .padding(horizontal = 30.dp)
                .padding(bottom = 30.dp)
                .border(
                    1.dp,
                    MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    RoundedCornerShape(20.dp)
                )
                .clip(RoundedCornerShape(20.dp))
                .background(MaterialTheme.colors.onSurface.copy(alpha = 0.03f)),
            contentAlignment = Alignment.Center
        ) {
            WaitView()
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(fraction = 0.9f)
                .padding(horizontal = 30.dp)
//                .padding(bottom = 30.dp)
                .border(
                    1.dp,
                    MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    RoundedCornerShape(20.dp)
                )
                .clip(RoundedCornerShape(20.dp))
                .background(MaterialTheme.colors.onSurface.copy(alpha = 0.03f)),
            contentAlignment = Alignment.Center
        ) {
            DriverView()
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(),
            contentAlignment = Alignment.Center
        ) {

            val calendar = Calendar.getInstance(Locale.ENGLISH)
            calendar.timeInMillis = CURRENT_LOCATION?.gps_time?: 0 * 1000L
            val date = DateFormat.format("yyyy-MM-dd HH:mm:ss",calendar).toString()

            Text(
                text = "GPS: (${CURRENT_LOCATION?.longitude},${CURRENT_LOCATION?.latitude}) ${date}",
                style = MaterialTheme.typography.caption
            )
        }

    }

    if (SHOW_DIALOG_FOR_LOGIN) {
        Dialog(onDismissRequest = { SHOW_DIALOG_FOR_LOGIN = false }) {
            LoginView()
        }
    }

    if (SHOW_DIALOG_FOR_ROUTES) {
        Dialog(onDismissRequest = { SHOW_DIALOG_FOR_ROUTES = false }) {
            RoutesView()
        }
    }

    if (SHOW_DEBUGVIEW) {
        DebugView()
    }
}

@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    CarssaTheme {
        Text("Android")
    }
}
