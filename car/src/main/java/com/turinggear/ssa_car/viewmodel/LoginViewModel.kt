package com.turinggear.ssa_car.viewmodel

import androidx.lifecycle.ViewModel
import com.turinggear.ssa_shared.util.KtorClient
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.Login
import com.turinggear.ssa_shared.model.ResponseMsg
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import kotlinx.serialization.decodeFromString

class LoginViewModel : ViewModel() {

    suspend fun loginWith(phone: String): String {
        return try {
            val x: Login = KtorClient.httpClient().post(urlString = apiCarLogin()) {
                parameter("phone", phone)
            }.body()
            if (x.code == 200) {
                x.data?.let { data ->
                    data.phone?.let {
                        DRIVER_LOGIN_PHONE = it
                    }
                    data.real_name?.let {
                        DRIVER_LOGIN_NAME = it
                    }
                    data.id?.let {
                        DRIVER_LOGIN_ID = it
                    }
                }
                "签到成功"
            } else {
                x.msg
            }
        } catch (e: ClientRequestException) {
            try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            e.localizedMessage ?: "签到失败"
        }
    }
}