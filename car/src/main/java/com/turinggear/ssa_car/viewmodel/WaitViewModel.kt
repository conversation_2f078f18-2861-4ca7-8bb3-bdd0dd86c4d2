package com.turinggear.ssa_car.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_shared.util.KtorClient
import com.turinggear.ssa_shared.apiCarWaiting
import com.turinggear.ssa_shared.model.ResponseWait
import com.turinggear.ssa_shared.model.Wait
import io.ktor.client.call.*
import io.ktor.client.request.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class WaitViewModel : ViewModel() {

    val waitsFlow = MutableStateFlow<List<Wait>?>(null)

    private suspend fun request(): ResponseWait {
        return KtorClient.httpClient().use {
            it.get(urlString = apiCarWaiting()).body()
        }
    }

    fun update() {
        viewModelScope.launch {
            kotlin.runCatching {
                request()
            }.onSuccess { responseWait ->
                responseWait.data?.let { list ->
                    waitsFlow.value = list.sortedBy { it.sort_order }
                }
            }.onFailure {
                print(it)
            }
        }
    }

    init {
        update()
    }
}