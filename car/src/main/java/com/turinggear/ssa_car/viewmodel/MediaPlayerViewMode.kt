package com.turinggear.ssa_car.viewmodel

import android.content.Context
import android.media.MediaPlayer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch

class MediaPlayerViewModel : ViewModel() {
    private var currentMediaPlayer: MediaPlayer? = null

    fun play(context: Context, resId: Int) {
        viewModelScope.launch {
            currentMediaPlayer?.let {
                if (it.isPlaying) {
                    it.stop()
                    it.release()
                }
            }
            currentMediaPlayer = MediaPlayer.create(context, resId).apply {
                start()
            }
        }
    }

    fun stop() {
        currentMediaPlayer?.let {
            if (it.isPlaying) {
                it.stop()
                it.release()
            }
            currentMediaPlayer = null
        }
    }
}