package com.turinggear.ssa_car.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_shared.util.KtorClient
import com.turinggear.ssa_shared.apiCarRoutes
import io.ktor.client.call.*
import io.ktor.client.request.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

@Serializable
data class ResponseRoute(val code: Int, val msg: String, val data: List<Route>? = null)

@Serializable
data class Route(val id: Int, val name: String)

class RoutesViewModel : ViewModel() {

    val routesFlow = MutableStateFlow<List<Route>?>(null)

    private suspend fun request(): ResponseRoute {
        return KtorClient.httpClient().use {
            it.get(urlString = apiCarRoutes()).body()
        }
    }

    init {
        viewModelScope.launch {
            kotlin.runCatching {
                request()
            }.onSuccess {
                routesFlow.value = it.data
            }.onFailure {
                print(it)
            }
        }
    }
}