package com.turinggear.ssa_car.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_shared.util.KtorClient
import com.turinggear.ssa_shared.apiCarWaitingDirection
import com.turinggear.ssa_shared.model.ResponseWaitDirection
import io.ktor.client.call.*
import io.ktor.client.request.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.jsonObject

class WaitDirectionModel : ViewModel() {

    val waitsDirectionFlow = MutableStateFlow<String?>(null)

    private suspend fun request(): ResponseWaitDirection {
        return KtorClient.httpClient().use {
            it.get(urlString = apiCarWaitingDirection()).body()
        }
    }

    fun update() {
        viewModelScope.launch {
            kotlin.runCatching {
                request()
            }.onSuccess { response ->
                var string = response.data.jsonObject["waiting"].toString()
                string = string.replace("{", "( ")
                string = string.replace("}", "  )")
                string = string.replace("\"", "")
                string = string.replace(",", ",  ")
                string = string.replace(":", ": ")
                waitsDirectionFlow.value = string
            }.onFailure {
                print(it)
            }
        }
    }

    init {
        update()
    }
}