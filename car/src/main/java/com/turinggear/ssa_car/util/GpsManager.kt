package com.turinggear.ssa_car.service

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.location.GpsStatus
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import androidx.core.app.ActivityCompat

// 为了结合broadcast使用,系统的定位状态
interface GpsDeviceStatusListener {
    fun onStatusChanged(newStatus: Int)
}

const val GPS_FIX = 1 // 设置中启用的子状态,有信号
const val GPS_NO_FIX = 2 // 设置中启用的子状态
//const val GPS_ENABLED = 3 // 设置中启用
const val GPS_DISABLED = 4 // 设置中禁用
//const val GPS_MALFUNCTION = 5 // 故障
const val GPS_UNKNOWN = 10 // 未知/不关心

// TODO:
//  平均速度
// 注意: 高版本用gnnsstatus,我们为低版本
class GpsManager : GpsStatus.Listener, LocationListener {
    val TAGS = "GpsListener"

    // gps更新触发条件,距离或者时间
    val MIN_DISTANCE_CHANGE_FOR_UPDATES = 10.0f; // 10 meters
    val MIN_TIME_BW_UPDATES = 1000 * 10L; // 10s

//    val GPS_LOST_THREHOLE = 5 * MIN_TIME_BW_UPDATES
//    var timeDriftToGPSTime = 0L // 注意gps时间和系统时间很可能不一致

    private lateinit var context: Context
    private lateinit var lm: LocationManager

    private var gpsDeviceStatusListener: GpsDeviceStatusListener? = null

    fun init(context: Context, gpsDeviceStatusListener: GpsDeviceStatusListener) {
        this.context = context
        this.lm = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager;
        this.gpsDeviceStatusListener = gpsDeviceStatusListener

        // 启动时候上报一次状态
        if (isPermissionGranted() && isGpsAvailable()) {
            this.gpsDeviceStatusListener?.onStatusChanged(GPS_NO_FIX)
        } else {
            this.gpsDeviceStatusListener?.onStatusChanged(GPS_DISABLED)
        }
    }

    fun isInitiated(): Boolean {
        return this::context.isInitialized
    }

    fun isPermissionGranted(): Boolean {
        return ActivityCompat.checkSelfPermission(
                this.context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
    }

    fun isGpsAvailable(): Boolean {
        return lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
    }

    // android系统的listener
    @SuppressLint("MissingPermission")
    fun addListeners() {
        // 不加虚拟机会挂
        if (!isPermissionGranted()) {
            return
        }
        // 接收设置gps启用禁用事件,以及第一次获取到位置
        // 注意该回调无论gps打开关闭都要加上
        lm.requestLocationUpdates(LocationManager.GPS_PROVIDER, MIN_TIME_BW_UPDATES, MIN_DISTANCE_CHANGE_FOR_UPDATES, this, this.context.mainLooper);

        // 获取gps卫星和是否fix
//        if (isPermissionGranted() && isGpsAvailable()) {
            this.lm.addGpsStatusListener(this)
//        }
    }

    fun removeListeners() {
        lm.removeUpdates(this)
        lm.removeGpsStatusListener(this)
    }

    @SuppressLint("MissingPermission")
    fun getLocation(): Location? {
        return lm.getLastKnownLocation(LocationManager.GPS_PROVIDER)
    }

    fun getSatellites(): Pair<Int, Int> {
        return Pair(satellitesInFix, satellites)
    }

    // gps listener
    var satellites = 0;
    var satellitesInFix = 0;

    @SuppressLint("MissingPermission")
    override fun onGpsStatusChanged(event: Int) {
        // 每次重设后查询
        satellites = 0
        satellitesInFix = 0
        for (sat in lm.getGpsStatus(null)?.getSatellites()!!) {
            if(sat.usedInFix()) {
                satellitesInFix++;
            }
            satellites++;
        }
//        Log.e(TAGS, "gps status: sat ${satellitesInFix}/${satellites}")

        // TODO: 增加gps是否正常检测,不仅依据卫星个数,gps time 长时间未更新
        if (satellitesInFix > 0) {
            gpsDeviceStatusListener?.onStatusChanged(GPS_FIX)
        } else {
            gpsDeviceStatusListener?.onStatusChanged(GPS_NO_FIX)
        }

        if (event == GpsStatus.GPS_EVENT_FIRST_FIX) {
//            Log.e(TAGS, "fix")
            gpsDeviceStatusListener?.onStatusChanged(GPS_FIX)

        } else if (event == GpsStatus.GPS_EVENT_SATELLITE_STATUS) {
//            Log.e(TAGS, "loop")

        } else if (event == GpsStatus.GPS_EVENT_STARTED) {
//            Log.e(TAGS, "start")

        } else if (event == GpsStatus.GPS_EVENT_STOPPED) {
//            Log.e(TAGS, "stop")
            gpsDeviceStatusListener?.onStatusChanged(GPS_NO_FIX)
        }
    }

    // location listener
    override fun onLocationChanged(location: Location) {
        Log.e(TAGS, "location ${location.latitude} ${location.longitude}")
    }

    override fun onProviderEnabled(provider: String) {
        // 注意不要调用super,会报错
        gpsDeviceStatusListener?.onStatusChanged(GPS_NO_FIX)
    }

    override fun onProviderDisabled(provider: String) {
        // 注意不要调用super,会报错
        gpsDeviceStatusListener?.onStatusChanged(GPS_DISABLED)
    }

    override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {
    }
}