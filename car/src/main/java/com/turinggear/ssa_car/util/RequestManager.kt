package com.turinggear.ssa_car.util

import android.annotation.SuppressLint
import android.os.Build
import com.turinggear.ssa_car.model.LocationEvent
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.*
import com.turinggear.ssa_shared.util.KtorClient
import com.turinggear.ssa_shared.util.TicketQrcodeParser
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.http.content.*
import kotlinx.serialization.decodeFromString

object RequestManager {

    suspend fun logout(phone: String): Pair<Int, String> {
        return try {
            val x: ResponseMsg = KtorClient.httpClient().delete(urlString = apiCarLogout()) {
                parameter("phone", phone)
            }.body()
            if (x.code == 200) {
                Pair(200, x.msg)
            } else {
                Pair(x.code, x.msg)
            }
        } catch (e: ClientRequestException) {
            try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                Pair(-1, "${responseMsg.code}: ${responseMsg.msg}")
            } catch (e: Exception) {
                Pair(-1, e.localizedMessage ?: "签退失败")
            }
        } catch (e: Exception) {
            Pair(-1, e.localizedMessage ?: "签退失败")
        }
    }

    @SuppressLint("HardwareIds")
    @Suppress("DEPRECATION")
    suspend fun registerDevice() {
        val random = (1000..9999).random()
        try {
            val x: Register = KtorClient.httpClient(withAuth = false)
                .post(urlString = apiRegisterMachine()) {
                    contentType(ContentType.Application.Json)
                    setBody(BodyForRegister(
                        serial = Build.SERIAL,
                        type = 50,
                        random = "$random"
                    ))
                }.body()
            HasTriedToRegisterAfterLaunched = true
            if (x.data != "已注册") {
                RANDOM_REGISTER_CODE = "$random"
                SHOW_ALERT_FOR_REGISTER = true
            }
        } catch (e: ClientRequestException) {
            REGISTER_ERROR_STRING = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            //SHOW_ALERT_FOR_REGISTER = true
        } catch (e: Exception) {
            REGISTER_ERROR_STRING = e.localizedMessage
            //SHOW_ALERT_FOR_REGISTER = true
        }
    }

    suspend fun consumeTicketWith(barcode: String): String? {
        RESPONSE_CONSUME = null
//        val array = barcode.split(".")
//        val ticketId = if (array.count() > 2) array[2] else ""
//        if (ticketId.isEmpty()) {
//            return "验票失败（二维码无效）"
//        }
        val parsed = TicketQrcodeParser.parse(barcode)
        if (parsed == null) {
            return "验票失败（二维码无效）"
        }
        val identifier = parsed.getIdentifier()
        var msg: String? = null
        try {
            val x: Consume = KtorClient.httpClient()
                .put(urlString = "${apiCarConsume()}/$identifier").body()
            RESPONSE_CONSUME = x
        } catch (e: ClientRequestException) {
            msg = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            msg = e.localizedMessage
        }
        return msg
    }

    suspend fun startup(version_code: Int): AppVersion? {
        return try {
            STARTUP = KtorClient.httpClient().post(urlString = apiCarStartup()) {
                setBody(TextContent(
                    text = "{\"version_code\": $version_code}",
                    contentType = ContentType.Application.Json
                ))
            }.body()
            STARTUP?.data?.appVersion
        } catch (e: Exception) {
            null
        }
    }

    suspend fun me(): CarMachineResponse {
        val resp: CarMachineResponse = KtorClient.httpClient().get(urlString = apiCarMe()).body()
        CURRENT_CAR = resp.msg

        DRIVER_LOGIN_ID = resp.msg?.driver?.id ?: 0
        DRIVER_LOGIN_NAME = resp.msg?.driver?.real_name ?: ""
        DRIVER_LOGIN_PHONE = resp.msg?.driver?.phone ?: ""
        return resp
    }

    suspend fun reportEvents(data: LocationEvent): Pair<Int, String> {
        return try {
            val x: ResponseMsg = KtorClient.httpClient().post(urlString = apiCarReportEvents()) {
                contentType(ContentType.Application.Json)
                setBody(data)
            }.body()
            if (x.code == 200) {
                Pair(200, x.msg)
            } else {
                Pair(x.code, x.msg)
            }
        } catch (e: Exception) {
            Pair(-1, e.localizedMessage ?: "fff")
        }
    }

    suspend fun changeBindingRoute(id: Int): Pair<Int, String> {
        return try {
            val x: ResponseMsg = KtorClient.httpClient().put(urlString = "${apiCarRoutesChange()}/$id").body()
            if (x.code == 200) {
                Pair(200, x.msg)
            } else {
                Pair(x.code, x.msg)
            }
        } catch (e: ClientRequestException) {
            try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                Pair(-1, "${responseMsg.code}: ${responseMsg.msg}")
            } catch (e: Exception) {
                Pair(-1, e.localizedMessage ?: "切换线路失败")
            }
        } catch (e: Exception) {
            Pair(-1, e.localizedMessage ?: "切换线路失败")
        }
    }
}
