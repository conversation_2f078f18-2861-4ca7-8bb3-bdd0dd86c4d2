package com.turinggear.ssa_car.util

import android.content.Context
import android.content.Intent
import com.turinggear.ssa_car.hw.SDKUtil
import kotlinx.coroutines.delay

interface IHardwareManager {
    fun init()
    fun deinit()

    suspend fun flashGreenLight()

    fun fullScreen()

    fun exitFullScreen()
}

class HardwareManager : IHardwareManager {
    private lateinit var mSdkUtil: SDKUtil
    private var ctx: Context

    constructor(ctx: Context)
    {
        this.ctx = ctx
    }

    override fun init()
    {
        this.mSdkUtil = SDKUtil.getInstance(ctx);
        //BOTH,补光灯和绿灯可以同时亮；EITHER，某一时刻，补光灯和绿灯只能亮其中某一种,默认为BOTH
//        this.mSdkUtil.setLightMode(SDKUtil.BOTH);
    }

    override fun deinit()
    {
        //关闭自然光摄像头
//        closeRGBCamera();
        //关闭红外光摄像头
//        closeIRCamera();
        //程序关闭时，随之释放SDK中的IC卡和微波读取线程。
        mSdkUtil.release();
    }

    override suspend fun flashGreenLight()
    {
        this.ctx.sendBroadcast(Intent("com.android.intent.OpenGreenFlashlight"))
        delay(500)
        this.ctx.sendBroadcast(Intent("com.android.intent.CloseGreenFlashlight"))
    }

    override fun fullScreen()
    {
        this.ctx.sendBroadcast(Intent("com.android.intent.DisableStatusbar"))
        this.ctx.sendBroadcast(Intent("com.android.intent.DisableNavigation"))

    }

    override fun exitFullScreen()
    {
        this.ctx.sendBroadcast(Intent("com.android.intent.EnableStatusbar"))
        this.ctx.sendBroadcast(Intent("com.android.intent.EnableNavigation"))

    }
}

public class DummyHardwareManager: IHardwareManager {
    constructor(ctx: Context)
    {
    }

    override fun init() {
    }

    override fun deinit()
    {
    }

    override suspend fun flashGreenLight() {
    }

    override fun fullScreen() {
    }

    override fun exitFullScreen() {
    }
}