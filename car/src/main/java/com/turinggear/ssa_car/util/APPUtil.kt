package com.turinggear.ssa_car.util

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import com.turinggear.ssa_car.service.GuardService

class APPUtil {
    companion object
    {
        public fun startLocalGuardService(ctx: Context)
        {
            ctx.startService(Intent(ctx, GuardService::class.java));
        }

        public fun startRemoteGuardService(ctx: Context) {
            val i = Intent();
            i.setComponent(
                ComponentName(
                    "com.turinggear.ssa_console",
                    "com.turinggear.ssa_console.service.GuardService"
                )
            );
            ctx.startService(i);
        }

//        public fun startGuardService(ctx: Context)
//        {
//            val i = Intent();
//            i.setComponent(ComponentName("com.turinggear.ssa_console", "com.turinggear.ssa_console.GuardService"));
//            ctx.startService(i);
//        }
//
//        public fun stopGuardService(ctx: Context)
//        {
//            val STOP_SERVICE = "com.turinggear.ssa_console.STOP_GUARD"
//            val intent = Intent();
//            intent.setAction(STOP_SERVICE);
//            ctx.sendBroadcast(intent);
//        }
    }
}