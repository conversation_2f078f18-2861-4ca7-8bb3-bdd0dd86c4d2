package com.turinggear.ssa_car.service

import android.app.Service
import android.content.Intent
import android.os.*
import android.telephony.CarrierConfigManager.Gps
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.turinggear.ssa_car.BuildConfig
import com.turinggear.ssa_car.model.LocationEvent
import com.turinggear.ssa_car.util.RequestManager
import com.turinggear.ssa_shared.CURRENT_LOCATION
import com.turinggear.ssa_shared.STARTUP
import io.ktor.client.call.*
import kotlinx.coroutines.*
import java.lang.Runnable

class ScheduleService : Service() {

    val TAGS = "ScheduleService"

    // 事件上报间隔默认值, 注意只从startup取一次动态配置, 轮询不能开关
//    val REPORT_TASK_INTERVAL = 1*60*1000L // 1min
    val REPORT_TASK_INTERVAL = 10*1000L // 10s

    val handler = Handler(Looper.getMainLooper());

    var isEventReportTaskStarted = false

    var gpsManager: GpsManager = GpsManager()

    private val job = SupervisorJob()
    private val ioScope = CoroutineScope(Dispatchers.IO + job)

    // 无限循环
    val eventReportTask = object : Runnable {
        override fun run() {
            // 未初始化无操作
            if (!gpsManager.isInitiated()) {
                handler.postDelayed(this, REPORT_TASK_INTERVAL)
                return
            }

            val data: LocationEvent

            if (!gpsManager.isPermissionGranted() || !gpsManager.isGpsAvailable()) {
                // 未启用则无数据
                data = LocationEvent(
                    version_code = BuildConfig.VERSION_CODE,
                    gps_status = GPS_DISABLED,
                )
            } else {
                // 启用则获取数据
                val location = gpsManager.getLocation()
                val sat = gpsManager.getSatellites()

                // TODO: 判断是否fix,注意这里应该和gpsmanager内一致
                val status: Int
                if (sat.first == 0) {
                    status = GPS_NO_FIX
                } else {
                    status = GPS_FIX
                }

                data = LocationEvent(
                    version_code = BuildConfig.VERSION_CODE,
                    gps_status = status,
                    speed = location?.speed?.toString().orEmpty(),
                    longitude = location?.longitude?.toString().orEmpty(),
                    latitude = location?.latitude?.toString().orEmpty(),
                    altitude = location?.altitude?.toString().orEmpty(),
                    velocity = location?.speed?.toString().orEmpty(),
                    acceleration = "0",
                    bearing = location?.bearing?.toString().orEmpty(),
                    satellites_used = sat.first ?: 0,
                    satellites_count = sat.second?: 0,
                    gps_time = location?.time ?: 0,
                )
            }
//            Log.e(TAGS, data.toString()) //dbg

            // 为了定时准确不能阻塞
//            runBlocking {
                val launch = <EMAIL> {
                    if (data.latitude.isNotEmpty() && data.longitude.isNotEmpty()) {
                        val resp = RequestManager.reportEvents(data)
                    } else {
//                        Log.e(TAGS, "skip report") //dbg
                    }
                }
//                launch.join()
//            }

            // 默认禁用
            if (STARTUP?.data?.machineInfo?.report_location_enabled == 0) {
                Log.e(TAGS, "report_location_enabled 0")
            } else {
                val delay: Long
                if (compareValues(STARTUP?.data?.machineInfo?.report_location_interval, 0) >= 0) {
                    // 有效值
                    delay = STARTUP?.data?.machineInfo?.report_location_interval!!
//                    Log.e(TAGS, "report_location_delay ${delay}")
                } else {
                    delay = REPORT_TASK_INTERVAL
                }

                CURRENT_LOCATION = data

                handler.postDelayed(this, delay)
            }
        }
    }

    private var previousGpsStatus: Int = GPS_UNKNOWN

    fun startGpsManager() {
        gpsManager.init(this, object : GpsDeviceStatusListener {
            override fun onStatusChanged(newStatus: Int) {
//                Log.e(TAGS, "${previousGpsStatus} to ${newStatus}")
                if (newStatus != previousGpsStatus) {
//                    Log.e(TAGS, "${previousGpsStatus} to ${newStatus} send bc")
                    Intent().also { intent ->
                        intent.setAction("com.turinggear.ssa_car.bc.GPS_DEVICE_STATUS")
                        intent.putExtra("status", newStatus)
                        sendBroadcast(intent)
                    }

                    previousGpsStatus = newStatus
                }
            }
        })

        gpsManager.addListeners()
    }
    override fun onCreate() {
        super.onCreate()

        startGpsManager()
    }

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        job.cancel()

        gpsManager.removeListeners()

        handler.removeCallbacks(eventReportTask);
        stopSelf();

        super.onDestroy()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val delay = intent?.getLongExtra("delay", REPORT_TASK_INTERVAL)?: REPORT_TASK_INTERVAL
        if (!isEventReportTaskStarted) {
            // 首次启动无限循环, 延迟启动
            handler.postDelayed(eventReportTask, delay)
            isEventReportTaskStarted = true
        } else {
            // 之后为手动插入任务
            handler.post(Runnable {
//                Log.e(TAGS, "one-shot task")
            })
        }

        return super.onStartCommand(intent, flags, startId)
    }
}