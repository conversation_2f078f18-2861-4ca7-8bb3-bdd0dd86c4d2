package com.turinggear.ssa_car.service

import android.app.Service
import android.content.Intent
import android.os.*
import android.util.Log
import com.turinggear.ssa_car.util.RequestManager
import kotlinx.coroutines.*
import java.lang.Runnable

class PollingService : Service() {

    val TAGS = "PollingService"

    val TASK_INTERVAL = 10*1000L // 10s

    val handler = Handler(Looper.getMainLooper());

    private val job = SupervisorJob()
    private val ioScope = CoroutineScope(Dispatchers.IO + job)

    // 无限循环
    val pollingTask = object : Runnable {
        override fun run() {
            val launch = <EMAIL> {
                try {
                    val resp = RequestManager.me()
//                    Log.i(TAGS, resp.toString())
                } catch (e: Exception) {
                    Log.e(TAGS, e.localizedMessage)
                }
            }

            handler.postDelayed(this, TASK_INTERVAL)
        }
    }

    override fun onCreate() {
        super.onCreate()
    }

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        job.cancel()

        handler.removeCallbacks(pollingTask);
        stopSelf();

        super.onDestroy()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 首次启动无限循环
        handler.post(pollingTask)

        return super.onStartCommand(intent, flags, startId)
    }
}