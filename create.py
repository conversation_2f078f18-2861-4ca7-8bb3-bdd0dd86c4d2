#!/usr/bin/env python3

import os


def append(filepath, s):
    with open(filepath, "a+") as f:
        f.write(s)

def overwrite(filepath, s):
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, "w+") as f:
        f.write(s)

def add_flavor(filepath, flavor_name, app_name, signing_config=None):
    with open(filepath, "r") as file:
        lines = file.readlines()

    stub_index = next(i for i, line in enumerate(lines) if "// add flavor here" in line)
    new_flavor = f"""
        {flavor_name} {{
            dimension = "version"
            resValue "string", "app_name", "{app_name}"
    """
    if signing_config is not None:
        new_flavor += f"\n            signingConfig signingConfigs.{signing_config}"
    new_flavor += "\n            }\n"

    lines.insert(stub_index, new_flavor)

    with open(filepath, "w") as file:
        file.writelines(lines)


if __name__ == "__main__":
    park_name = "lihuboat"
    cn_name="蠡湖智能游船"
    api_url="https://ssa-api-lihuboat.daohezhixing.com"
    oss_url="https://ssa-static-lihuboat.daohezhixing.com"
    kiosk_app_name=f"{cn_name}/大屏"
    car_app_name=""
    #car_app_name=f"{cn_name}/车载"
    handheld_app_name=f"{cn_name}/手持"
    board="rk3288"
    pay_platform=10

    makefile_lines=f"""
{park_name}:
\t../gradlew assemble{park_name.capitalize()}
    """
    append("default.mk", makefile_lines)

    handheld_scenic_config = f"""<?xml version="1.0" encoding="utf-8"?>
    <resources>
        <string name="api_endpoint">{api_url}</string>
        <string name="default_rules_ticket">1. 票当日有效，核销后不退款。\n2. 当日未核销过的，联系客服电话，后台系统原路退款。\n3. 过了当日未核销的不退款。</string>
        <string name="app_title">{cn_name}智能手持终端</string>
        <string name="receipt_title">购票凭条</string>
        <integer name="pay_platform">{pay_platform}</integer>
    </resources>
    """
    overwrite(
        f"handheld/src/{park_name}/res/values/scene_area_config.xml", handheld_scenic_config
    )

    if car_app_name:
        car_scenic_config = f"""<?xml version="1.0" encoding="utf-8"?>
        <resources>
            <string name="api_endpoint">{api_url}</string>
            <string name="oss_url">{oss_url}</string>
            <string name="default_rules_ticket">1. 票当日有效，核销后不退款。\n2. 当日未核销过的，联系客服电话，后台系统原路退款。\n3. 过了当日未核销的不退款。</string>
            <string name="default_rules_passenger">1. 游客乘坐必须遵守秩序，服从工作人员管理。\n 2.
            严格按规定定员乘坐，不得超载，以确保安全。\n 3.1.3米以下儿童必须有成人陪护，不得单独乘坐。 \n 4. 凡患心脏病、高血压及酗酒者谢绝乘坐。\n 5. 严禁乘客携带易燃、易爆等危险品乘坐车上禁止吸烟。\n 6.文明旅游，不得打闹，以免发生危险。\n 7. 自觉维护上、下车秩序。\n 8. 请自觉保持车/船内清洁卫生。\n 9. 本票据仅限当日有效，购票当日持本票据客可在园区指定位置，按规定乘坐。</string>
        </resources>
        """
        overwrite(
            f"car/src/{park_name}/res/values/scene_area_config.xml", car_scenic_config
        )
        add_flavor("car/build.gradle", park_name, car_app_name)

    kiosk_scenic_config = f"""<?xml version="1.0" encoding="utf-8"?>
    <resources>
        <string name="api_endpoint">{api_url}</string>
        <string name="oss_url">{oss_url}</string>
        <string name="default_rules_ticket">1. 票当日有效，核销后不退款。\n2. 当日未核销过的，联系客服电话，后台系统原路退款。\n3. 过了当日未核销的不退款。</string>
        <string name="default_rules_passenger">1. 游客乘坐必须遵守秩序，服从工作人员管理。\n 2.
        严格按规定定员乘坐，不得超载，以确保安全。\n 3.1.3米以下儿童必须有成人陪护，不得单独乘坐。 \n 4. 凡患心脏病、高血压及酗酒者谢绝乘坐。\n 5. 严禁乘客携带易燃、易爆等危险品乘坐车上禁止吸烟。\n 6.文明旅游，不得打闹，以免发生危险。\n 7. 自觉维护上、下车秩序。\n 8. 请自觉保持车/船内清洁卫生。\n 9. 本票据仅限当日有效，购票当日持本票据客可在园区指定位置，按规定乘坐。</string>
        <string name="receipt_title">购票凭条</string>
        <string name="weather_api">https://devapi.qweather.com/v7/weather/now?key=9973561b0c8241359933101072a05c6d&amp;location=116.644009,40.421239</string>
        <integer name="pay_platform">{pay_platform}</integer>
    </resources>
    """
    overwrite(
        f"kiosk/src/{park_name}/res/values/scene_area_config.xml", kiosk_scenic_config
    )

    add_flavor("kiosk/build.gradle", park_name, kiosk_app_name, signing_config=board)
    add_flavor("handheld/build.gradle", park_name, handheld_app_name)

    print("其他工作:")
    print("- 修改./Makefile")
    print("- 大屏用图片 res/drawable")
    print("- 大屏用语音 res/raw")
