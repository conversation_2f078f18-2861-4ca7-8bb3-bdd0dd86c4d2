package com.turinggear.ssa_kiosk

import android.util.Log
import com.turinggear.ssa_kiosk.util.RequestManager
import kotlinx.coroutines.*
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import org.junit.After
import org.junit.Test

import org.junit.Assert.*
import org.junit.Before

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@ExperimentalCoroutinesApi
class RequestTest {
//    val dispatcher = TestCoroutineDispatcher()
    private val dispatcher = newSingleThreadContext("UI thread")

    @Before
    fun setup() {
        Dispatchers.setMain(dispatcher)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
        dispatcher.close()
    }

    @Test
    fun testStartup(): Unit = runBlocking {
        launch(Dispatchers.Main) {  // Will be launched in the mainThreadSurrogate dispatcher
            val resp = RequestManager.startup(1, "kiosk1");
            if (resp != null) {
                println(resp)
            } else {
                println("null")
            }
        }
    }
}