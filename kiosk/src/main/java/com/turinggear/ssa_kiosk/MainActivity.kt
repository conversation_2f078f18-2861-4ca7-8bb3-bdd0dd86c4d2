package com.turinggear.ssa_kiosk

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import android.widget.Toast.LENGTH_LONG
import android.widget.Toast.LENGTH_SHORT
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Snackbar
import androidx.compose.material.SnackbarHost
import androidx.compose.material.Text
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toComposeRect
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.window.layout.WindowMetricsCalculator
import coil.compose.AsyncImage
import com.turinggear.ssa_kiosk.service.ScheduleService
import com.turinggear.ssa_kiosk.ui.MainViewLandscape
import com.turinggear.ssa_kiosk.ui.MainViewPortrait
import com.turinggear.ssa_kiosk.ui.RestartCountdownDialog
import com.turinggear.ssa_kiosk.ui.ScreensaverView
import com.turinggear.ssa_kiosk.ui.ScreensaverViewNew
import com.turinggear.ssa_kiosk.ui.theme.KioskTheme
import com.turinggear.ssa_kiosk.util.APPUtil
import com.turinggear.ssa_kiosk.util.AppReceiver
import com.turinggear.ssa_kiosk.util.DeviceAdminManager
import com.turinggear.ssa_kiosk.util.MediaManager
import com.turinggear.ssa_kiosk.util.PrinterManager
import com.turinggear.ssa_kiosk.util.RequestManager
import com.turinggear.ssa_kiosk.viewmodel.StartupViewModel
import com.turinggear.ssa_shared.API_BASE
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.BARCODE
import com.turinggear.ssa_shared.CurrentVideoUrlForScreensaver
import com.turinggear.ssa_shared.FIRST_STATION_FOR_SELECTED_GOOD
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.HasTriedToRegisterAfterLaunched
import com.turinggear.ssa_shared.IsPortrait
import com.turinggear.ssa_shared.LAST_STATION_FOR_SELECTED_GOOD
import com.turinggear.ssa_shared.NetworkConnectedCount
import com.turinggear.ssa_shared.PAYMENTSTATE
import com.turinggear.ssa_shared.PAYMENT_STATE
import com.turinggear.ssa_shared.RANDOM_REGISTER_CODE
import com.turinggear.ssa_shared.REGISTER_ERROR_STRING
import com.turinggear.ssa_shared.SCAFFOLD_STATE
import com.turinggear.ssa_shared.SCANTYPE
import com.turinggear.ssa_shared.SCAN_TYPE
import com.turinggear.ssa_shared.SELECTED_AMOUNT
import com.turinggear.ssa_shared.SELECTED_DIRECTION_STATION_ID
import com.turinggear.ssa_shared.SELECTED_GOOD
import com.turinggear.ssa_shared.SHOW_ALERT_FOR_REGISTER
import com.turinggear.ssa_shared.SHOW_POPUP
import com.turinggear.ssa_shared.STARTUP
import com.turinggear.ssa_shared.StatusCodeFromBaidu
import com.turinggear.ssa_shared.StatusCodeFromHealthApi
import com.turinggear.ssa_shared.ui.RegisterAlertDialog
import com.turinggear.ssa_shared.util.KtorClient
import com.turinggear.ssa_shared.util.TicketQrcode
import com.turinggear.ssa_shared.util.TicketQrcodeParser
import com.turinggear.ssa_shared.util.cleanScannedQrcode
import com.turinggear.ssa_shared.util.isValidCodeForOldFormat
import com.turinggear.ssa_shared.util.OSUtils
import com.turinggear.ssa_shared.util.UpgradeUtil
import com.turinggear.ssa_shared.util.downloadFile
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import com.turinggear.ssa_shared.R
import kotlinx.coroutines.Job


@Suppress("DEPRECATION")
class MainActivity : ComponentActivity() {
    private lateinit var mDeviceAdminManager: DeviceAdminManager

    private val mainVM: StartupViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ApplicationContext = this.applicationContext
        API_BASE = this.applicationContext?.getString(R.string.api_endpoint) ?: ""
        SCAN_TYPE = SCANTYPE.CALL_CAR

        val appReceiver = AppReceiver(this, lifecycleScope)

        // 必须在检查版本静默安装之前执行
        mDeviceAdminManager = DeviceAdminManager(this)
        if (mDeviceAdminManager.isOwner()) {
            mDeviceAdminManager.setKioskMode(true) //必须放到onCreate()中
        }

        if (BuildConfig.DEBUG)
        {
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
        }

//        checkConnection(this, url = apiHealthCheck(), Lifecycle.State.CREATED, stopOnConnected = true) {
//            when (it) {
//                // 初始
//                ConnectionState.CONNECTED -> {
//                    Log.e("ConnectionChecker", "Connected")
//                    IsNetworkConnected = true
//                }
//                // 重连
//                ConnectionState.RECONNECTED -> {
//                    Log.e("ConnectionChecker", "ReConnected")
//                    finish()
//                    startActivity(this.intent)
//                    IsNetworkConnected = true
//                }
//                // 断开
//                else -> {
//                    Log.e("ConnectionChecker", it.toString())
//                    IsNetworkConnected = false
//                }
//            }
//        }

        // 其他打印机相关调用之前运行
        PrinterManager.init(this)

        // 监控USB插拔
        val intentFilter = IntentFilter("com.android.example.USB_PERMISSION")
        intentFilter.addAction("android.hardware.usb.action.USB_DEVICE_ATTACHED")
        intentFilter.addAction("android.hardware.usb.action.USB_DEVICE_DETACHED")
        application.registerReceiver(appReceiver, intentFilter)
        /*
         * 监控本地网络连接情况
         * 注意仅是本地连接情况，比如接入了无网络的路由器，也属于接入成功，但无法连接外网
         * 联机外网的情况通过 baidu.com 和 /api/health 两层进行判断
         */
        application.registerReceiver(appReceiver, IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION))
        // 打印机相关
        lifecycleScope.launch {
            delay(2000)
            PrinterManager.updateAllPrinterStatus(application = application)
        }

        // zc-339a
        // 隐藏的api,从下边链接发现的
        // [flutter_hardware_interaction/FlutterHardwareInteractionPlugin.java at 596283fc346c49017f68d2cbd6c487a5ae0023c6 · hisan-web/flutter_hardware_interaction](https://github.com/hisan-web/flutter_hardware_interaction/blob/596283fc346c49017f68d2cbd6c487a5ae0023c6/android/src/main/java/com/hisanweb/flutter_hardware_interaction/FlutterHardwareInteractionPlugin.java#L74)
        this.sendBroadcast(Intent("hide.systemui"))
        this.sendBroadcast(Intent("com.zc.close_gesture"))

//        Log.e("ssa", "serial " + SerialNumber.originalSerial())


        val enableRouteDirection = this.resources.getBoolean(R.bool.enable_route_direction)
        setContent {
            KioskTheme {
                MainView(mainVM, activity = this)
                if (SHOW_ALERT_FOR_REGISTER) {
                    RegisterAlertDialog(
                        modifier = Modifier.fillMaxWidth(fraction = 0.75f),
                        code = RANDOM_REGISTER_CODE,
                        error = REGISTER_ERROR_STRING
                    )
                }
                LaunchedEffect(Unit) {
                    // 先测试一次,避免启动就重加载
                    StatusCodeFromBaidu = RequestManager.requestInternet()
                    if (StatusCodeFromBaidu.toString().startsWith("2")) {
                        NetworkConnectedCount += 1
                    }

                    while (true) {
                        val poll = RequestManager.poll()
                        poll?.let {
                            GLOBAL_POLL = it
                        }
                        StatusCodeFromBaidu = RequestManager.requestInternet()
                        if (StatusCodeFromBaidu.toString().startsWith("2")) {
                            NetworkConnectedCount += 1
                            if (NetworkConnectedCount == 1) {
                                // 第一次连通外网时重启一次 activity
                                Log.e("XXX", "relaunch")
//                                finish()
//                                startActivity(intent)
                                <EMAIL>()
                            }
                        }
                        StatusCodeFromHealthApi = RequestManager.requestHealthApi()
                        delay(3000)
                        if (enableRouteDirection && SHOW_POPUP) {
                            if (FIRST_STATION_FOR_SELECTED_GOOD == null && LAST_STATION_FOR_SELECTED_GOOD == null) {
                                RequestManager.requestFirstAndLastStations()
                            }
                        }
                    }
                }
            }
        }

        // 注册设备
        if (!HasTriedToRegisterAfterLaunched) {
            lifecycleScope.launch {
                RequestManager.registerDevice()
            }
        }

        // 更新版本
        if (OSUtils.isConsoleAppInstalled(this)) {
            Log.e("XXX", "console app found")
            // 如果守护App存在则开启与守护App的通信
            APPUtil.startLocalGuardService(this)
            APPUtil.startRemoteGuardService(this)
        }
        updateVersion(OSUtils.checkRootStatus())

        // 启动定时循环任务, 如检查全部打印机
        val intent = Intent(this, ScheduleService::class.java)
        intent.putExtra("cmd", "printer_check_and_report_loop")
        this.startService(intent)

        val enableVideoSound =
            this.resources.getBoolean(R.bool.enable_video_sound)
        if (enableVideoSound) {
            // 禁用本身欢迎语音
            MediaManager.mute()
        }

//        mainVM.onShowRebootDialog() //debug
    }

    private fun updateVersion(isSilent: Boolean) {
        lifecycleScope.launch {
            var appVersion = STARTUP?.data?.appVersion
            if (appVersion == null) {
                appVersion = RequestManager.startup(BuildConfig.VERSION_CODE)
//                appVersion = RequestManager.startup(1) //debug
            }
            appVersion?.let {
                if (!it.down_url.endsWith(".apk", ignoreCase = true)) {
                    return@let
                }
                val message = "开始到版本(${it.code})的更新"
                Toast.makeText(applicationContext, message, LENGTH_LONG).show()
                delay(1000)

                Log.e("XXX", "downloading")
                val url = it.down_url

                var ret = false

                try {
                    ret = UpgradeUtil.download(this@MainActivity, packageName, url)
                    Log.e("XXX", "downloaded")
                    if (ret) {
                        if (isSilent) {
//                            ret = UpgradeUtil.installByPM(this@MainActivity, packageName)
                            ret = UpgradeUtil.installAsSystemApp(this@MainActivity, packageName)
                        } else {
                            ret = UpgradeUtil.installInteractively(this@MainActivity, packageName)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("XXX", "error ${e.localizedMessage}")
                    Toast.makeText(this@MainActivity, e.localizedMessage, LENGTH_SHORT).show()

                    ret = false
                }
                Log.e("XXX", "download result $ret")
                if (ret) {
                    Toast.makeText(this@MainActivity, "更新成功", LENGTH_SHORT).show()

                    // 仅当静默安装,特别的安装为系统app需要重启
                    if(isSilent) {
                        mainVM.onShowRebootDialog()
                    }
                }
            }
        }
    }

    // 监控扫码
    private var sequenceString = ""

    @SuppressLint("RestrictedApi")
    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.action != KeyEvent.ACTION_UP) {
            sequenceString += event.unicodeChar.toChar()
            return true
        }
        var triggered = event.keyCode == KeyEvent.KEYCODE_ENTER
        if (BuildConfig.DEBUG && OSUtils.isEmulator()) {
            // 发现在电脑的AVD模拟器无法用enter/USB扫码头等触发, 所以用z键
            triggered = event.keyCode == KeyEvent.KEYCODE_Z
            sequenceString = "cashruleseverythingaroundme" // 任意字符过检测 模拟付款
//            sequenceString = "T-2q3kGgrA36CbAJF1UWRYwA8iM" // 模拟叫车
//            sequenceString = "2024121114401632664730693" // 模拟补票
        }
        if (triggered) {

            BARCODE = cleanScannedQrcode(sequenceString)
            sequenceString = ""

            if (!isValidCodeForOldFormat(BARCODE)) {
                Toast.makeText(applicationContext, "请出示正确的二维码或条码", LENGTH_LONG).show()
                return true
            }

            val enableRouteDirection = this.resources.getBoolean(R.bool.enable_route_direction)

            // FOR 叫车
            if (BARCODE.isNotEmpty() and (SCAN_TYPE == SCANTYPE.CALL_CAR)) {
//                val array = BARCODE.split(".")
//                val ticketId = if (array.count() > 2) array[2] else ""
                val parsed = TicketQrcodeParser.parse(BARCODE)
                if (parsed != null) {
                    val identifier = parsed.getIdentifier()
                    lifecycleScope.launch {
                        if (enableRouteDirection) {
                            // TODO
                            RequestManager.callCarFrom(identifier)
                            RequestManager.requestFirstAndLastStationsForCallCarWith(ticketId = identifier)
                        } else {
                            val responseString = RequestManager.callCarFrom(identifier)
                            responseString?.let {
                                val message = it
                                SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                                SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(message)
                            }
                        }
                    }
                } else {
                    Toast.makeText(applicationContext, "请检查车票二维码", LENGTH_LONG).show()
                }
            }
            // FOR 微信支付
            if (BARCODE.isNotEmpty()
                and (SCAN_TYPE == SCANTYPE.WECHAT_PAY)
                and ((PAYMENT_STATE == PAYMENTSTATE.UNSTARTED) or (PAYMENT_STATE == PAYMENTSTATE.USER_TAPPED) or (PAYMENT_STATE == PAYMENTSTATE.FAILED))
            ) {
                lifecycleScope.launch {
                    var pay_playform = applicationContext?.resources?.getInteger(R.integer.pay_platform) ?: 10
                    // 这里按照需求使用 默认调试模式 用现金支付
                    if (BuildConfig.DEBUG) {
                        pay_playform = 30 // cash
                    }
                    val qrcodeFormat = applicationContext?.resources?.getInteger(R.integer.ticket_qrcode_format)?: TicketQrcode.FORMAT_ID
                    SELECTED_GOOD?.let {
                        if (enableRouteDirection) {
                            if (!SELECTED_DIRECTION_STATION_ID.isNullOrEmpty()) {
                                RequestManager.wechatPayWith(BARCODE, it.id, SELECTED_AMOUNT, pay_playform, qrcodeFormat)
                            }
                        } else {
                            RequestManager.wechatPayWith(BARCODE, it.id, SELECTED_AMOUNT, pay_playform, qrcodeFormat)
                        }
                    }
                }
                return true // 不触发屏幕按键
            }
            // FOR 补打小票
            if (BARCODE.isNotEmpty()
                and (SCAN_TYPE == SCANTYPE.REPRINT_TICKET)
            ) {
                lifecycleScope.launch {
                    RequestManager.requestTicketsWith(orderNo = BARCODE)
                }
                return true // 不触发屏幕按键
            }
        }
        return super.dispatchKeyEvent(event)
    }

    override fun onBackPressed() {
        //super.onBackPressed() //注释掉以禁用系统返回键
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
    }
}

@Composable
fun MainView(vm: StartupViewModel = viewModel(), activity: Activity) {
//    val vm: StartupViewModel = viewModel()
    STARTUP = vm.startupFlow.collectAsState().value

    val showDialog = vm.showRebootDialog

    val configuration = LocalConfiguration.current
    val windowMetrics = remember(configuration) {
        WindowMetricsCalculator.getOrCreate()
            .computeCurrentWindowMetrics(activity)
    }
    val windowDpSize = with(LocalDensity.current) {
        windowMetrics.bounds.toComposeRect().size.toDpSize()
    }
    IsPortrait = windowDpSize.width < windowDpSize.height
    val scaffoldState = rememberScaffoldState()
    Scaffold(
        scaffoldState = scaffoldState,
        snackbarHost = {
            SnackbarHost(
                hostState = it,
                snackbar = { data ->
                    Snackbar(
                        modifier = Modifier
                            .padding(20.dp)
                            .clickable { it.currentSnackbarData?.dismiss() },
                        backgroundColor = COLOR_WARNING
                    ) {
                        Text(
                            text = data.message,
                            modifier = Modifier.padding(20.dp),
                            style = MaterialTheme.typography.h5
                        )
                    }
                })
        },
    ) {
        if (IsPortrait) {
            MainViewPortrait()
        } else {
            MainViewLandscape()
        }
        RestartCountdownDialog(
            initialCountdownSeconds = STARTUP?.data?.scenicAreaConfig?.app_reboot_delay ?: 15,

            showDialog = showDialog,
            onDismiss = { vm.onDismissRebootDialog() },
            onRestartNow = {
                vm.onDismissRebootDialog()
                Toast.makeText(activity, "正在立即重启设备...", Toast.LENGTH_SHORT).show()
                OSUtils.reboot()
            },
            onCancelRestart = {
                vm.onDismissRebootDialog()
                Toast.makeText(activity, "重启已取消", Toast.LENGTH_SHORT).show()
            },
            onTimeout = {
                vm.onDismissRebootDialog()
                Toast.makeText(activity, "倒计时结束，正在重启设备...", Toast.LENGTH_LONG).show()
                OSUtils.reboot()
            }
        )

        val shouldShowScreensaver =
            (GLOBAL_POLL?.data?.machineInfo?.status == 20) || (GLOBAL_POLL?.data?.stationInfo?.status == 20)
        if (shouldShowScreensaver) {
            val screenSaverUrl =
                if (IsPortrait) GLOBAL_POLL?.data?.stationInfo?.screen_saver_vertical else GLOBAL_POLL?.data?.stationInfo?.screen_saver_transverse
            screenSaverUrl?.let { url ->
                if (url.isNotEmpty()) {
                    if (url.lowercase().contains(".mp4") ||
                        url.lowercase().contains(".m4a") ||
                        url.lowercase().contains(".webm") ||
                        url.lowercase().contains(".flv")
                    ) {
                        val fileName = "screensaver_" + url.split("/").last()
                        val file = File(ApplicationContext?.filesDir, fileName)
                        if (file.exists()) {
                            ScreensaverViewNew(videoUrl = file.toString())
                        } else {
                            if (CurrentVideoUrlForScreensaver != url) {
                                CurrentVideoUrlForScreensaver = url
                                ScreensaverViewNew(videoUrl = url)
                            } else {
                                ScreensaverView(videoUrl = url)
                            }
                            LaunchedEffect(Unit) {
                                ApplicationContext?.filesDir?.walk()?.forEach { f ->
                                    if (f.name.startsWith("screensaver_")) {
                                        f.delete()
                                    }
                                }
                                ApplicationContext?.let {
                                    try {
                                        KtorClient.httpClient().downloadFile(file, url) { success ->
                                            if (success) {
                                                CurrentVideoUrlForScreensaver = "" // 刷新一次界面用缓存
                                            }
                                        }
                                    } catch (e: Exception) {
                                        print(e.localizedMessage)
                                    }
                                }
                            }
                        }
                    } else {
                        CurrentVideoUrlForScreensaver = ""
                        AsyncImage(
                            model = url,
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .clickable(enabled = false, onClick = { })
                                .fillMaxSize()
                        )
                    }
                } else {
                    CurrentVideoUrlForScreensaver = ""
                }
            }
        } else {
            CurrentVideoUrlForScreensaver = ""
        }
    }
    SCAFFOLD_STATE = scaffoldState
}