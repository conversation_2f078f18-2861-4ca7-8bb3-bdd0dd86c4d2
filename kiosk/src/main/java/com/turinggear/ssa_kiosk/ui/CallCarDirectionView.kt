package com.turinggear.ssa_kiosk.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.util.RequestManager
import com.turinggear.ssa_shared.*
import kotlinx.coroutines.launch
import com.turinggear.ssa_shared.R

@Composable
fun CallCarDirectionView() {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    val enableRouteDirection = context.resources.getBoolean(R.bool.enable_route_direction)
    Box(
        modifier = Modifier
            .clickable(enabled = false, onClick = { })
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.8f)),
        contentAlignment = Alignment.Center
    ) {
        Popup(alignment = Alignment.Center, onDismissRequest = { }) {
            Column(
                modifier = Modifier
                    .width(320.dp)
                    .border(
                        1.dp,
                        MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                        RoundedCornerShape(10.dp)
                    )
                    .clip(RoundedCornerShape(10.dp))
                    .background(MaterialTheme.colors.background)
                    .aspectRatio(1.3f),
                horizontalAlignment = Alignment.Start
            ) {
                Box(
                    contentAlignment = Alignment.TopEnd
                ) {
                    Column(
                        Modifier
                            .clickable {
                                FIRST_STATION_FOR_CALL_CAR = null
                                LAST_STATION_FOR_CALL_CAR = null
                            }
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp)
                            .padding(top = 30.dp, bottom = 20.dp),
                        verticalArrangement = Arrangement.spacedBy(10.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        val title = "叫车"
                        Text(
                            text = title,
                            color = MaterialTheme.colors.onBackground.copy(0.5f),
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.body1
                        )
                    }
                    TextButton(
                        onClick = {
                            FIRST_STATION_FOR_CALL_CAR = null
                            LAST_STATION_FOR_CALL_CAR = null
                        },
                        modifier = Modifier
                            .padding(all = 5.dp)
                            .size(36.dp)
                            .aspectRatio(1f),
                        colors = ButtonDefaults.textButtonColors(backgroundColor = Color.Transparent),
                        contentPadding = PaddingValues(all = 0.dp)
                    ) {
                        Icon(
                            Icons.Outlined.Close,
                            contentDescription = null,
                            modifier = Modifier.size(30.dp),
                            tint = MaterialTheme.colors.onBackground.copy(0.5f)
                        )
                    }
                }
                Divider(thickness = 0.5.dp)
                Column(
                    Modifier
                        .weight(6f)
                        .padding(vertical = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(
                        15.dp,
                        alignment = Alignment.CenterVertically
                    )
                ) {
                    val startPadding = 55.dp

                    if (enableRouteDirection) {
                        FIRST_STATION_FOR_CALL_CAR?.let { firstStation ->
                            LAST_STATION_FOR_CALL_CAR?.let { lastStation ->
                                if (firstStation.id == CURRENT_STATION_ID) {
                                    SELECTED_DIRECTION_STATION_ID = lastStation.id.toString()
                                }
                                if (lastStation.id == CURRENT_STATION_ID) {
                                    SELECTED_DIRECTION_STATION_ID = firstStation.id.toString()
                                }
                                Row(
                                    Modifier.padding(start = startPadding, end = 10.dp),
                                    verticalAlignment = Alignment.Top
                                ) {
                                    Text(
                                        text = "方向：",
                                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                                        style = MaterialTheme.typography.h6
                                    )
                                    Column(modifier = Modifier.padding(start = 5.dp)) {
                                        Row(
                                            Modifier
                                                .fillMaxWidth()
                                                .height(30.dp)
                                                .selectable(
                                                    selected = true,
                                                    enabled = (firstStation.id != CURRENT_STATION_ID),
                                                    onClick = {
                                                        scope.launch {
                                                            val responseString =
                                                                RequestManager.directionRecord(
                                                                    stationId = firstStation.id.toString(),
                                                                    ticket_id = SCANNED_TICKET_ID_CALL_CAR
                                                                )
                                                            responseString?.let {
                                                                val message = it
                                                                SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                                                                SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(
                                                                    message
                                                                )
                                                            }
                                                            FIRST_STATION_FOR_CALL_CAR = null
                                                            LAST_STATION_FOR_CALL_CAR = null
                                                        }
                                                    }
                                                ),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            RadioButton(
                                                selected = (SELECTED_DIRECTION_STATION_ID == firstStation.id.toString()),
                                                enabled = (firstStation.id != CURRENT_STATION_ID),
                                                onClick = {
                                                    scope.launch {
                                                        val responseString =
                                                            RequestManager.directionRecord(
                                                                stationId = firstStation.id.toString(),
                                                                ticket_id = SCANNED_TICKET_ID_CALL_CAR
                                                            )
                                                        responseString?.let {
                                                            val message = it
                                                            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                                                            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(
                                                                message
                                                            )
                                                        }
                                                        FIRST_STATION_FOR_CALL_CAR = null
                                                        LAST_STATION_FOR_CALL_CAR = null
                                                    }
                                                },
                                                colors = RadioButtonDefaults.colors(
                                                    disabledColor = MaterialTheme.colors.onSurface.copy(
                                                        alpha = 0.2f
                                                    )
                                                )
                                            )
                                            val color = if (firstStation.id == CURRENT_STATION_ID) {
                                                MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
                                            } else {
                                                Color.Unspecified
                                            }
                                            Text(
                                                text = firstStation.name + if (firstStation.id == CURRENT_STATION_ID) " (本站)" else "",
                                                color = color,
                                                style = MaterialTheme.typography.caption
                                            )
                                        }
                                        Row(
                                            Modifier
                                                .fillMaxWidth()
                                                .height(30.dp)
                                                .selectable(
                                                    selected = true,
                                                    enabled = (lastStation.id != CURRENT_STATION_ID),
                                                    onClick = {
                                                        scope.launch {
                                                            val responseString =
                                                                RequestManager.directionRecord(
                                                                    stationId = lastStation.id.toString(),
                                                                    ticket_id = SCANNED_TICKET_ID_CALL_CAR
                                                                )
                                                            responseString?.let {
                                                                val message = it
                                                                SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                                                                SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(
                                                                    message
                                                                )
                                                            }
                                                            FIRST_STATION_FOR_CALL_CAR = null
                                                            LAST_STATION_FOR_CALL_CAR = null
                                                        }
                                                    }
                                                ),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            RadioButton(
                                                selected = (SELECTED_DIRECTION_STATION_ID == lastStation.id.toString()),
                                                enabled = (lastStation.id != CURRENT_STATION_ID),
                                                onClick = {
                                                    scope.launch {
                                                        val responseString =
                                                            RequestManager.directionRecord(
                                                                stationId = lastStation.id.toString(),
                                                                ticket_id = SCANNED_TICKET_ID_CALL_CAR
                                                            )
                                                        responseString?.let {
                                                            val message = it
                                                            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                                                            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(
                                                                message
                                                            )
                                                        }
                                                        FIRST_STATION_FOR_CALL_CAR = null
                                                        LAST_STATION_FOR_CALL_CAR = null
                                                    }
                                                },
                                                colors = RadioButtonDefaults.colors(
                                                    disabledColor = MaterialTheme.colors.onSurface.copy(
                                                        alpha = 0.2f
                                                    )
                                                )
                                            )
                                            val color = if (lastStation.id == CURRENT_STATION_ID) {
                                                MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
                                            } else {
                                                Color.Unspecified
                                            }
                                            Text(
                                                text = lastStation.name + if (lastStation.id == CURRENT_STATION_ID) " (本站)" else "",
                                                color = color,
                                                style = MaterialTheme.typography.caption
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                Column(Modifier.weight(2f)) { }
            }
        }
    }
}