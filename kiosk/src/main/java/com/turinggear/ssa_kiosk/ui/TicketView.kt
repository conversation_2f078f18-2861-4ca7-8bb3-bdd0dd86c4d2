package com.turinggear.ssa_kiosk.ui

import android.app.Application
import android.content.Context
import android.media.MediaPlayer
import android.widget.ImageView
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.FormatListNumbered
import androidx.compose.material.icons.outlined.LocalActivity
import androidx.compose.material.icons.outlined.MarkChatRead
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import coil.compose.rememberAsyncImagePainter
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.DEFAULT_RULES_TICKET
import com.turinggear.ssa_kiosk.MARGIN
import com.turinggear.ssa_kiosk.util.MediaManager
import com.turinggear.ssa_kiosk.util.PrinterManager
import com.turinggear.ssa_kiosk.util.RequestManager
import com.turinggear.ssa_kiosk.viewmodel.GoodViewModel
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.ui.TicketCard
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.turinggear.ssa_shared.R

@Composable
fun TicketViewLandscape() {
    Column(
        Modifier
            .fillMaxHeight()
            .fillMaxWidth(fraction = 1 / 3f)
    ) {
        // 购票流程
        TicketStepView(
            Modifier
                .weight(1f)
                .padding(horizontal = MARGIN.dp)
                .padding(top = 10.dp)
        )
        Divider(
            Modifier.padding(horizontal = MARGIN.dp, vertical = 2.dp),
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
            thickness = 0.5.dp
        )
        // 乘车购票
        TicketHeader()
        TicketBuyView(
            Modifier
                .weight(3.9f)
                .padding(horizontal = 16.dp)
                .padding(vertical = 10.dp)
        )
        PrinterErrorText(Modifier.padding(horizontal = (MARGIN + 4).dp), fontSize = 8.sp)
        Divider(
            Modifier
                .padding(horizontal = MARGIN.dp)
                .padding(top = 10.dp, bottom = 5.dp),
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
            thickness = 0.5.dp
        )
        // 购票须知
        NoticeForTicket(
            Modifier
                //.weight(1.9f)
                .padding(horizontal = MARGIN.dp)
                .padding(bottom = 4.dp)
        )
    }
}


@Composable
fun TicketViewPortrait() {
    Column(
        Modifier
            .fillMaxWidth()
            .fillMaxHeight(fraction = 0.6f)
    ) {
        TicketHeader(Modifier.fillMaxWidth(fraction = 0.7f))
        Row(Modifier.fillMaxSize()) {
            TicketBuyView(
                Modifier
                    .fillMaxWidth(fraction = 0.7f)
                    .padding(16.dp)
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 8.dp, end = 16.dp, bottom = 8.dp)
                    .background(Color.Transparent),
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                TicketStepView()
                Divider(
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    thickness = 0.5.dp
                )
                NoticeForTicket()
                Divider(
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    thickness = 0.5.dp
                )
                val fontSize = 10.sp
                Text(
                    text = "软件版本：${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                    modifier = Modifier
                        .pointerInput(Unit) {
                            detectTapGestures(
                                onDoubleTap = { SHOW_DEBUGVIEW = true },
//                                onTap = {
//                                    if (BuildConfig.DEBUG) {
//                                        if (ColorPaletteIndex < 2) {
//                                            ColorPaletteIndex += 1
//                                        } else {
//                                            ColorPaletteIndex = 0
//                                        }
//                                    }
//                                }
                            )
                        }
                        .padding(horizontal = 0.dp)
                        .padding(top = 7.dp),
                    color = MaterialTheme.colors.onBackground.copy(0.6f),
                    fontSize = fontSize
                )
                PrinterErrorText(
                    modifier = Modifier
                        .padding(horizontal = 0.dp)
                        .padding(bottom = 6.dp),
                    fontSize = fontSize,
                    horizontalArrangement = Arrangement.Start
                )
            }
        }
    }
}

// 山海关特供
@Composable
fun TicketViewPortraitShg() {
    Column(
        Modifier
            .fillMaxWidth()
            .fillMaxHeight(fraction = 0.45f)
    ) {
        TicketHeader(Modifier.fillMaxWidth(fraction = 0.7f))
        Row(Modifier.fillMaxSize()) {
            TicketBuyViewShg(
                Modifier
                    .fillMaxWidth(fraction = 0.7f)
                    .padding(16.dp)
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 16.dp, end = 16.dp, bottom = 8.dp)
                    .background(Color.Transparent),
//                verticalArrangement = Arrangement.SpaceEvenly
            ) {
//                TicketStepView()
                Divider(
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    thickness = 0.5.dp
                )
                NoticeForTicket()
                Divider(
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    thickness = 0.5.dp
                )

                val fontSize = 10.sp
                //这里就是一个占位
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(20.dp)
                        .background(Color.White)
                ) {

                }
                Text(
                    text = "软件版本：${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                    modifier = Modifier
                        .pointerInput(Unit) {
                            detectTapGestures(
                                onDoubleTap = { SHOW_DEBUGVIEW = true },
//                                onTap = {
//                                    if (BuildConfig.DEBUG) {
//                                        if (ColorPaletteIndex < 2) {
//                                            ColorPaletteIndex += 1
//                                        } else {
//                                            ColorPaletteIndex = 0
//                                        }
//                                    }
//                                }
                            )
                        }
                        .padding(horizontal = 0.dp)
                        .padding(top = 7.dp),
                    color = MaterialTheme.colors.onBackground.copy(0.6f),
                    fontSize = fontSize
                )
                //这里就是一个占位
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(10.dp)
                        .background(Color.White)
                ) {

                }
                PrinterErrorText(
                    modifier = Modifier
                        .padding(horizontal = 0.dp)
                        .padding(bottom = 6.dp),
                    fontSize = fontSize,
                    horizontalArrangement = Arrangement.Start
                )
            }

        }
    }
}


@Composable
fun TicketBuyView(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            GLOBAL_POLL?.data?.machineInfo?.station_id?.let {
                val goodViewModel: GoodViewModel = viewModel()
                val goodsFlow = goodViewModel.goodsFlow.collectAsState()
                goodViewModel.update()
                LaunchedEffect(Unit) {
                    while (true) {
                        delay(1000 * 3)
                        goodViewModel.update()
                    }
                }

                val scope = rememberCoroutineScope()
                val goods = goodsFlow.value ?: listOf()

                val distinctCategoryValues = goods.map { it.category }.distinct().sorted()
                val firstTwoCategories = distinctCategoryValues.take(2)

                val context = LocalContext.current
                var goodsForOne: List<Good> = listOf()
                var goodsForTwo: List<Good> = listOf()
                var titleOne = ""
                var titleOneAmount = ""
                var titleTwo = ""
                var titleTwoAmount = ""

                if (firstTwoCategories.size > 0) {
                    goodsForOne = goods.filter { it.category == firstTwoCategories.get(0) }
                    titleOne = GOOD_CATEGORY_MAP.get(goodsForOne.get(0).category)?: ""
                    titleOneAmount = "(共${goodsForOne.count()}种票)"
                }
                if (firstTwoCategories.size > 1) {
                    goodsForTwo = goods.filter { it.category == firstTwoCategories.get(1) }
                    titleTwo = GOOD_CATEGORY_MAP.get(goodsForTwo.get(0).category)?: ""
                    titleTwoAmount = "(共${goodsForTwo.count()}种票)"
                }

                val hideTicketRowTitle = context.resources.getBoolean(R.bool.hide_ticket_row_title) ?: false
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 6.dp)
                ) {

                        if (!hideTicketRowTitle) {
                        Row(
                            modifier = Modifier
                                .padding(horizontal = 6.dp)
                                .fillMaxHeight(fraction = 0.2f)
                                .fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = titleOne,
                                color = MaterialTheme.colors.onBackground.copy(0.9f),
                                style = MaterialTheme.typography.caption
                            )
                            Spacer(modifier = Modifier.weight(1.0f))
                            Text(
                                text = titleOneAmount,
                                color = MaterialTheme.colors.onBackground.copy(0.6f),
                                style = MaterialTheme.typography.overline
                            )
                        }
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .horizontalScroll(rememberScrollState()),
                        verticalAlignment = Alignment.CenterVertically
                    ) {

                        goodsForOne.forEachIndexed { _, good ->
                            TicketCard(
                                onClick = {
                                    scope.launch {
                                        onGoodClicked(context, good)
                                    }
                                },
                                Modifier
                                    .padding(horizontal = 6.dp)
                                    .padding(bottom = 12.dp)
                                    .clip(RoundedCornerShape(6.dp))
                                    .background(MaterialTheme.colors.surface),
                                good = good
                            )
                        }
                    }
                }
                Divider(
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    thickness = 0.5.dp
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 6.dp)
                ) {
                    if (!hideTicketRowTitle) {
                        Row(
                            modifier = Modifier
                                .padding(horizontal = 6.dp)
                                .fillMaxHeight(fraction = 0.2f)
                                .fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = titleTwo,
                                color = MaterialTheme.colors.onBackground.copy(0.9f),
                                style = MaterialTheme.typography.caption
                            )
                            Spacer(modifier = Modifier.weight(1.0f))
                            Text(
                                text = titleTwoAmount,
                                color = MaterialTheme.colors.onBackground.copy(0.6f),
                                style = MaterialTheme.typography.overline
                            )
                        }
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .horizontalScroll(rememberScrollState()),
                        verticalAlignment = Alignment.CenterVertically
                    ) {

                        goodsForTwo.forEachIndexed { _, good ->
                            TicketCard(
                                onClick = {
                                    scope.launch {
                                        onGoodClicked(context, good)
                                    }
                                },
                                Modifier
                                    .padding(horizontal = 6.dp)
                                    .padding(bottom = 12.dp)
                                    .clip(RoundedCornerShape(6.dp))
                                    .background(MaterialTheme.colors.surface),
                                good = good
                            )
                        }
                    }
                }
            }
        }
        if (LocalNetworkConnected == false) {
            NetworkErrorMask("设备未联网", "请检查设备网络连接情况")
        } else if ((StatusCodeFromBaidu != null) and !StatusCodeFromBaidu.toString()
                .startsWith("2")
        ) {
            NetworkErrorMask("网络连接异常", "请检查设备网络连接情况")
        } else if ((StatusCodeFromHealthApi != null) and !StatusCodeFromHealthApi.toString()
                .startsWith("2")
        ) {
            NetworkErrorMask("服务接口异常", "请联系技术人员")
        } else if ((PRINTER_1_STATUS != 24) and (PRINTER_2_STATUS != 24)) {
            PrinterErrorMask()
        } else if (IsClosingTime) {
            ClosingTimeMask()
        }
    }
}

suspend fun onGoodClicked(context: Context, good: Good) {
    val enableRouteDirection = context.resources.getBoolean(R.bool.enable_route_direction)
    PrinterManager.shouldUpdatePrinterStatus(application = context.applicationContext as Application)
    delay(PrinterManager.PRINTER_CHECK_TIME_MS)
    if ((PRINTER_1_STATUS == 24) or (PRINTER_2_STATUS == 24)) {
        SHOW_POPUP = true
        SCAN_TYPE = SCANTYPE.WECHAT_PAY
        SELECTED_GOOD = good
        if (enableRouteDirection) {
            SELECTED_DIRECTION_STATION_ID = null
            RequestManager.requestFirstAndLastStations()
        }

        MediaManager.playLoopingNotice(false)
        MediaManager.playMusic(context, R.raw.dian_ji_gou_piao)
    }
}

@Composable
fun TicketBuyViewShg(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .fillMaxSize()
            .padding(top = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            GLOBAL_POLL?.data?.machineInfo?.station_id?.let {
                val goodViewModel: GoodViewModel = viewModel()
                val goodsFlow = goodViewModel.goodsFlow.collectAsState()
                goodViewModel.update()
                LaunchedEffect(Unit) {
                    while (true) {
                        delay(1000 * 3)
                        goodViewModel.update()
                    }
                }

                val scope = rememberCoroutineScope()
                val goods = goodsFlow.value ?: listOf()

                val distinctCategoryValues = goods.map { it.category }.distinct().sorted()
                val firstTwoCategories = distinctCategoryValues.take(2)

                val context = LocalContext.current
                var goodsForOne: List<Good> = listOf()
                var goodsForTwo: List<Good> = listOf()
                var titleOne = ""
                var titleOneAmount = ""
                var titleTwo = ""
                var titleTwoAmount = ""

                if (firstTwoCategories.size > 0) {
                    goodsForOne = goods.filter { it.category == firstTwoCategories.get(0) }
                    titleOne = GOOD_CATEGORY_MAP.get(goodsForOne.get(0).category)?: ""
                    titleOneAmount = "(共${goodsForOne.count()}种票)"
                }
                if (firstTwoCategories.size > 1) {
                    goodsForTwo = goods.filter { it.category == firstTwoCategories.get(1) }
                    titleTwo = GOOD_CATEGORY_MAP.get(goodsForTwo.get(0).category)?: ""
                    titleTwoAmount = "(共${goodsForTwo.count()}种票)"
                }

                val hideTicketRowTitle = context.resources.getBoolean(R.bool.hide_ticket_row_title) ?: false
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 6.dp)
                ) {

                        if (!hideTicketRowTitle) {
                        Row(
                            modifier = Modifier
                                .padding(horizontal = 6.dp)
                                .fillMaxHeight(fraction = 0.2f)
                                .fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = titleOne,
                                color = MaterialTheme.colors.onBackground.copy(0.9f),
                                style = MaterialTheme.typography.caption
                            )
                            Spacer(modifier = Modifier.weight(1.0f))
                            Text(
                                text = titleOneAmount,
                                color = MaterialTheme.colors.onBackground.copy(0.6f),
                                style = MaterialTheme.typography.overline
                            )
                        }
                    }

                    Row {
                        Row(
                            modifier = Modifier
                                .fillMaxSize()
                                .weight(1f)
                                .horizontalScroll(rememberScrollState()),
                            verticalAlignment = Alignment.CenterVertically
                        ) {

                            goodsForOne.forEachIndexed { _, good ->
                                TicketCard(
                                    onClick = {
                                        scope.launch {
                                            onGoodClicked(context, good)
                                        }
                                    },
                                    Modifier
                                        .padding(horizontal = 6.dp)
                                        .padding(bottom = 12.dp)
                                        .clip(RoundedCornerShape(6.dp))
                                        .background(MaterialTheme.colors.surface),
                                    good = good
                                )
                            }
                        }
                        TicketStepView()
                    }
                }
                Divider(
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    thickness = 0.5.dp
                )
                Box(
                    modifier = Modifier
                    .weight(1f)){
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 6.dp)
                    ) {
                        if (!hideTicketRowTitle) {
                            Row(
                                modifier = Modifier
                                    .padding(horizontal = 6.dp)
                                    .fillMaxHeight(fraction = 0.2f)
                                    .fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = titleTwo,
                                    color = MaterialTheme.colors.onBackground.copy(0.9f),
                                    style = MaterialTheme.typography.caption
                                )
                                Spacer(modifier = Modifier.weight(1.0f))
                                Text(
                                    text = titleTwoAmount,
                                    color = MaterialTheme.colors.onBackground.copy(0.6f),
                                    style = MaterialTheme.typography.overline
                                )
                            }
                        }
                        Row(
                            modifier = Modifier
                                .fillMaxSize()
                                .horizontalScroll(rememberScrollState()),
                            verticalAlignment = Alignment.CenterVertically
                        ) {

                            goodsForTwo.forEachIndexed { _, good ->
                                TicketCard(
                                    onClick = {
                                        scope.launch {
                                            onGoodClicked(context, good)
                                        }
                                    },
                                    Modifier
                                        .padding(horizontal = 6.dp)
                                        .padding(bottom = 12.dp)
                                        .clip(RoundedCornerShape(6.dp))
                                        .background(MaterialTheme.colors.surface),
                                    good = good
                                )
                            }
                        }
                    }

                    var placeholder = R.drawable.map_2
                    var url = GLOBAL_POLL?.data?.machineStaticConfig?.map4_url?: ""
                    AsyncImage(
                        model = url,
                        placeholder = rememberAsyncImagePainter(placeholder),
                        error = rememberAsyncImagePainter(placeholder),
                        fallback = rememberAsyncImagePainter(placeholder),
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds
                    )

                }

            }
        }
        if (LocalNetworkConnected == false) {
            NetworkErrorMask("设备未联网", "请检查设备网络连接情况")
        } else if ((StatusCodeFromBaidu != null) and !StatusCodeFromBaidu.toString()
                .startsWith("2")
        ) {
            NetworkErrorMask("网络连接异常", "请检查设备网络连接情况")
        } else if ((StatusCodeFromHealthApi != null) and !StatusCodeFromHealthApi.toString()
                .startsWith("2")
        ) {
            NetworkErrorMask("服务接口异常", "请联系技术人员")
        } else if ((PRINTER_1_STATUS != 24) and (PRINTER_2_STATUS != 24)) {
            PrinterErrorMask()
        } else if (IsClosingTime) {
            ClosingTimeMask()
        }
    }
}

@Composable
fun TicketHeader(modifier: Modifier = Modifier) {
    Row(
        modifier.padding(start = 16.dp, top = 2.dp, end = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            Icons.Outlined.LocalActivity,
            contentDescription = null,
            modifier = Modifier
                .size(30.dp)
                .padding(top = 1.dp, end = 5.dp),
            tint = MaterialTheme.colors.onBackground.copy(0.8f)
        )
        Text(
            text = "购票",
            color = MaterialTheme.colors.onBackground.copy(0.9f),
            style = MaterialTheme.typography.h6
        )
        val context = LocalContext.current
        val disableCallcar = context.resources.getBoolean(R.bool.disable_callcar) ?: false
        val disableReissue = context.resources.getBoolean(R.bool.disable_reissue_ticket) ?: false

        if (!disableCallcar) {
            Text(
                text = "（多次票可扫码叫车）",
                color = MaterialTheme.colors.onBackground.copy(0.6f),
                style = MaterialTheme.typography.button
            )
        }
        if (!disableReissue) {
            Text(
                text = "补打小票",
                modifier = Modifier
                    .padding(start = 2.dp, top = 1.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(MaterialTheme.colors.surface)
                    .clickable(
                        enabled = (StatusCodeFromHealthApi
                            .toString()
                            .startsWith("2") and
                                ((PRINTER_1_STATUS == 24) or (PRINTER_2_STATUS == 24))),
                        onClick = { SHOW_REPRINT = true }
                    )
                    .padding(top = 3.dp, bottom = 4.dp, start = 6.dp, end = 6.dp),
                color = MaterialTheme.colors.onBackground.copy(0.7f),
                style = MaterialTheme.typography.overline
            )
        }
        Spacer(modifier = Modifier.weight(1.0f))
    }
}

// 购票流程
@Composable
fun TicketStepView(modifier: Modifier = Modifier) {
    Column(modifier = modifier, verticalArrangement = Arrangement.Center) {
        Row(
            modifier = Modifier
                .padding(end = 12.dp)
                .padding(bottom = if (IsPortrait) 12.dp else 10.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start
        ) {
            Icon(
                Icons.Outlined.FormatListNumbered,
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .padding(top = 1.dp, end = 4.dp),
                tint = MaterialTheme.colors.onBackground.copy(0.8f)
            )
            Text(
                text = "购票流程",
                color = MaterialTheme.colors.onBackground.copy(0.8f),
                style = MaterialTheme.typography.button
            )
        }
        if (IsPortrait) {
            Column(
                modifier = Modifier.padding(end = 12.dp, bottom = 10.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    NoticeText(text = "1. 点击购票", fontSize = 12.sp)
                    NoticeText(text = "2. 选择张数", fontSize = 12.sp)
                }
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    NoticeText(text = "3. 微信扫码支付", fontSize = 12.sp)
                    if (BuildConfig.FLAVOR != "waterpark") {
                        NoticeText(text = "4. 取票", fontSize = 12.sp)
                    }
                }
            }
        } else {
            Row(
                modifier = Modifier.padding(bottom = 10.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                NoticeText(text = "1. 点击购票", fontSize = 11.sp)
                NoticeText(text = "2. 选择购买张数", fontSize = 11.sp)
                NoticeText(text = "3. 微信扫码支付", fontSize = 11.sp)
                if (BuildConfig.FLAVOR != "waterpark") {
                    NoticeText(text = "4. 取票", fontSize = 11.sp)
                }
            }
        }
    }
}

// 购票须知
@Composable
fun NoticeForTicket(modifier: Modifier = Modifier) {
    Column(modifier = modifier, verticalArrangement = Arrangement.Top) {
        Row(
            modifier = Modifier
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start
        ) {
            Icon(
                Icons.Outlined.MarkChatRead,
                contentDescription = null,
                modifier = Modifier
                    .size(22.dp)
                    .padding(top = 2.dp, end = 4.dp),
                tint = MaterialTheme.colors.onBackground.copy(0.8f)
            )
            Text(
                text = "购票须知",
                color = MaterialTheme.colors.onBackground.copy(0.8f),
                style = MaterialTheme.typography.button
            )
        }
        val rule1 = GLOBAL_POLL?.data?.stationInfo?.ticket_rules
        val rule2 = GLOBAL_POLL?.data?.machineStaticConfig?.ticket_rules
        val ticketRules = when {
            !rule1.isNullOrEmpty() -> rule1
            !rule2.isNullOrEmpty() -> rule2
            !DEFAULT_RULES_TICKET.isNullOrEmpty() -> DEFAULT_RULES_TICKET
            else -> ""
        }

        Text(
            text = ticketRules,
            Modifier
                .padding(bottom = 8.dp),
            color = MaterialTheme.colors.onBackground.copy(0.8f),
            fontSize = if (IsPortrait) 10.sp else 8.sp,
            lineHeight = if (IsPortrait) 18.sp else 14.sp
        )
    }
}

@Composable
fun NoticeText(text: String, fontSize: TextUnit = 9.sp) {
    Box(
        Modifier
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(4.dp)
            )
    ) {
        Text(
            text = text,
            Modifier.padding(top = 4.dp, bottom = 5.dp, start = 7.dp, end = 8.5.dp),
            color = MaterialTheme.colors.onBackground.copy(0.8f),
            fontSize = fontSize,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}
