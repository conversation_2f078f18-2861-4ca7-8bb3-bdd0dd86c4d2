package com.turinggear.ssa_kiosk.ui

import android.content.Context
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.viewinterop.AndroidView
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.extractor.DefaultExtractorsFactory
import com.google.android.exoplayer2.source.ProgressiveMediaSource
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout
import com.google.android.exoplayer2.ui.StyledPlayerView
import com.google.android.exoplayer2.upstream.DataSource
import com.google.android.exoplayer2.upstream.DefaultBandwidthMeter
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource
import com.google.android.exoplayer2.upstream.FileDataSource
import com.google.android.exoplayer2.upstream.cache.CacheDataSink
import com.google.android.exoplayer2.upstream.cache.CacheDataSource
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor
import com.google.android.exoplayer2.upstream.cache.SimpleCache
import com.google.android.exoplayer2.util.Util
import java.io.File
import com.turinggear.ssa_shared.R

// 代码来自 [android - Using cache in ExoPlayer - Stack Overflow](https://stackoverflow.com/questions/28700391/using-cache-in-exoplayer)
// 注意使用singleton 对 SimpleCache防止被锁
// 缓存是否生效可以检查 du -s /data/data/com.turinggear.ssa_kiosk/cache/exo/

object VideoCache {
    private var sDownloadCache: SimpleCache? = null
//    private const val maxCacheSize: Long = 100 * 1024 * 1024

    fun getInstance(context: Context, maxCacheSize: Long): SimpleCache {
        val evictor = LeastRecentlyUsedCacheEvictor(maxCacheSize)
        if (sDownloadCache == null) sDownloadCache = SimpleCache(File(context.cacheDir, "exo"), evictor)
        return sDownloadCache as SimpleCache
    }
}

class CacheDataSourceFactory(
    private val context: Context,
    private val maxCacheSize: Long,
    private val maxFileSize: Long
) :
    DataSource.Factory {
    private val defaultDatasourceFactory: DefaultDataSourceFactory


    init {
        val userAgent: String = Util.getUserAgent(context, "kiosk")
        Util.getUserAgent(context, context.applicationContext.packageName)
        val bandwidthMeter = DefaultBandwidthMeter()
        defaultDatasourceFactory = DefaultDataSourceFactory(
            this.context,
            bandwidthMeter,
            DefaultHttpDataSource.Factory().setUserAgent(userAgent)
        )
    }

    override fun createDataSource(): DataSource {
//        val evictor = LeastRecentlyUsedCacheEvictor(maxCacheSize)
        val simpleCache: SimpleCache by lazy {
            VideoCache.getInstance(context, maxCacheSize)
        }
//        simpleCache = SimpleCache(File(context.cacheDir, "exoCache"), evictor)
        return CacheDataSource(
            simpleCache, defaultDatasourceFactory.createDataSource(),
            FileDataSource(), CacheDataSink(simpleCache, maxFileSize),
            CacheDataSource.FLAG_BLOCK_ON_CACHE or CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR, null
        )
    }
}

@Composable
fun ScreensaverView(videoUrl: String, modifier: Modifier = Modifier) {
    val context = LocalContext.current

    Box(modifier = modifier
        .fillMaxSize()
        .clickable(enabled = false, onClick = { })
    ) {
        val enableVideoSound =
            context.resources.getBoolean(R.bool.enable_video_sound)

        val playerView = StyledPlayerView(LocalContext.current)
        playerView.useController = false
        playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
        val player = ExoPlayer.Builder(LocalContext.current).build()
        player.repeatMode = ExoPlayer.REPEAT_MODE_ONE
//        player.setMediaItem(MediaItem.fromUri(videoUrl))
        if (enableVideoSound) {
            player.volume = 1f // Unmute the player
        } else {
            player.volume = 0f // Mute the player
        }
        playerView.player = player
        LaunchedEffect(player) {
            // 512MB
            val dataSourceFactory = CacheDataSourceFactory(context,
                512 * 1024 * 1024, 512 * 1024 * 1024)

            player.prepare(
                ProgressiveMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(MediaItem.fromUri(Uri.parse(videoUrl)))
            )
//            player.prepare()

            player.playWhenReady = true
        }
        AndroidView(
            factory = {
                playerView
            },
            modifier = Modifier
                .align(Alignment.Center)
                .background(colorResource(id = R.color.video_bg))
                .fillMaxSize()
        )
        DisposableEffect(Unit) {
            onDispose {
                player.stop()
            }
        }
    }
}

@Composable
fun ScreensaverViewNew(videoUrl: String) {
    ScreensaverView(videoUrl = videoUrl)
}