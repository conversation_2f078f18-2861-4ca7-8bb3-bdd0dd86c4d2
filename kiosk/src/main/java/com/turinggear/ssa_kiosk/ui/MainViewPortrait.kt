package com.turinggear.ssa_kiosk.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.R

@Composable
fun MainViewPortrait() {
    val context = LocalContext.current
    val enableLayoutShg = context.resources.getBoolean(R.bool.enable_layout_shg)
    val enableLayoutYqh = context.resources.getBoolean(R.bool.enable_layout_yqh)
    val enableVideo = context.resources.getBoolean(R.bool.enable_video)
    val enableReturnBoat = context.resources.getBoolean(R.bool.enable_shr_return_boat)
    val enableRouteDirection = context.resources.getBoolean(R.bool.enable_route_direction)
    Box(
        Modifier
            .fillMaxSize()
            .background(MaterialTheme.colors.background),
        contentAlignment = Alignment.Center
    ) {
        // 主界面
        Column(
            Modifier
                .fillMaxSize()
                .background(Color.Transparent),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleView()
            Divider(thickness = 0.5.dp)
            GuideViewPortrait()
            Divider(
                modifier = Modifier.padding(top = 8.dp, bottom = 16.dp),
                thickness = 0.5.dp
            )
            if (enableLayoutShg) {
                TicketViewPortraitShg()
            } else {
                TicketViewPortrait()
                Divider(
                    modifier = Modifier.padding(top = 16.dp),
                    thickness = 0.5.dp
                )
            }

            var videoUrl = GLOBAL_POLL?.data?.machineStaticConfig?.video4_url ?: ""
//            url = "https://daohezhixing-dl.oss-cn-beijing.aliyuncs.com/sh.mp4" // debug
            var shrReturnBoatUrl = GLOBAL_POLL?.data?.machineStaticConfig?.return_boat_url?: ""
            if (enableReturnBoat && shrReturnBoatUrl.isNotEmpty()) {
                QrcodeView(shrReturnBoatUrl)
            }
            else if (enableVideo && videoUrl.isNotEmpty()) {
//                ScreensaverView(url, Modifier.padding(horizontal = 16.dp)) //, vertical = 3.dp).clip(RoundedCornerShape(5.dp))
                ScreensaverView(videoUrl, Modifier.padding(horizontal = 16.dp, vertical = 3.dp).clip(RoundedCornerShape(5.dp)))
            } else {
                InfoMap4PortraitView()
            }
        }
        // 点击购票后的弹窗
        if (SHOW_POPUP and (SELECTED_GOOD != null)) {
            PopupView()
        }
        if (SHOW_REPRINT) {
            SCAN_TYPE = SCANTYPE.REPRINT_TICKET
            ReprintScreen()
        }
        if (SHOW_DEBUGVIEW) {
            DebugView()
        }
        if (enableRouteDirection) {
            if ((FIRST_STATION_FOR_CALL_CAR != null) and (LAST_STATION_FOR_CALL_CAR != null)) {
                CallCarDirectionView()
            }
        }
    }
}