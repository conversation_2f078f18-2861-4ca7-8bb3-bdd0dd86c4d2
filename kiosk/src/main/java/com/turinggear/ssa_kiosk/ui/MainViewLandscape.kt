package com.turinggear.ssa_kiosk.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.R

/**
 * -------------------------------------
 *               上方标题区
 * -------------------------------------
 * 左侧乘车购票区｜中间地图导览区｜右侧信息展示区
 * -------------------------------------
 */
@Composable
fun MainViewLandscape() {
    val context = LocalContext.current
    val enableRouteDirection = context.resources.getBoolean(R.bool.enable_route_direction)
    Box(
        Modifier
            .fillMaxSize()
            .background(MaterialTheme.colors.background),
        contentAlignment = Alignment.Center
    ) {
        // 主界面
        Column(
            Modifier
                .fillMaxSize()
                .background(Color.Transparent)
        ) {
            TitleView()
            Divider(thickness = 0.5.dp)
            Row {
                TicketViewLandscape()
                Divider(
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(0.5.dp),
                    thickness = 0.5.dp
                )
                GuideViewLandscape()
                Divider(
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(0.5.dp),
                    thickness = 0.5.dp
                )
                InfoViewLandscape()
            }
        }
        // 点击购票后的弹窗
        if (SHOW_POPUP and (SELECTED_GOOD != null)) {
            PopupView()
        }
        if (SHOW_REPRINT) {
            SCAN_TYPE = SCANTYPE.REPRINT_TICKET
            ReprintScreen()
        }
        if (SHOW_DEBUGVIEW) {
            DebugView()
        }
        if (enableRouteDirection) {
            if ((FIRST_STATION_FOR_CALL_CAR != null) and (LAST_STATION_FOR_CALL_CAR != null)) {
                CallCarDirectionView()
            }
        }
    }
}