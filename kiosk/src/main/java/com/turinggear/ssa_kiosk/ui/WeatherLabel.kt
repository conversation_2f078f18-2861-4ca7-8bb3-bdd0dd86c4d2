package com.turinggear.ssa_kiosk.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_kiosk.util.MediaManager
import com.turinggear.ssa_kiosk.viewmodel.WeatherViewModel
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.isClosingTime
import kotlinx.coroutines.delay
import java.time.Instant
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Composable
fun WeatherLabel(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colors.onPrimary.copy(0.7f),
    textAlign: TextAlign = TextAlign.End
) {

    var weatherString = ""
    val weatherViewModel: WeatherViewModel = viewModel()
    val weatherFlow = weatherViewModel.weatherFlow.collectAsState()
    val weather = weatherFlow.value
    weather?.let {
        weatherString =
            "\n${it.text}${it.temp}° / ${it.windDir}${it.windScale}级 / 湿度${it.humidity}%"
        if (IsPortrait) {
            weatherString =
                "\n${it.text}${it.temp}° ${it.windDir}${it.windScale}级 湿度${it.humidity}%"
        }
    }

    var timeString: String by remember {  mutableStateOf("") }
    var timestampForWeather: Long? by remember { mutableStateOf(null) }
    var timestampForClosing: Long? by remember { mutableStateOf(null) }

    LaunchedEffect(Unit) {
        while (true) {
            delay(1000)
            timeString = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))

            if (timestampForWeather == null) {
                timestampForWeather = Instant.now().epochSecond
            } else {
                timestampForWeather?.let {
                    // 20分钟更新一次天气数据
                    if (Instant.now().epochSecond - it >= 60 * 20) {
                        timestampForWeather = Instant.now().epochSecond
                        weatherViewModel.update()
                    }
                }
            }

            if (timestampForClosing == null) {
                timestampForClosing = Instant.now().epochSecond
            } else {
                timestampForClosing?.let {
                    // 每秒检测一次是否在售票时间
                    if (Instant.now().epochSecond - it >= 1) {
                        GLOBAL_POLL?.let { poll ->
                            IsClosingTime = poll.isClosingTime()
                            if (IsClosingTime) {
                                MediaManager.playLoopingNotice(false)
                            }
                            else if (CurrentVideoUrlForScreensaver.isNotEmpty()) {
                                MediaManager.playLoopingNotice(false)
                            }
                            else if (!SHOW_POPUP) {
                                MediaManager.playLoopingNotice(true)
                            }
                        }
                    }
                }
            }
        }
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.CenterEnd
    ) {
        Text(
            text = timeString + weatherString,
            color = color,
            textAlign = textAlign,
            style = MaterialTheme.typography.overline.copy(lineHeight = 18.sp)
        )
    }
}