package com.turinggear.ssa_kiosk.ui

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
// Material 2 Imports
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun RestartCountdownDialog(
    showDialog: Boolean,
    initialCountdownSeconds: Int = 10,
    onDismiss: () -> Unit,
    onRestartNow: () -> Unit,
    onCancelRestart: () -> Unit,
    onTimeout: () -> Unit
) {
    if (showDialog) {
        var countdownSeconds by remember { mutableStateOf(initialCountdownSeconds) }
        val currentOnTimeout by rememberUpdatedState(onTimeout)
        val currentOnDismiss by rememberUpdatedState(onDismiss)

        LaunchedEffect(Unit) {
            countdownSeconds = initialCountdownSeconds
            while (countdownSeconds > 0 && isActive) {
                delay(1000L)
                countdownSeconds--
            }
            if (isActive && countdownSeconds == 0) {
                currentOnTimeout()
                currentOnDismiss()
            }
        }

        Dialog(
            onDismissRequest = {
                Log.d("RestartDialogM2", "Dialog onDismissRequest triggered (should not happen with current properties)")
            },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Surface( // Material 2 Surface
                modifier = Modifier
                    .fillMaxWidth(0.3f)
                    .wrapContentHeight(),
                shape = RoundedCornerShape(12.dp),
                color = MaterialTheme.colors.surface, // M2: MaterialTheme.colors
                elevation = 8.dp // M2: elevation instead of tonalElevation
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = "重启提示",
                        style = MaterialTheme.typography.h6, // M2: h6 (or similar)
                        color = MaterialTheme.colors.onSurface // M2: MaterialTheme.colors
                    )
                    Text(
                        text = if (countdownSeconds > 0) "${countdownSeconds}秒钟后重启设备" else "准备重启...",
                        style = MaterialTheme.typography.body1, // M2: body1 (or similar)
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        OutlinedButton( // Material 2 OutlinedButton
                            onClick = {
                                onCancelRestart()
                                currentOnDismiss()
                            }
                        ) {
                            Text("取消重启")
                        }
                        Button( // Material 2 Button
                            onClick = {
                                onRestartNow()
                                currentOnDismiss()
                            },
                            colors = ButtonDefaults.buttonColors(
                                backgroundColor = MaterialTheme.colors.primary // M2: backgroundColor
                            )
                        ) {
                            Text("立即重启", color = MaterialTheme.colors.onPrimary) // M2: MaterialTheme.colors
                        }
                    }
                }
            }
        }
    }
}