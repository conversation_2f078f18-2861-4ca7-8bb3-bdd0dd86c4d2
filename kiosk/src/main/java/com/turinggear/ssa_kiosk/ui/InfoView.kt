package com.turinggear.ssa_kiosk.ui

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_kiosk.*
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.viewmodel.StationViewModel
import com.turinggear.ssa_shared.*

@Composable
fun InfoViewLandscape() {
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .fillMaxWidth()
            .padding(MARGIN.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CurrentStationView(
            Modifier
                .weight(1.2f)
                .padding(bottom = MARGIN.dp))
        RouteView(Modifier.weight(3f))
    }
}

@Composable
fun InfoViewPortrait() {
    Row(
        Modifier
            .fillMaxSize()
    ) {
        SpringRouteView(
            Modifier
                .weight(0.87f)
                .padding(vertical = 30.dp)
        )
//        InnerRouteView(
//            Modifier
//                .weight(0.87f)
//                .padding(vertical = 30.dp)
//        )
//        Divider(
//            modifier = Modifier
//                .fillMaxHeight()
//                .width(0.5.dp),
//            thickness = 0.5.dp
//        )
//        OuterRouteView(
//            Modifier
//                .weight(1f)
//                .padding(vertical = 20.dp)
//        )
    }
}

@Composable
fun CurrentStationView(modifier: Modifier = Modifier) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(
            12.dp,
            alignment = Alignment.CenterVertically
        ),
        modifier = modifier
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .fillMaxSize()
    ) {

        CURRENT_STATION_NAME = GLOBAL_POLL?.data?.machineInfo?.station_name ?: ""
        CURRENT_STATION_ID = GLOBAL_POLL?.data?.machineInfo?.station_id

        if (IsPortrait) {
            Column(Modifier
                .fillMaxWidth()
                .height(90.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                GLOBAL_POLL?.let {
                    WeatherLabel(Modifier
                        .height(50.dp)
                        .padding(bottom = 10.dp)
                        .padding(horizontal = 8.dp),
                        color = MaterialTheme.colors.onBackground.copy(0.7f),
                        textAlign = TextAlign.Center)
                    Divider()
                }
            }
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start
        ) {
            Icon(
                Icons.Outlined.Place,
                contentDescription = null,
                modifier = Modifier
                    .size(16.dp)
                    .padding(end = 3.dp),
                tint = MaterialTheme.colors.onBackground.copy(0.7f)
            )
            Text(
                text = "当前站点",
                color = MaterialTheme.colors.onBackground.copy(0.6f),
                style = MaterialTheme.typography.button
            )
        }
        Text(
            text = CURRENT_STATION_NAME,
            color = MaterialTheme.colors.onBackground,
            style = MaterialTheme.typography.h6.copy(
                fontWeight = FontWeight.SemiBold,
                letterSpacing = 1.5.sp
            )
        )
        var eta = "10"
        GLOBAL_POLL?.data?.machineStaticConfig?.eta?.let {
            val array = it.split(":")
            if (array.count() > 1) {
                eta = array[1]
            }
        }
//        val countDownText = if (IsPortrait) "（约${eta}分钟到站）" else "（大约${eta}分钟到站）"
//        Text(
//            text = if (CURRENT_STATION_NAME.isEmpty()) "" else countDownText,
//            color = MaterialTheme.colors.onBackground.copy(0.8f),
//            textAlign = TextAlign.Center,
//            style = MaterialTheme.typography.caption
//        )

        if (IsPortrait) {
            Box(Modifier
                .fillMaxWidth()
                .height(90.dp))
        }
    }
}

@Composable
fun InnerRouteView(modifier: Modifier = Modifier) {
    val stationViewModel: StationViewModel = viewModel()
    val innerStationsFlow = stationViewModel.innerStationsFlow.collectAsState()
    val innerStations = innerStationsFlow.value?.sortedBy { it.route_sort_order } ?: listOf()

    Box(
        modifier = modifier
            .clickable { stationViewModel.update() }
            .fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(all = 16.dp),
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    if (index < innerStations.count()) {
                        val station = innerStations[index]
                        BorderText(text = station.name)
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp)
                                    .height(10.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.5f)
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(0.5f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    if (index < innerStations.count()) {
                        val station = innerStations[index]
                        Box(contentAlignment = Alignment.Center) {
                            BorderText(text = station.name, alpha = 0f)
                            if (index == 0) {
                                Icon(
                                    Icons.Outlined.North,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                            if (index == 2) {
                                Icon(
                                    Icons.Outlined.South,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                        }
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    val i = index + 1
                    if (i < innerStations.count()) {
                        val station = innerStations[i]
                        Box(contentAlignment = Alignment.Center) {
                            if (index == 2) {
                                BorderText(text = station.name)
                            } else {
                                BorderText(text = station.name, alpha = 0f)
                            }
                        }
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(0.5f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    if (index < innerStations.count()) {
                        val station = innerStations[index]
                        Box(contentAlignment = Alignment.Center) {
                            BorderText(text = station.name, alpha = 0f)
                            if (index == 0) {
                                Icon(
                                    Icons.Outlined.MoreVert,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                            if (index == 2) {
                                Icon(
                                    Icons.Outlined.South,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                        }
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    val i = 6 - index
                    if (i < innerStations.count()) {
                        val station = innerStations[i]
                        BorderText(text = station.name)
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.West,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp)
                                    .height(10.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.5f)
                            )
                        }
                    }
                }
            }
        }

        Box(
            modifier = Modifier
                .fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "电瓶车线路",
                color = MaterialTheme.colors.onBackground.copy(0.3f),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.caption
            )
        }
    }
}

@Composable
fun OuterRouteView(modifier: Modifier = Modifier) {
    val stationViewModel: StationViewModel = viewModel()
    val outerStationsFlow = stationViewModel.outerStationsFlow.collectAsState()
    val outerStations = outerStationsFlow.value?.sortedBy { it.route_sort_order } ?: listOf()

    Box(
        modifier = modifier
            .clickable { stationViewModel.update() }
            .fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(all = 20.dp),
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(4) { index ->
                    if (index < outerStations.count()) {
                        val station = outerStations[index]
                        SmallBorderText(text = station.name)
                        if (index < 3) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp)
                                    .height(10.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.5f)
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(0.5f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(4) { index ->
                    if (index < outerStations.count()) {
                        val station = outerStations[index]
                        Box(contentAlignment = Alignment.Center) {
                            SmallBorderText(text = station.name, alpha = 0f)
                            if (index == 0) {
                                Icon(
                                    Icons.Outlined.North,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                            if (index == 3) {
                                Icon(
                                    Icons.Outlined.South,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                        }
                        if (index < 3) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(4) { index ->
                    val i = index + 1
                    if (i < outerStations.count()) {
                        val station = outerStations[i]
                        Box(contentAlignment = Alignment.Center) {
                            if (index == 3) {
                                SmallBorderText(text = station.name)
                            } else {
                                SmallBorderText(text = station.name, alpha = 0f)
                            }
                        }
                        if (index < 3) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(0.5f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(4) { index ->
                    val i = index + 1
                    if (i < outerStations.count()) {
                        val station = outerStations[i]
                        Box(contentAlignment = Alignment.Center) {
                            SmallBorderText(text = station.name, alpha = 0f)
                            if (index == 0) {
                                Icon(
                                    Icons.Outlined.MoreVert,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                            if (index == 3) {
                                Icon(
                                    Icons.Outlined.South,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                        }
                        if (index < 3) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(4) { index ->
                    var i = index + 2
                    if (index == 0) {
                        i = 10
                    }
                    if (i < outerStations.count()) {
                        val station = outerStations[i]
                        Box(contentAlignment = Alignment.Center) {
                            if ((index == 3) or (index == 0)) {
                                SmallBorderText(text = station.name)
                            } else {
                                SmallBorderText(text = station.name, alpha = 0f)
                            }
                        }
                        if (index < 3) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(0.5f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(4) { index ->
                    val i = index + 2
                    if (i < outerStations.count()) {
                        val station = outerStations[i]
                        Box(contentAlignment = Alignment.Center) {
                            SmallBorderText(text = station.name, alpha = 0f)
                            if (index == 0) {
                                Icon(
                                    Icons.Outlined.North,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                            if (index == 3) {
                                Icon(
                                    Icons.Outlined.South,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                        }
                        if (index < 3) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(4) { index ->
                    val i = 9 - index
                    if (i < outerStations.count()) {
                        val station = outerStations[i]
                        SmallBorderText(text = station.name)
                        if (index < 3) {
                            Icon(
                                Icons.Outlined.West,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp)
                                    .height(10.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.5f)
                            )
                        }
                    }
                }
            }
        }

        Box(
            modifier = Modifier
                .fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "电瓶车线路\n（请到指定站点乘车）",
                color = MaterialTheme.colors.onBackground.copy(0.3f),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.caption
            )
        }
    }
}


@Composable
fun RouteView(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .background(Color.Transparent)
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
//            if (BuildConfig.FLAVOR != "shiyuan") {
                InfoMap3LandscapeView()
//            } else {
//                SpringRouteView(Modifier.padding(vertical = 48.dp))
////                InnerRouteView(Modifier.weight(1f))
////                Divider(modifier = Modifier.padding(horizontal = 0.5.dp), thickness = 0.5.dp)
////                OuterRouteView(Modifier.weight(1f))
//            }
        }
    }
}

@Composable
fun BorderText(text: String, end: Dp = 0.dp, alpha: Float = 0.8f) {
    Box(
        Modifier
            .padding(end = end)
            .clip(RoundedCornerShape(4.dp))
            .background(MaterialTheme.colors.onSurface.copy(alpha = if (alpha == 0f) 0.0f else 0.1f))
    ) {
        Text(
            text = text,
            Modifier.padding(top = 3.dp, bottom = 3.dp, start = 6.dp, end = 6.dp),
            color = MaterialTheme.colors.onBackground.copy(alpha),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.caption
        )
    }
}

@Composable
fun SmallBorderText(text: String, fontSize: TextUnit = 10.sp, end: Dp = 0.dp, alpha: Float = 0.8f) {
    Box(
        Modifier
            .padding(end = end)
            .clip(RoundedCornerShape(4.dp))
            .background(MaterialTheme.colors.onSurface.copy(alpha = if (alpha == 0f) 0.0f else 0.1f))
    ) {
        Text(
            text = text,
            Modifier.padding(top = 3.dp, bottom = 3.dp, start = 5.dp, end = 5.dp),
            color = MaterialTheme.colors.onBackground.copy(alpha),
            fontSize = fontSize,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}
@Composable
fun SpringRouteView(modifier: Modifier = Modifier) {
    val stationViewModel: StationViewModel = viewModel()
    val innerStationsFlow = stationViewModel.springStationsFlow.collectAsState()
    val innerStations = innerStationsFlow.value?.sortedBy { it.route_sort_order } ?: listOf()

    Box(
        modifier = modifier
            .clickable { stationViewModel.update() }
            .fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(all = 16.dp),
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    if (index < innerStations.count()) {
                        val station = innerStations[index]
                        BorderText(text = station.name)
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp)
                                    .height(10.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.5f)
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(0.5f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    if (index < innerStations.count()) {
                        val station = innerStations[index]
                        Box(contentAlignment = Alignment.Center) {
                            BorderText(text = station.name, alpha = 0f)
                            if (index == 0) {
                                Icon(
                                    Icons.Outlined.North,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                            if (index == 2) {
                                Icon(
                                    Icons.Outlined.South,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                        }
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    val i = index + 1
                    if (i < innerStations.count()) {
                        val station = innerStations[i]
                        Box(contentAlignment = Alignment.Center) {
                            if (index == 2) {
                                BorderText(text = station.name)
                            } else {
                                BorderText(text = station.name, alpha = 0f)
                            }
                        }
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(0.5f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    if (index < innerStations.count()) {
                        val station = innerStations[index]
                        Box(contentAlignment = Alignment.Center) {
                            BorderText(text = station.name, alpha = 0f)
                            if (index == 0) {
                                Icon(
                                    Icons.Outlined.MoreVert,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                            if (index == 2) {
                                Icon(
                                    Icons.Outlined.South,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .width(16.dp)
                                        .height(10.dp),
                                    tint = MaterialTheme.colors.onBackground.copy(0.5f)
                                )
                            }
                        }
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.East,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp),
                                tint = Color.Transparent
                            )
                        }
                    }
                }
            }
            LazyRow(
                Modifier
                    .weight(1f)
                    .fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(3) { index ->
                    val i = 6 - index
                    if (i < innerStations.count()) {
                        val station = innerStations[i]
                        BorderText(text = station.name)
                        if (index < 2) {
                            Icon(
                                Icons.Outlined.West,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(16.dp)
                                    .height(10.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.5f)
                            )
                        }
                    }
                }
            }
        }

        Box(
            modifier = Modifier
                .fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "电瓶车线路",
                color = MaterialTheme.colors.onBackground.copy(0.3f),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.caption
            )
        }
    }
}
