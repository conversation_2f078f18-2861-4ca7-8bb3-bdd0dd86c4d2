package com.turinggear.ssa_kiosk.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.compose.rememberAsyncImagePainter
import com.turinggear.ssa_kiosk.URL_WX_QRCODE
import com.turinggear.ssa_shared.IsPortrait
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.R

@Composable
fun TitleView() {
    Row(
        modifier = Modifier
            .background(MaterialTheme.colors.primary)
            .fillMaxWidth()
            .padding(horizontal = 8.dp)
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(Modifier.weight(1f), contentAlignment = Alignment.CenterStart) {
            Image(
                painter = rememberAsyncImagePainter(R.drawable.logo),
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier.size(44.dp)
            )
        }
        Column(
            modifier = Modifier.weight(1.1f),
            verticalArrangement = Arrangement.spacedBy(if (IsPortrait) 4.dp else 2.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val subtitle = GLOBAL_POLL?.data?.machineStaticConfig?.screen_subtitle ?: ""
            val title = GLOBAL_POLL?.data?.machineStaticConfig?.screen_title ?: ""
            Text(
                text = subtitle,//"北京世园公司内部交通",
                color = MaterialTheme.colors.onPrimary,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.caption
            )
            Text(
                text = title,//"智能购票终端",
                color = MaterialTheme.colors.onPrimary,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.subtitle1
            )
        }
        Row(Modifier.weight(1f),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (!IsPortrait) {
                GLOBAL_POLL?.let {
                    WeatherLabel(Modifier.padding(end = 10.dp))
                }
            }
            AsyncImage(
                model = URL_WX_QRCODE,
                placeholder = rememberAsyncImagePainter(R.drawable.wx_qrcode),
                error = rememberAsyncImagePainter(R.drawable.wx_qrcode),
                fallback = rememberAsyncImagePainter(R.drawable.wx_qrcode),
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier.size(44.dp)
            )
        }
    }
}