package com.turinggear.ssa_kiosk.ui

import android.media.MediaPlayer
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Done
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.util.MediaManager
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.PAYMENTSTATE
import com.turinggear.ssa_shared.PAYMENT_STATE
import com.turinggear.ssa_shared.SELECTED_DIRECTION_STATION_ID
import com.turinggear.ssa_shared.R

@Composable
fun WechatPayButton(enableRouteDirection: Boolean, modifier: Modifier = Modifier) {

    val tipText = when (PAYMENT_STATE) {
        PAYMENTSTATE.MAKING_ORDER -> "正在创建订单..."
        PAYMENTSTATE.USERPAYING -> "正在支付，请稍等..."
        PAYMENTSTATE.SUCCEED -> "支付成功"
        PAYMENTSTATE.FAILED -> "支付失败，请重试"
        else -> {
            if (enableRouteDirection && SELECTED_DIRECTION_STATION_ID.isNullOrEmpty()) {
                "请选择乘坐方向"
            } else {
                "请在下方扫码口出示微信付款码"
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(
            12.dp,
            alignment = Alignment.CenterVertically
        ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = tipText,
            color = Color.Blue,
            style = MaterialTheme.typography.body1.copy(fontWeight = FontWeight.Medium)
        )
        var bgColor = Color(color = 0xFF57AB6D)
        var enableButton = true
        if (enableRouteDirection && SELECTED_DIRECTION_STATION_ID.isNullOrEmpty()) {
            bgColor = MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
            enableButton = false
        }
        Button(
            onClick = {
                PAYMENT_STATE = PAYMENTSTATE.USER_TAPPED
                MediaManager.playMusic(ApplicationContext, R.raw.show_wechat_pay)
//                PrinterManager.printTest() //dbg
            },
            enabled = (((PAYMENT_STATE == PAYMENTSTATE.UNSTARTED) or (PAYMENT_STATE == PAYMENTSTATE.FAILED)) && enableButton),
            colors = ButtonDefaults.outlinedButtonColors(
                backgroundColor = bgColor
            )
        ) {
            when {
                (PAYMENT_STATE > PAYMENTSTATE.UNSTARTED) and (PAYMENT_STATE < PAYMENTSTATE.SUCCEED) ->
                    Box(contentAlignment = Alignment.Center) {
//                        CircularProgressIndicator(
//                            modifier = Modifier.size(24.dp),
//                            color = Color.White,
//                            strokeWidth = 1.dp
//                        )
//                        Icon(
//                            Icons.Outlined.South,
//                            contentDescription = null,
//                            modifier = Modifier.size(18.dp),
//                            tint = Color.White
//                        )

                        Text(
                            text = "正在识别您的付款码...",
                            color = Color.White,
                            style = MaterialTheme.typography.body1.copy(fontWeight = FontWeight.Bold)
                        )
                    }
                PAYMENT_STATE == PAYMENTSTATE.SUCCEED ->
                    Icon(
                        Icons.Outlined.Done,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = Color.White
                    )
                else ->
                    Text(
                        text = "微信付款",
                        color = Color.White,
                        style = MaterialTheme.typography.body1.copy(fontWeight = FontWeight.Bold)
                    )
            }
        }
    }
}