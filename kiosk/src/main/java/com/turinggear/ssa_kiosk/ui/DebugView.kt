package com.turinggear.ssa_kiosk.ui

import android.app.Application
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.AttachFile
import androidx.compose.material.icons.outlined.Close
import androidx.compose.material.icons.outlined.ExitToApp
import androidx.compose.material.icons.outlined.PowerSettingsNew
import androidx.compose.material.icons.outlined.Print
import androidx.compose.material.icons.outlined.Refresh
import androidx.compose.material.icons.outlined.Spa
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.MARGIN
import com.turinggear.ssa_kiosk.MainActivity
import com.turinggear.ssa_kiosk.ui.theme.KioskTheme
import com.turinggear.ssa_kiosk.util.PrinterManager
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.SHOW_DEBUGVIEW
import com.turinggear.ssa_shared.util.OSUtils
import kotlinx.coroutines.delay
import kotlin.system.exitProcess

@Composable
fun DebugView() {

    val inputNumber = remember { mutableStateOf("") }
    val showHiddenArea = remember { mutableStateOf(false) }

    LaunchedEffect(key1 = true) {
        // 60s 自动关闭
        delay(60 * 1000)
        SHOW_DEBUGVIEW = false
        ApplicationContext?.let {
//            Toast.makeText(it, "调试界面已经自动关闭", Toast.LENGTH_SHORT).show()
        }
    }

    Box(
        modifier = Modifier
            .clickable(enabled = false, onClick = { })
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.8f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            Modifier
                .width(320.dp)
                .aspectRatio(0.7f)
                .clip(RoundedCornerShape(8.dp))
                .background(MaterialTheme.colors.background),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(fraction = 1 / 11f),
                contentAlignment = Alignment.BottomEnd
            ) {
                IconButton(
                    onClick = {
                        SHOW_DEBUGVIEW = false
                    }
                ) {
                    Icon(
                        Icons.Outlined.Close,
                        contentDescription = null,
                        tint = MaterialTheme.colors.onBackground.copy(0.5f)
                    )
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxHeight(fraction = 1 / 10f),
                contentAlignment = Alignment.TopCenter
            ) {
                var title = "调试页面 " + Build.SERIAL
                OSUtils.getICCID(ApplicationContext as Application)?.let {
                    title += "\n" + it
                }
//                title += "\n" + "8986032147200087275" //dbg

                Text(
                    text = title,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.subtitle2.copy()
                )
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MARGIN.dp)
                    .verticalScroll(rememberScrollState())
            ) {
//                OSUtils.getICCID(ApplicationContext as Application)?.let {
//                    Row(
//                        modifier = Modifier.fillMaxWidth(),
//                        verticalAlignment = Alignment.CenterVertically
//                    ) {
//                        Text(
//                            text = it,
//                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
//                            textAlign = TextAlign.Center,
//                            style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Thin)
//                        )
//                    }
//                    Divider()
//                }
                if (showHiddenArea.value) {
                    Button(onClick = {
                        val intent = Intent()
                        intent.component = ComponentName("com.android.launcher3", "com.android.launcher3.Launcher")
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        ApplicationContext?.startActivity(intent)
                    }) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(text = "显示桌面", textAlign = TextAlign.Center)
                        }
                    }
                    TextButton(onClick = {
                        val intent = Intent()
                        intent.component = ComponentName("com.turinggear.ssa_console", "com.turinggear.ssa_console.MainActivity")
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        ApplicationContext?.startActivity(intent)
                    }) {
                        Icon(
                            Icons.Outlined.Spa,
                            contentDescription = null
                        )
                        Text(text = "打开控制台", modifier = Modifier.padding(start = 8.dp))
                    }
                    TextButton(onClick = {
                        exitProcess(0)
                    }) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Outlined.ExitToApp,
                                contentDescription = null
                            )
                            Text(text = "退出软件", modifier = Modifier.padding(start = 8.dp))
                        }
                    }
                    TextButton(onClick = {
                        ApplicationContext?.startActivity(Intent(Intent.ACTION_REBOOT))
                    }) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Outlined.PowerSettingsNew,
                                contentDescription = null
                            )
                            Text(text = "重启系统", modifier = Modifier.padding(start = 8.dp))
                        }
                    }
                    TextButton(onClick = {
                        PrinterManager.printTest()
                    }) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Outlined.Print,
                                contentDescription = null
                            )
                            Text(text = "测试打印机", modifier = Modifier.padding(start = 8.dp))
                        }
                    }
//                    Divider()
                }
//                TextButton(onClick = {
//                    ApplicationContext?.let {
//                        PrinterManager.updateAllPrinterStatus(application = it as Application)
//                    }
//                }) {
//                    Row(
//                        modifier = Modifier.fillMaxWidth(),
//                        verticalAlignment = Alignment.CenterVertically
//                    ) {
//                        Icon(
//                            Icons.Outlined.Print,
//                            contentDescription = null
//                        )
//                        Text(text = "刷新所有打印机状态", modifier = Modifier.padding(start = 8.dp))
//                    }
//                }
//                Divider()
//                TextButton(onClick = {
//                    OSUtils.clearExoCache(ApplicationContext as Context)
//                }) {
//                    Row(
//                        modifier = Modifier.fillMaxWidth(),
//                        verticalAlignment = Alignment.CenterVertically
//                    ) {
//                        Icon(
//                            Icons.Outlined.AttachFile,
//                            contentDescription = null
//                        )
//                        // 视频播放时清空没有危险
//                        Text(text = "清除视频缓存", modifier = Modifier.padding(start = 8.dp))
//                    }
//                }
            }

            // 密码键盘行
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MARGIN.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                val numbers = (0..9).toMutableList()
//                numbers.shuffle()

                for (i in numbers.chunked(3)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        for (number in i) {
                            TextButton(onClick = {
                                if (inputNumber.value.length < 6) {
                                    inputNumber.value += number.toString()
                                }
                                if ((inputNumber.value.length == 6) && (inputNumber.value == "220405")) {
                                    showHiddenArea.value = true
                                }
                            }) {
                                Text(text = number.toString())
                            }
                        }
                    }
                }
            }

        }

    }

}

@Preview(showBackground = true)
@Composable
fun DebugViewPreview() {
    KioskTheme {
        DebugView()
    }
}