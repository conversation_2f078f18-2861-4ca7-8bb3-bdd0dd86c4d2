package com.turinggear.ssa_kiosk.ui

import android.app.Application
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Print
import androidx.compose.material.icons.outlined.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_kiosk.*
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_shared.model.ticketStatusColorFrom
import com.turinggear.ssa_shared.model.ticketStatusStringFrom
import com.turinggear.ssa_kiosk.util.PrinterManager
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.Ticket
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.turinggear.ssa_shared.R

suspend fun onReprintClicked(ticket: Ticket, qrcodeFormat: Int) {
    PrinterManager.shouldUpdatePrinterStatus(ApplicationContext as Application)
    delay(PrinterManager.PRINTER_CHECK_TIME_MS)
    if ((PRINTER_1_STATUS != 24) and (PRINTER_2_STATUS != 24)) {
            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar("$PRINTER_1_STATUS_STRING  /  $PRINTER_2_STATUS_STRING")
    } else {
        PrinterManager.print(ticket = ticket, isReprint = true, qrcodeFormat = qrcodeFormat)
            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar("正在出票…")
    }
}

@Composable
fun ReprintScreen() {
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .clickable(enabled = false, onClick = { })
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.8f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            Modifier
                .width(320.dp)
                .aspectRatio(0.7f)
                .clip(RoundedCornerShape(8.dp))
                .background(MaterialTheme.colors.background),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(fraction = 1 / 11f),
                contentAlignment = Alignment.BottomEnd
            ) {
                IconButton(
                    onClick = {
                        SHOW_REPRINT = false
                        SCAN_TYPE = SCANTYPE.CALL_CAR
                        ReprintTickets = null
                    }
                ) {
                    Icon(
                        Icons.Outlined.Close,
                        contentDescription = null,
                        tint = MaterialTheme.colors.onBackground.copy(0.5f)
                    )
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxHeight(fraction = 1 / 10f),
                contentAlignment = Alignment.TopCenter
            ) {
                var title = "补打小票"
                Text(
                    text = title,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold)
                )
            }
            Column(
                modifier = Modifier
                    .padding(horizontal = MARGIN.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 20.dp)
                        .padding(horizontal = MARGIN.dp),
                    contentAlignment = Alignment.Center
                ) {
                    var text = "1. 打开微信支付凭证\n2. 点击查看账单详情\n3. 扫描页面上的条码"
                    if (BuildConfig.FLAVOR == "waterpark") {
                        text = "\n1. 重新扫描小程序码，并确认授权成功\n\n2. 打开微信支付凭证，点击账单详情\n\n3. 将页面上的条码展示在扫码口即可"
                    }
                    Text(
                        text = text,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.5f),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.subtitle1
                    )
                }
                ReprintTickets?.let {
                    val scope = rememberCoroutineScope()
                    it.forEach { ticket ->
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 15.dp)
                                .border(
                                    0.5.dp,
                                    MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                                    RoundedCornerShape(8.dp)
                                )
                                .background(
                                    MaterialTheme.colors.onSurface.copy(alpha = 0.04f),
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(horizontal = 15.dp, vertical = 12.dp),
                            contentAlignment = Alignment.BottomEnd
                        ) {
                            val text =
                                "编号：${ticket.ticket_no}\n时间：${ticket.created_at}\n票种：${ticket.goods?.description}\n单价：${ticket.goods?.price}元"
                            Column(Modifier.fillMaxWidth()) {
                                Text(text = text, style = MaterialTheme.typography.caption)
                                Text(
                                    text = "状态：${ticketStatusStringFrom(ticket)}",
                                    color = ticketStatusColorFrom(ticket),
                                    style = MaterialTheme.typography.caption
                                )
                            }
                            var enabled = false
                            var tintColor = Color.LightGray
                            if (ticket.status == 10) {
                                enabled = true
                                tintColor = Color(0xFF53A949)
                            }
                            IconButton(
                                onClick = {
                                    val codeFormat = context.resources.getInteger(R.integer.ticket_qrcode_format)
                                    scope.launch {
                                        onReprintClicked(ticket, codeFormat)
                                    }
                                },
                                modifier = Modifier
                                    .background(
                                        tintColor.copy(alpha = 0.2f),
                                        RoundedCornerShape(8.dp)
                                    ),
                                enabled = enabled
                            ) {
                                Icon(
                                    imageVector = Icons.Filled.Print,
                                    contentDescription = null,
                                    tint = tintColor.copy(alpha = 0.8f)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}