package com.turinggear.ssa_kiosk.ui

import android.app.Application
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.HistoryToggleOff
import androidx.compose.material.icons.outlined.ReportProblem
import androidx.compose.material.icons.outlined.WifiOff
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.turinggear.ssa_kiosk.*
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.util.PrinterManager
import com.turinggear.ssa_shared.*
import kotlinx.coroutines.launch

@Composable
fun ErrorMask(title: String, subtitle: String, imageVector: ImageVector, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black.copy(0.7f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(10.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector,
                contentDescription = null,
                modifier = Modifier.size(30.dp),
                tint = Color.Yellow
            )
            Text(
                text = title,
                color = Color.Yellow,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.subtitle1
            )
            Text(
                text = subtitle,
                color = Color.Yellow,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.button
            )
        }
    }
}

@Composable
fun ClosingTimeMask() {
    val title = "非售票时间"
    var subtitle = ""
    GLOBAL_POLL?.data?.scenicAreaConfig?.sell_ticket_time?.let {
        val array = it.split("-")
        val start = array.first().dropLast(3)
        val end = array.last().dropLast(3)
        subtitle = "（售票时间：$start-$end）"
    }
    GLOBAL_POLL?.data?.scenicAreaConfig?.stop_ticket_times?.let { stop_ticket_times ->
        var breakingTime = ""
        stop_ticket_times.forEach {
            val array = it.split("-")
            val start = array.first().dropLast(3)
            val end = array.last().dropLast(3)
            if (breakingTime.isNotEmpty()) {
                breakingTime += ", "
            }
            breakingTime += "$start-$end"
        }
        if (breakingTime.isNotEmpty()) {
            subtitle = subtitle.replace("（", "")
            subtitle = subtitle.replace("）", "")
            subtitle += "\n\n（休息时间：${breakingTime}）"
        }
    }
    val imageVector = Icons.Outlined.HistoryToggleOff
    ErrorMask(title = title, subtitle = subtitle, imageVector = imageVector)
}

@Composable
fun NetworkErrorMask(title: String, subtitle: String) {
    val imageVector = Icons.Outlined.WifiOff
    ErrorMask(title = title, subtitle = subtitle, imageVector = imageVector)
}

@Composable
fun PrinterErrorMask() {
    val title = if ((PRINTER_1_STATUS == 56) and (PRINTER_2_STATUS == 56)) {
        "打印机缺纸"
    } else {
        "打印机故障"
    }
    val subtitle = "已通知工作人员"
    val imageVector = Icons.Outlined.ReportProblem
    ErrorMask(
        title = title,
        subtitle = subtitle,
        imageVector = imageVector,
        modifier = Modifier.clickable(enabled = true, onClick = {
            PrinterManager.updateAllPrinterStatus(ApplicationContext as Application)
        })
    )
}

@Composable
fun PrinterErrorText(
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 9.sp,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.End
) {
    val printer1StatusString = PRINTER_1_STATUS_STRING
    val printer2StatusString = PRINTER_2_STATUS_STRING
//    val printer1StatusString = when (PRINTER_1_STATUS) {
//        24 -> "打印机¹正常"
//        56 -> "打印机¹缺纸"
//        -1 -> "打印机¹未连接"
//        else -> "打印机¹异常"
//    }
//    val printer2StatusString = when (PRINTER_2_STATUS) {
//        24 -> "打印机²正常"
//        56 -> "打印机²缺纸"
//        -1 -> "打印机²未连接"
//        else -> "打印机²异常"
//    }
    var printer1StatusColor = when (PRINTER_1_STATUS) {
        24 -> MaterialTheme.colors.onBackground.copy(0.6f)
        else -> COLOR_WARNING
    }
    var printer2StatusColor = when (PRINTER_2_STATUS) {
        24 -> MaterialTheme.colors.onBackground.copy(0.6f)
        else -> COLOR_WARNING
    }
    if ((PRINTER_1_STATUS != 24) and (PRINTER_2_STATUS != 24)) {
        printer1StatusColor = Color.Red
        printer2StatusColor = Color.Red
    }
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = horizontalArrangement,
        verticalAlignment = Alignment.Bottom
    ) {
        val t = if (!IsPortrait) {
            "软件版本：${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})" + if (BuildConfig.FLAVOR != "waterpark") " ， " else ""
        } else ""
        Text(
            text = t + if (BuildConfig.FLAVOR != "waterpark") "设备状态：" else "",
            modifier = Modifier
                .pointerInput(Unit) {
                    detectTapGestures(
                        onDoubleTap = { SHOW_DEBUGVIEW = true },
//                        onTap = {
//                            if (BuildConfig.DEBUG) {
//                                if (ColorPaletteIndex < 2) {
//                                    ColorPaletteIndex += 1
//                                } else {
//                                    ColorPaletteIndex = 0
//                                }
//                            }
//                        }
                    )
                },
            color = MaterialTheme.colors.onBackground.copy(0.6f),
            fontSize = fontSize
        )
        if (BuildConfig.FLAVOR != "waterpark") {
            val scope = rememberCoroutineScope()
            Text(
                text = printer1StatusString,
                modifier = Modifier
                    .clickable {
                        PrinterManager.shouldUpdatePrinterStatus(ApplicationContext as Application)
                        scope.launch {
                            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar("打印机状态已更新")
                        }
                    },
                color = if (BuildConfig.FLAVOR == "waterpark") MaterialTheme.colors.onBackground.copy(0.6f) else printer1StatusColor,
                fontSize = fontSize
            )
            Text(
                text = " / ",
                color = MaterialTheme.colors.onBackground.copy(0.6f),
                fontSize = fontSize
            )
            Text(
                text = printer2StatusString,
                modifier = Modifier
                    .clickable {
                        PrinterManager.shouldUpdatePrinterStatus(ApplicationContext as Application)
                        scope.launch {
                            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar("打印机状态已更新")
                        }
                    },
                color = if (BuildConfig.FLAVOR == "waterpark") MaterialTheme.colors.onBackground.copy(0.6f) else printer2StatusColor,
                fontSize = fontSize
            )
        }
    }
}