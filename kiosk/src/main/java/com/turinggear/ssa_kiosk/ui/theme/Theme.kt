package com.turinggear.ssa_kiosk.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material.MaterialTheme
import androidx.compose.material.darkColors
import androidx.compose.material.lightColors
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.turinggear.ssa_shared.ColorPaletteIndex

private val DarkColorPalette = darkColors(
    primary = Color(0xFF062671),
    primaryVariant = Purple700,
    secondary = Teal200,
    surface = Color(0xFF062671).copy(alpha = 0.6f),
    onPrimary = Color.White,
    onSecondary = Color.White,
)

private val LightColorPalette = lightColors(
    primary = Color(0xFF062671),
    primaryVariant = Purple700,
    secondary = Teal200,
    surface = Color(0xFFE6E9F0),
    onSecondary = Color.Black,

    /* Other default colors to override
    background = Color.White,
    onPrimary = Color.White,
    onBackground = Color.Black,
    onSurface = Color.Black,
    */
)

private val KioskColorPalette = darkColors(
    primary = Color(0xFF062671),
    background = Color(0xFF062672),
    surface = Color(0xFF1F3C80),
    onPrimary = Color.White,
    onSecondary = Color.White,
    onBackground = Color.White
)

@Composable
fun KioskTheme(darkTheme: Boolean = isSystemInDarkTheme(), content: @Composable () -> Unit) {
//    val colors = if (darkTheme) {
//        DarkColorPalette
//    } else {
//        LightColorPalette
//    }

    val colors = when (ColorPaletteIndex) {
        0 -> if (darkTheme) DarkColorPalette else LightColorPalette
        1 -> if (darkTheme) LightColorPalette else DarkColorPalette
        else -> KioskColorPalette
    }

    MaterialTheme(
        colors = colors,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}