package com.turinggear.ssa_kiosk.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.apiTicketGoods
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.ResponseGoods
import com.turinggear.ssa_shared.util.KtorClient
import io.ktor.client.call.*
import io.ktor.client.request.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class GoodViewModel : ViewModel() {

    val goodsFlow = MutableStateFlow<List<Good>?>(null)

    private suspend fun requestGoods(): ResponseGoods {
        return KtorClient.httpClient().use {
            val stationId = GLOBAL_POLL?.data?.machineInfo?.station_id
            it.get(urlString = "${apiTicketGoods()}?visibility=20&station_id=$stationId").body()
        }
    }

    fun update() {
        viewModelScope.launch {
            kotlin.runCatching {
                requestGoods()
            }.onSuccess {
                goodsFlow.value = it.data.data
            }.onFailure {
                print(it)
            }
        }
    }
}