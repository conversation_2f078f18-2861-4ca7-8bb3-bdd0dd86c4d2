package com.turinggear.ssa_kiosk.viewmodel

import android.util.MutableBoolean
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_shared.model.Startup
import com.turinggear.ssa_shared.apiTicketStartup
import com.turinggear.ssa_shared.util.KtorClient
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.http.content.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue

class StartupViewModel : ViewModel() {

    val startupFlow = MutableStateFlow<Startup?>(null)

    var showRebootDialog by mutableStateOf(false)

    private suspend fun request(): Startup {
        return KtorClient.httpClient().use {
            it.post(urlString = apiTicketStartup()) {
                setBody(TextContent(
                    text = "{\"version_code\": ${BuildConfig.VERSION_CODE}}",
                    contentType = ContentType.Application.Json
                ))
            }.body()
        }
    }

    fun onShowRebootDialog() {
        showRebootDialog = true
    }

    fun onDismissRebootDialog() {
        showRebootDialog = false
    }

    fun onConfirmReboot() {
        // Perform reboot logic
        showRebootDialog = false
    }

    init {
        viewModelScope.launch {
            runCatching {
                request()
            }.onSuccess {
                startupFlow.value = it
            }.onFailure {
                print(it.localizedMessage)
            }
        }
    }
}

