package com.turinggear.ssa_kiosk.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.model.ResponseWeather
import com.turinggear.ssa_shared.model.Weather
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import com.turinggear.ssa_shared.R

class WeatherViewModel() : ViewModel() {

    val weatherFlow = MutableStateFlow<Weather?>(null)

    private suspend fun request(): ResponseWeather {
        return HttpClient(OkHttp) {
            install(ContentNegotiation) {
                json(<PERSON><PERSON> {
                    ignoreUnknownKeys = true
                    isLenient = true
                })
            }
        }.use {
            it.get(urlString = ApplicationContext?.getString(R.string.weather_api) ?: "")
        }.body()
    }

    fun update() {
        viewModelScope.launch {
            runCatching {
                request()
            }.onSuccess {
                weatherFlow.value = it.now
            }.onFailure {
                print(it.localizedMessage)
            }
        }
    }

    init {
        update()
    }
}