package com.turinggear.ssa_kiosk.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.apiTicketRoutes
import com.turinggear.ssa_shared.apiTicketRoutesForStation
import com.turinggear.ssa_shared.model.ResponseStation
import com.turinggear.ssa_shared.model.Station
import com.turinggear.ssa_shared.util.KtorClient
import io.ktor.client.call.*
import io.ktor.client.request.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class StationViewModel : ViewModel() {

    val innerStationsFlow = MutableStateFlow<List<Station>?>(null)
    val outerStationsFlow = MutableStateFlow<List<Station>?>(null)
    val routeImagesFlow = MutableStateFlow<List<String?>?>(null)
    val springStationsFlow = MutableStateFlow<List<Station>?>(null)

    private suspend fun request(): ResponseStation? {
        val stationId = GLOBAL_POLL?.data?.machineInfo?.station_id
        if (stationId != null) {
            return KtorClient.httpClient().use { client ->
                client.get(urlString = apiTicketRoutesForStation(stationId))
            }.body()
        } else {
            return null
        }
    }

    fun update() {
        viewModelScope.launch {
            runCatching {
                request()
            }.onSuccess {
                if (it != null) {
//                    innerStationsFlow.value = it.data.first().stations
//                    outerStationsFlow.value = it.data.last().stations
//                    val n = it.data.filter { it.id == 4 }.first()
//                    springStationsFlow.value = n.stations
                    routeImagesFlow.value = it.data.map { r -> r.route_image }
//                    Log.e("XXX", it.data.toString())
                } else {
//                    Log.e("XXX", "????")
                }
            }.onFailure {
                print(it.localizedMessage)
            }
        }
    }

    init {
        update()
    }
}