package com.turinggear.ssa_kiosk.service

import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import com.turinggear.IRemoteService


abstract class AbstractGuardService : Service() {
    abstract fun getPeerPackageName(): String?
    abstract fun getPeerServiceName(): String?
    abstract fun whenDisconnected()

    private lateinit var mBinderFromPeer: IRemoteService;

    private val conn: ServiceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName) {
            Log.e("XXX", getPeerPackageName() + " disconnect")
            bindPeer() //TODO: 是否需要?
            whenDisconnected()
        }

        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            mBinderFromPeer = IRemoteService.Stub.asInterface(service)
            try {
                Log.d(
                    "XXX",
                    ("收到${getPeerPackageName()}消息：name="
                            + mBinderFromPeer.getName())
                )
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }
    private fun bindPeer() {
        val packageName = getPeerPackageName()
        val serviceName = getPeerServiceName()
        if (packageName == null || serviceName == null)
        {
            Log.e("XXX", "skipped bind")
            return
        }
        val serverIntent = Intent()
        serverIntent.setClassName(packageName, serviceName)
        bindService(serverIntent, conn, Context.BIND_AUTO_CREATE)
    }

    override fun onCreate() {
        super.onCreate()

        Log.e("XXX", "${applicationContext.packageName} create")
        bindPeer()
    }

    override fun onBind(intent: Intent): IBinder {
        Log.e("XXX", "${applicationContext.packageName} bind")
        return binder
    }

    private val binder = object : IRemoteService.Stub() {
        override fun getName(): String {
            return applicationContext.packageName
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 这里bind很关键
        bindPeer()
        return START_STICKY
    }

}
