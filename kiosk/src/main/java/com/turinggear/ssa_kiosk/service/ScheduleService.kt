package com.turinggear.ssa_kiosk.service

import android.app.Application
import android.app.Service
import android.content.Intent
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import com.turinggear.ssa_kiosk.util.PrinterManager
import com.turinggear.ssa_shared.ApplicationContext

class ScheduleService : Service() {

    val TAGS = "ScheduleService"

    val handler = Handler(Looper.getMainLooper());

    var isPrinterReportTaskStarted = false

    // 无限循环
    // 每1分钟检查所有打印机
    val printerCheckAndReportTask = object : Runnable {
        override fun run() {
//            Log.e(TAGS, "run")
            PrinterManager.updateAllPrinterStatus(ApplicationContext as Application)

//            val statusString = PRINTER_1_STATUS_STRING + " / " + PRINTER_2_STATUS_STRING
//            Log.e(TAGS, "result " + statusString)

            handler.postDelayed(this, 1*60*1000)
        }
    }

    override fun onCreate() {
        super.onCreate()
    }

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        handler.removeCallbacks(printerCheckAndReportTask);
        stopSelf();

        super.onDestroy()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
//        Log.e(TAGS, "start")

        // TODO: 处理extras

        if (!isPrinterReportTaskStarted) {
            // 首次启动无限循环
            handler.post(printerCheckAndReportTask)
            isPrinterReportTaskStarted = true
        } else {
            // TODO: 不应该在循环任务里?
            // 之后为手动插入任务
            handler.post(Runnable {
//                Log.e(TAGS, "one-shot task")
                PrinterManager.updatePrinterStatus(ApplicationContext as Application)
//                Log.e(TAGS, "task finished")
            })
        }

        return super.onStartCommand(intent, flags, startId)
    }
}