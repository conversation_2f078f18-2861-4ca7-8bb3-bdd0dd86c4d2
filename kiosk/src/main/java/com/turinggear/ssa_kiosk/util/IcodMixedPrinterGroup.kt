package com.turinggear.ssa_kiosk.util

import android.app.Application
import android.content.Context
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.util.Log
import com.szsicod.print.escpos.PrinterAPI
import com.szsicod.print.io.SerialAPI
import com.szsicod.print.io.USBAPI
import com.szsicod.print.log.Utils
import com.turinggear.ssa_kiosk.*
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.*
import com.turinggear.ssa_shared.util.formatTicketQrcode
import java.io.File
import java.nio.charset.Charset

// 研科数码打印机 USB+Serial
class IcodMixedPrinterGroup: PrinterGroup {
    companion object {
        init {
            System.loadLibrary("serial_icod")
        }
    }

    private val usbManager: UsbManager
    private val TAGS: String = "PrinterGroup"

    private var context: Context

    // 当前打印机索引 0/1
    // 固定0为usb 1为串口
    var currentPrinterIndex = 0

    // usb device
    private var printerUsbDevice: UsbDevice? = null

    // 从usbdevice创建的研科printer
    lateinit var currentPrinter: PrinterAPI

    constructor(context: Context) {
        this.context = context

        // icod printer
        Utils.init(context)

        usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    }

    // 遍历usb设备,获取打印机,设置成员变量
    fun scanUsbPrinter() {
        val usbDeviceList = usbManager.deviceList
        if (usbDeviceList.size == 0) {
            Log.e(TAGS, "init: no usb devices")
        }

        for (usbDevice in usbDeviceList.values) {
//            Log.e(TAGS, "init: usb device" + usbDevice.productName)
            Log.e(TAGS, "init: " + usbDevice.toString())

            val productName = usbDevice.productName
            if (productName == null) {
                continue
            } else if (!productName.contains("ICOD")) {
                continue
            }
            printerUsbDevice = usbDevice
            Log.e(TAGS, "init: add " + usbDevice.toString())
        }

    }

    override fun init() {
        // 启动时候必须连好设备
        scanUsbPrinter()

        // singleton,所以只有这一个对象,连接不同的usb口
        currentPrinter = PrinterAPI.getInstance()
        currentPrinter.setOutput(false) // 禁用log 输出
//        connectFirstNormalPrinter()
    }

    // 连接0,1打印机
    @Throws(NoSuchElementException::class)
    fun connectPrinter(index: Int): Boolean {
        val isSwitching = (index != currentPrinterIndex)

        if (isSwitching && currentPrinter.isConnect) {
            Log.e(TAGS, "connectPrinter: disconnect ${currentPrinterIndex}")
            currentPrinter.disconnect()
        }

        // 不再重复连接
        if (currentPrinter.isConnect) {
            return true
        }

        var ret = PrinterAPI.SUCCESS
        if (index == 0) {
            if (printerUsbDevice == null) {
                Log.i(TAGS, "connectPrinter: skip bad printer #${index}")
                return false
            }

            val io = USBAPI(context, printerUsbDevice);
            Log.e(TAGS, "connectPrinter: usb")
            ret = currentPrinter.connect(io)

        } else {
            if (BuildConfig.DEBUG) {
                ret = PrinterAPI.FAIL
            } else {
                val io = SerialAPI(File("/dev/ttyS1"), 38400, 0)
                Log.e(TAGS, "connectPrinter: serial")
                ret = currentPrinter.connect(io)
            }
        }

        if (ret == PrinterAPI.SUCCESS)
        {
            Log.e(TAGS, "connectPrinter: connected to printer index ${index}")
            currentPrinterIndex = index

            return true
        }
        else {
            Log.i(TAGS, "connectPrinter: skip bad printer #${index}")
            return false
        }
    }

    override fun printTest()
    {
        val ticket = Ticket(1, "SZJAKHDKL", 0, 10, 10, "2022-01-01", TicketGoods(1, 10, "测试10次票", "", "100.0", 10))
        val order = Order(1, "2022010101283", "100", "100", 10, 10, 10, "2022-01-01")
        print(ticket, order, null, false)
    }

    override fun print(
        ticket: Ticket,
        order: Order?,
        good: Good?,
        isReprint: Boolean,
        qrcodeFormat: Int,
        extraData: Map<String, Any>
    ) {
        if (!currentPrinter.isConnect) {
            Log.i(TAGS, "no connected printer")
            return
        }

        Log.i(TAGS, "print on printer #${currentPrinterIndex}")

        var goodId = good?.id
        var goodName = good?.name
        var goodPrice = good?.price
        var goodCategory = good?.category
        var orderNo = order?.order_no
        ticket.goods?.let {
            goodId = it.id
            goodName = it.name
            goodPrice = it.price
            goodCategory = it.category
        }
        val let = ticket.order?.let {
            orderNo = it.order_no
        }

        var title = RECEIPT_TITLE?: "-"
        if (isReprint) {
            title = "（补打）\n$title"
        }
        var qrcodeStr = formatTicketQrcode(goodId, orderNo, ticket, qrcodeFormat)

        currentPrinter.setLineSpace(8)
        currentPrinter.setAlignMode(1)
        currentPrinter.setCharSize(0, 0)
        currentPrinter.printString(title)
        currentPrinter.printAndFeedLine(1)

        ticket.station?.let {
            currentPrinter.printString("出票站点: $it\n")
            currentPrinter.printFeed()
        }
        ticket.machine?.let {
            currentPrinter.printString("出票设备: $it\n")
            currentPrinter.printFeed()
        }
        currentPrinter.setCharSize(1, 1)
        currentPrinter.printString(CURRENT_STATION_NAME)
        currentPrinter.printAndFeedLine(2)
        currentPrinter.printQRCode(qrcodeStr, 10, false)
        currentPrinter.printAndFeedLine(2)
//        PrinterManager.usbPrinter.POS_Print2DBarcode(UsbPrinter.BAR_TYPE_2D._QR, 10, qrcode)
        goodName?.let {
            if (!it.contains("单")) {
                currentPrinter.setCharSize(0, 0)
                currentPrinter.printString("[在自助售票机扫码叫车]")
                currentPrinter.printAndFeedLine(2)
            }
        }
        currentPrinter.setCharSize(1, 1)
        val categoryName = GOOD_CATEGORY_MAP[goodCategory] ?: ""
        currentPrinter.printString(categoryName)
        currentPrinter.setEnableUnderLine(1)
        currentPrinter.printString(goodName)
        currentPrinter.setEnableUnderLine(0)
        currentPrinter.printFeed()
        currentPrinter.setCharSize(0, 0)
        currentPrinter.printFeed()

        currentPrinter.setAlignMode(0)
        currentPrinter.printString("------------------------------------------------")
        currentPrinter.printFeed()
        currentPrinter.printString("单价：${goodPrice}元")
        currentPrinter.printFeed()
        currentPrinter.printString("编号：${ticket.ticket_no}")
        currentPrinter.printFeed()
        currentPrinter.printString("购票时间：${ticket.created_at}")
        currentPrinter.printFeed()
        currentPrinter.printString("------------------------------------------------")

        // 温馨提示
        var ticketRules = GLOBAL_POLL?.data?.machineStaticConfig?.ticket_rules ?: DEFAULT_RULES_TICKET
        // 获取每个station覆盖的售票规则
        if (extraData.containsKey("ticket_rules")) {
            ticketRules = extraData["ticket_rules"]?.toString()
        }
        currentPrinter.setAlignMode(0)
        currentPrinter.printString(ticketRules?:"-")
        currentPrinter.printAndFeedLine(2)

        currentPrinter.printAndFeedLine(5)
        currentPrinter.setCharSize(0, 0)
        currentPrinter.cutPaper(66, 0)
        return;
    }

    private fun getStatusDescribe(getStatus: Int): Pair<Int, String> {
        return try {
            if (getStatus == -1) {
                return Pair(STATUS_DISCONNECTED, "未连接")
            }
            val builder = StringBuilder()
            val descriptBuffer = StringBuffer()
            val troubleBuffer = StringBuffer()

            var isError = false

            //错误状态
            if (getStatus and 0x100 > 0) {
                descriptBuffer.append("机械错误, ")
                troubleBuffer.append("MachineError|")
                isError = true
            }
            if (getStatus and 0x40 > 0) {
                descriptBuffer.append("可自动恢复错误, ")
                troubleBuffer.append("CorrectingError|")
                isError = true
            }
            if (getStatus and 0x80 > 0) {
                descriptBuffer.append("不可恢复错误, ")
                troubleBuffer.append("NotCorrectError|")
                isError = true
            }
            //脱机状态
            if (getStatus and 0x4 > 0) {
                descriptBuffer.append("发生错误, ")
                troubleBuffer.append("happen error|")
                isError = true
            }
            if (getStatus and 0x20 > 0) {
                descriptBuffer.append("盖板打开, ")
                troubleBuffer.append("box open|")
                isError = true
            }
            if (isError)
            {
                return Pair(STATUS_MALFUNCTION, descriptBuffer.toString())
            }

            //打印机状态
            if (getStatus and 0x1 > 0) {
                descriptBuffer.append("脱机, ")
                troubleBuffer.append("Offline|")
                isError = true
            }
//            if (getStatus and 0x2 > 0 || getStatus and 0x10 > 0) {
//                descriptBuffer.append("正在feed, ") //[8]
//                troubleBuffer.append("feeding|")
//            }
            if (isError)
            {
                // 对于这个打印机,按缺纸处理
                // return Pair(STATUS_DISCONNECTED, descriptBuffer.toString())
                return Pair(STATUS_OUT_OF_PAPER, descriptBuffer.toString())
            }

            //传感应状态
//            if (getStatus and 0x200 > 0) {
//                descriptBuffer.append("少纸, ") //[1]
//                troubleBuffer.append("PaperFew|")
//                isError = true
//            }
            if (getStatus and 0x400 > 0 || getStatus and 0x08 > 0) {
                descriptBuffer.append("缺纸, ")
                troubleBuffer.append("OutOfPaper|")
                isError = true
            }
            if (isError)
            {
                return Pair(STATUS_OUT_OF_PAPER, descriptBuffer.toString())
            }

            var descript = descriptBuffer.toString().trim { it <= ' ' }
            descript = if (!descript.isEmpty()) {
                descript.substring(0, descript.length - 1)
            } else {
                "正常"
            }
            Pair(STATUS_NORMAL, descript)
        } catch (e: java.lang.Exception) {
            Pair(STATUS_MALFUNCTION, "未知异常")
        }
    }

    fun setPrinterStatusText(index: Int, newStatus: Int, newRawStatusStr: String): PrinterStatus {
        var formatted = ""
        if (index == 0) {
            val superscript = "¹"
            PRINTER_1_STATUS = newStatus
            formatted = "打印机${superscript}${newRawStatusStr}".replace(", ", "")
            PRINTER_1_STATUS_STRING = formatted
        } else if (index == 1) {
            val superscript = "²"
            PRINTER_2_STATUS = newStatus
            formatted = "打印机${superscript}${newRawStatusStr}".replace(", ", "")
            PRINTER_2_STATUS_STRING = formatted
        }

        return PrinterStatus(index, "", newStatus, formatted)
    }

    override fun updatePrinterStatus(application: Application?): List<PrinterStatus> {
        Log.e(TAGS, "update status")

        val printerStatuses = mutableListOf<PrinterStatus>()
        var printerStatus: PrinterStatus

        // 检查当前
        val rawStatus = currentPrinter.getStatus()
        val (status, rawStatusStr) = getStatusDescribe(rawStatus)

        // 正常则返回,不检查另一个打印机
        if (status == 24) {
            // 当前打印机状态
            printerStatus = setPrinterStatusText(currentPrinterIndex, status, rawStatusStr)
            printerStatuses.add(printerStatus)

            // 另一个打印机状态
            if (currentPrinterIndex == 0) {
                printerStatuses.add(PrinterStatus(1, "", PRINTER_2_STATUS, PRINTER_2_STATUS_STRING))
            } else if (currentPrinterIndex == 1) {
                printerStatuses.add(PrinterStatus(0, "", PRINTER_1_STATUS, PRINTER_1_STATUS_STRING))
            }

            Log.e(TAGS, "I'm OK " + printerStatuses.toString())

            return printerStatuses
        }

        // 更新问题打印机文字
        printerStatus = setPrinterStatusText(currentPrinterIndex, status, rawStatusStr)
        printerStatuses.add(printerStatus)

        // 切换
        Log.e(TAGS, "switching from " + printerStatus.toString())
        val nextIndex = 1 - currentPrinterIndex
        var ret: Boolean
        try {
            ret = connectPrinter(nextIndex)
        } catch (e: NoSuchElementException){
            ret = false
        }
        if (ret) {
            // 更新下一个打印机文字
            val newRawStatus = currentPrinter.getStatus()
            val (newStatus, newRawStatusStr) = getStatusDescribe(newRawStatus)

            printerStatus = setPrinterStatusText(currentPrinterIndex, newStatus, newRawStatusStr)
        } else {
            printerStatus = setPrinterStatusText(currentPrinterIndex, -1, "打印机未连接")
        }
        printerStatuses.add(printerStatus)
        Log.e(TAGS, "switched to " + printerStatus.toString())

        return printerStatuses.toList()
    }

    override fun updateAllPrinterStatus(application: Application?): List<PrinterStatus> {
        Log.e(TAGS, "update status all")
        scanUsbPrinter()

        val status1: Int
        val status2: Int

        val printerStatuses = mutableListOf<PrinterStatus>()
        var printerStatus: PrinterStatus

        var index = 0

        // 更新打印机1
        var ret: Boolean
        try {
            ret = connectPrinter(index)
        } catch (e: NoSuchElementException){
            ret = false
        }

        if (ret) {
            val rawStatus = currentPrinter.getStatus()
            val (status, rawStatusStr) = getStatusDescribe(rawStatus)
            printerStatus = setPrinterStatusText(index, status, rawStatusStr)

            status1 = status
        } else {
            printerStatus = setPrinterStatusText(index, -1, "打印机未连接")

            status1 = -1
        }
        printerStatuses.add(printerStatus)

        Log.e(TAGS, "checkout 0: " + printerStatus.toString())

        // delay(2000) // 延迟可能导致连续买票的时候不出票？

        // 更新打印机2
        index = 1
        try {
            ret = connectPrinter(index)
        } catch (e: NoSuchElementException){
            ret = false
        }

        if (ret) {
            val rawStatus = currentPrinter.getStatus()
            val (status, rawStatusStr) = getStatusDescribe(rawStatus)
            printerStatus = setPrinterStatusText(index, status, rawStatusStr)
        } else {
            printerStatus = setPrinterStatusText(index, -1, "打印机未连接")
        }
        printerStatuses.add(printerStatus)
        Log.e(TAGS, "checkout 1: " + printerStatus.toString())

        // 如果打印机1正常,则切换回1
        if (status1 == 24) {
            connectPrinter(0)
        } else {
            // 无操作,已经连接打印机2
        }

        return printerStatuses
    }
}