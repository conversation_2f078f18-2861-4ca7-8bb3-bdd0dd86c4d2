package com.turinggear.ssa_kiosk.util

import android.app.Application
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.Order
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.util.TicketQrcode

const val STATUS_NORMAL = 24
const val STATUS_OUT_OF_PAPER = 56
const val STATUS_MALFUNCTION = 10
const val STATUS_MALFUNCTION_AND_OOP = 48
const val STATUS_MALFUNCTION_AND_NOT_OOP = 16
const val STATUS_DISCONNECTED = -1

data class PrinterStatus (
    val index: Int,
    val name: String,
    val status: Int,
    val statusStr: String
)

interface PrinterGroup {
    fun init()
    fun print(
        ticket: Ticket,
        order: Order? = null,
        good: Good? = null,
        isReprint: Boolean = false,
        qrcodeFormat: Int = TicketQrcode.FORMAT_ID,
        extraData: Map<String, Any> = emptyMap()
    )
    fun printTest()

    // 例行的更新打印机状态
    fun updatePrinterStatus(application: Application?): List<PrinterStatus>

    // 更新全部打印机状态,较少调用
    fun updateAllPrinterStatus(application: Application?): List<PrinterStatus>
}