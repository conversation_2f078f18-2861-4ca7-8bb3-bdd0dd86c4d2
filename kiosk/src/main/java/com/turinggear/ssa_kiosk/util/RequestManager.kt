package com.turinggear.ssa_kiosk.util

import android.annotation.SuppressLint
import android.app.Application
import com.turinggear.ssa_shared.util.SerialNumber
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.*
import com.turinggear.ssa_shared.util.KtorClient
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.http.content.*
import kotlinx.coroutines.delay
import kotlinx.serialization.decodeFromString

object RequestManager {

    suspend fun requestFirstAndLastStationsForCallCarWith(ticketId: String) {
        FIRST_STATION_FOR_CALL_CAR = null
        LAST_STATION_FOR_CALL_CAR = null
        try {
            val x: ResponseStationsForOneRoute =
                KtorClient.httpClient()
                    .get(urlString = apiTicketRoutes() + "/ticket_get_stations/${ticketId}").body()
            if (x.code == 200) {
                FIRST_STATION_FOR_CALL_CAR = x.data.first()
                LAST_STATION_FOR_CALL_CAR = x.data.last()
                SCANNED_TICKET_ID_CALL_CAR = ticketId
            }
        } catch (e: Exception) {
            e.localizedMessage
        }
    }

    suspend fun requestFirstAndLastStations() {
        FIRST_STATION_FOR_SELECTED_GOOD = null
        LAST_STATION_FOR_SELECTED_GOOD = null
        try {
            val x: ResponseStationsForOneRoute =
                KtorClient.httpClient()
                    .get(urlString = apiTicketRoutes() + "/${SELECTED_GOOD?.metadata?.route_id}/stations").body()
            if (x.code == 200) {
                FIRST_STATION_FOR_SELECTED_GOOD = x.data.first()
                LAST_STATION_FOR_SELECTED_GOOD = x.data.last()
            }
        } catch (e: Exception) {
            e.localizedMessage
        }
    }

    suspend fun callCarFrom(ticketId: String): String? {
        return try {
            val x: ResponseTicket =
                KtorClient.httpClient().put(urlString = apiTicketCall() + "/${ticketId}").body()
            if (x.code == 200) {
                return x.msg
            } else {
                return "${x.code}: ${x.msg}"
            }
        } catch (e: ClientRequestException) {
            try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                return "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            e.localizedMessage
        }
    }

    suspend fun directionRecord(stationId: String, order_id: String? = null, ticket_id: String? = null): String? {
        return try {
            val x: ResponseTicket =
                KtorClient.httpClient().put(urlString = apiTicketDirectionRecord() + "/${stationId}") {
                    var t = ""
                    order_id?.let {
                        t = "{\"order_id\": $it}"
                    }
                    ticket_id?.let {
                        t = "{\"ticket_id\": $it}"
                    }
                    if (t.isNotEmpty()) {
                        setBody(TextContent(
                            text = t,
                            contentType = ContentType.Application.Json
                        ))
                    }
                }.body()
            if (x.code == 200) {
                return x.msg
            } else {
                return "${x.code}: ${x.msg}"
            }
        } catch (e: ClientRequestException) {
            try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                return "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            e.localizedMessage
        }
    }

    @SuppressLint("HardwareIds")
    @Suppress("DEPRECATION")
    suspend fun registerDevice() {
        val serial = SerialNumber.originalSerial()
        val random = (1000..9999).random()
        try {
            val x: Register = KtorClient.httpClient().post(urlString = apiRegisterMachine()) {
                contentType(ContentType.Application.Json)
                setBody(BodyForRegister(
                    serial = serial,
                    type = 10,
                    random = "$random"
                ))
            }.body()
            HasTriedToRegisterAfterLaunched = true
            if (x.data == "注册申请无效") {
                REGISTER_ERROR_STRING = x.data + "\n序列号：$serial"
                SHOW_ALERT_FOR_REGISTER = true
            } else if (x.data != "已注册") {
                RANDOM_REGISTER_CODE = "$random"
                SHOW_ALERT_FOR_REGISTER = true
            }
        } catch (e: ClientRequestException) {
            REGISTER_ERROR_STRING = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            //SHOW_ALERT_FOR_REGISTER = true
        } catch (e: Exception) {
            REGISTER_ERROR_STRING = e.localizedMessage
            //SHOW_ALERT_FOR_REGISTER = true
        }
    }

    suspend fun startup(version_code: Int, serial: String = ""): AppVersion? {
        return try {
            STARTUP = KtorClient.httpClient().post(urlString = apiTicketStartup()) {
                setBody(TextContent(
                    text = "{\"version_code\": $version_code}",
                    contentType = ContentType.Application.Json
                ))
            }.body()
            STARTUP?.data?.appVersion
        } catch (e: Exception) {
            null
        }
    }

    suspend fun wechatPayWith(
        authCode: String,
        goodsId: Int,
        amount: Int,
        pay_playform: Int,
        qrcodeFormat: Int
    ) {

        // 先拿到支付码，再做所有后续请求。所有 2xx 会在 try 里，其他 code 进入 catch
        try {
            // one. 创建订单
            PAYMENT_STATE = PAYMENTSTATE.MAKING_ORDER
            val one: ResponseOrder = KtorClient.httpClient()
                .post(urlString = apiTicketOrders() + "/${goodsId}") {
                    contentType(ContentType.Application.Json)
                    setBody(BodyForOrderMake(
                        pay_platform = pay_playform,
                        num = amount
                    ))
                }.body()
            val orderNew = one.data
            // two. 支付订单（尝试支付，但不一定成功，比如未输入密码。即使成功，生成车票也需要一定时间）
            PAYMENT_STATE = PAYMENTSTATE.USERPAYING
            val two: ResponseOrder = KtorClient.httpClient()
                .put(urlString = apiTicketOrdersPay()) {
                    contentType(ContentType.Application.Json)
                    setBody(BodyForOrderPay(order = orderNew.order_no, authCode))
                }.body()
            BARCODE = ""
            val orderTriedPaid = two.data
            // three. 轮询结果（3秒钟1次，最多15次为了富余。有结果就结束并打印，一直无结果则给出相应提示）
            for (index in 1..15) {
                // 若用户关闭窗口，则不再轮询
                if (PAYMENT_STATE == PAYMENTSTATE.UNSTARTED) {
                    break
                }
                delay(3000)
//                val three: ResponseTicketList = KtorClient.httpClient()
//                    .get(urlString = apiTicketTickets() + "/${orderTriedPaid.order_no}").body()
                val three: ResponseTicketList = KtorClient.httpClient()
                    .get(urlString = apiTicketTickets(orderTriedPaid.order_no)).body()
                val tickets = three.data
                if (tickets != null) {
                    // 来自cssh需求,某些station会设置覆盖的售票规则,这里读取以打印
                    val overrided_ticker_rules = GLOBAL_POLL?.data?.stationInfo?.ticket_rules
                    val extraData = overrided_ticker_rules?.let { mapOf("ticket_rules" to it) } ?: emptyMap()

                    PAYMENT_STATE = PAYMENTSTATE.SUCCEED
                    SELECTED_DIRECTION_STATION_ID?.let {
                        directionRecord(stationId = it, order_id = orderTriedPaid.id.toString())
                    }
                    tickets.forEachIndexed { ticketIndex, ticket ->
                        SELECTED_GOOD?.let {

                            val printer1StatusBefore = PRINTER_1_STATUS
                            val printer2StatusBefore = PRINTER_2_STATUS
                            PrinterManager.shouldUpdatePrinterStatus(ApplicationContext as Application)
                            delay(1000)
                            val printer1StatusAfter = PRINTER_1_STATUS
                            val printer2StatusAfter = PRINTER_2_STATUS
                            val printer1StatusChanged = printer1StatusBefore != printer1StatusAfter
                            val printer2StatusChanged = printer2StatusBefore != printer2StatusAfter

                            if (printer1StatusChanged or printer2StatusChanged) {
                                if ((ticketIndex - 1) in (0..tickets.count())) {
                                    val previousTicket = tickets[ticketIndex - 1]
                                    PrinterManager.print(ticket = previousTicket, order = orderTriedPaid, good = it,
                                        qrcodeFormat = qrcodeFormat,
                                        extraData = extraData)
                                }
                            }
                            PrinterManager.print(ticket = ticket, order = orderTriedPaid, good = it,
                                qrcodeFormat = qrcodeFormat,
                                extraData = extraData
                            )
                            delay(1000)
                        }
                    }
                    delay(3000)
                    SHOW_POPUP = false
                    SCAN_TYPE = SCANTYPE.CALL_CAR
                    SELECTED_AMOUNT = 1
                    SELECTED_DIRECTION_STATION_ID = null
                    PAYMENT_STATE = PAYMENTSTATE.UNSTARTED
                    break
                }
                // 超时则提示失败
                if (index == 15) {
                    PAYMENT_STATE = PAYMENTSTATE.FAILED
                }
            }
        } catch (e: ClientRequestException) {
            PAYMENT_STATE = PAYMENTSTATE.FAILED
            val msg = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(msg)
        } catch (e: Exception) {
            PAYMENT_STATE = PAYMENTSTATE.FAILED
            e.localizedMessage?.let {
                SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(it)
            }
        }
    }

    suspend fun requestTicketsWith(orderNo: String) {
        ReprintTickets = null
        try {
            val x: ResponseTicketList = KtorClient.httpClient()
                .get(urlString = apiTicketTickets(orderNo)).body()
            x.data?.let {
                ReprintTickets = it
            }
        } catch (e: ClientRequestException) {
            val msg = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(msg)
        } catch (e: Exception) {
            e.localizedMessage?.let {
                SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(it)
            }
        }
    }

    suspend fun resentElectronicTicketsWith(orderNo: String) {
        try {
            val x: ResponseTicketList = KtorClient.httpClient()
                .get(urlString = "${apiTicketResentTickets()}/$orderNo").body()
            val msg = if (x.code == 200) {
                x.msg
            } else {
                "${x.code}: ${x.msg}"
            }
            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(msg)
        } catch (e: ClientRequestException) {
            val msg = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
            SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(msg)
        } catch (e: Exception) {
            e.localizedMessage?.let {
                SCAFFOLD_STATE?.snackbarHostState?.currentSnackbarData?.dismiss()
                SCAFFOLD_STATE?.snackbarHostState?.showSnackbar(it)
            }
        }
    }

    suspend fun poll(): Poll? {
        return try {
            KtorClient.httpClient().post(urlString = apiTicketPoll()).body()
        } catch (e: Exception) {
            null
        }
    }

    suspend fun requestInternet(): Int {
        var domain = "https://status.aliyun.com"
        if (com.turinggear.ssa_kiosk.BuildConfig.FLAVOR == "waterpark") {
            domain = "https://www.baidu.com"
        }
        return try {
            val x: HttpResponse = KtorClient.httpClient().head(urlString = domain).body()
            x.status.value
        } catch (e: Exception) {
            0
        }
    }

    suspend fun requestHealthApi(): Int {
        return try {
            val x: HttpResponse = KtorClient.httpClient().head(urlString = apiHealthCheck()).body()
            x.status.value
        } catch (e: Exception) {
            0
        }
    }
}
