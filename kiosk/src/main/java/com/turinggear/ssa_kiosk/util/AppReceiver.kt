package com.turinggear.ssa_kiosk.util

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import androidx.lifecycle.LifecycleCoroutineScope
import com.turinggear.ssa_shared.LocalNetworkConnected
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Suppress("DEPRECATION")
class AppReceiver(private val activity: Activity, private val lifecycleScope: LifecycleCoroutineScope) : BroadcastReceiver() {

    override fun onReceive(contxt: Context?, intent: Intent?) {
        when (intent?.action) {
            "android.hardware.usb.action.USB_DEVICE_ATTACHED" -> {
                lifecycleScope.launch {
                    delay(2000)
                    PrinterManager.updateAllPrinterStatus(application = activity.application)
                }
            }
            "android.hardware.usb.action.USB_DEVICE_DETACHED" -> {
                PrinterManager.updateAllPrinterStatus(application = activity.application)
            }
            "android.net.conn.CONNECTIVITY_CHANGE" -> {
                val cm = contxt?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val activeNetwork: android.net.NetworkInfo? = cm.activeNetworkInfo
                val isConnected: Boolean = (activeNetwork?.isConnectedOrConnecting == true)
                LocalNetworkConnected = isConnected
            }
        }
    }
}