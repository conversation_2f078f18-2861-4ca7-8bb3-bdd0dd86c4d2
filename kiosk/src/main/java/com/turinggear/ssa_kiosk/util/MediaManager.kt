package com.turinggear.ssa_kiosk.util

import android.content.Context
import android.media.MediaPlayer
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.SHOW_POPUP
import com.turinggear.ssa_shared.R

object MediaManager {
    var mediaPlayer: MediaPlayer? = null
    var loopPlayer: MediaPlayer = MediaPlayer.create(ApplicationContext, R.raw.loop)

    var isMuted = false

    fun mute() {
        isMuted = true
    }

    fun playLoopingNotice(enable: Boolean) {
        if (isMuted) {
            return
        }
        if (enable) {
            if (!loopPlayer.isPlaying) {
                loopPlayer.isLooping = true
                loopPlayer.seekTo(0)
                loopPlayer.start()
            }
        } else {
            if (loopPlayer.isPlaying) {
                loopPlayer.pause()
            }
        }
    }

    fun playMusic(context: Context?, resId: Int) {
        if (isMuted) {
            return
        }

        mediaPlayer =
            MediaPlayer.create(context, resId)
        mediaPlayer?.start()
    }

    fun stopMusic() {
        mediaPlayer?.stop()
    }
}