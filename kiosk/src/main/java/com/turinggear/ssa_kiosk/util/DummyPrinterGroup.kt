package com.turinggear.ssa_kiosk.util

import android.app.Application
import android.util.Log
import com.turinggear.ssa_kiosk.*
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.Order
import com.turinggear.ssa_shared.util.formatTicketQrcode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.nio.charset.Charset
import kotlin.text.toByteArray

// 调试打印机
class DummyPrinterGroup: PrinterGroup {
    private val TAG: String = "DummyPrinterGroup"

    override fun init() {
    }

    override fun print(
        ticket: Ticket,
        order: Order?,
        good: Good?,
        isReprint: Boolean,
        qrcodeFormat: Int,
        extraData: Map<String, Any>
    ) {
        Log.e(TAG, "dummy print")
        if (extraData.containsKey("ticket_rules")) {
            Log.e(TAG, "ticket_rules: " + extraData["ticket_rules"])
        }
        var goodId = good?.id
        var goodName = good?.name
        var goodPrice = good?.price
        var goodCategory = good?.category
        var orderNo = order?.order_no
        ticket.goods?.let {
            goodId = it.id
            goodName = it.name
            goodPrice = it.price
            goodCategory = it.category
        }

        ticket.order?.let {
            orderNo = it.order_no
        }

        Log.e(TAG, "goodId: $goodId, goodName: $goodName, goodPrice: $goodPrice, goodCategory: $goodCategory")

        var title = RECEIPT_TITLE
        if (isReprint) {
            Log.e(TAG, "补打")
        }
        var qrcodeStr = formatTicketQrcode(goodId, orderNo, ticket, qrcodeFormat)
        Log.e(TAG, "qrcode: " + qrcodeStr)
    }

    override fun printTest() {
        return
    }

    override fun updatePrinterStatus(application: Application?): List<PrinterStatus> {
        val scope = CoroutineScope(Dispatchers.Main)
        scope.launch {
            PRINTER_1_STATUS = 24
            PRINTER_1_STATUS_STRING = when (PRINTER_1_STATUS) {
                24 -> "打印机¹正常"
                56 -> "打印机¹缺纸"
                -1 -> "打印机¹未连接"
                else -> "打印机¹异常"
            }
            delay(2000)
            PRINTER_2_STATUS = 24
            PRINTER_2_STATUS_STRING = when (PRINTER_2_STATUS) {
                24 -> "打印机²正常"
                56 -> "打印机²缺纸"
                -1 -> "打印机²未连接"
                else -> "打印机²异常"
            }
        }

        return listOf()
    }

    override fun updateAllPrinterStatus(application: Application?): List<PrinterStatus> {
        return updatePrinterStatus(application)
    }
}