package com.turinggear.ssa_kiosk.util

import android.app.Activity
import android.app.admin.DevicePolicyManager
import android.app.admin.SystemUpdatePolicy
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.PowerManager
import android.os.UserManager
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import com.turinggear.ssa_kiosk.MainActivity
import com.turinggear.ssa_kiosk.MyDeviceAdminReceiver
import java.lang.Exception

/**
 *
1. 参考代码
- [How to turn your Android application into a kiosk | Snowdog](https://snow.dog/blog/kiosk-mode-android)
源码在
[mrugacz95/kiosk: Example of Kiosk Mode for Android](https://github.com/mrugacz95/kiosk)
- [AndroidRootedKiosk.md](https://gist.github.com/Katee/4c7f499f350b4c603154)

2. 使用技术
依赖是android本身的
[Device administration overview  |  Android Developers](https://developer.android.com/guide/topics/admin/device-admin)
和root

3. 测试环境
genymotion模拟器自带root应用
[Root access - Device image User Guide](https://docs.genymotion.com/paas/10_Using_root_access/)

4. 其他测试工具
(参数的-e是针对genymotion模拟器 tcp/ip连接这种 **************:5555     device)
设置为owner
adb  -e shell dpm set-device-owner com.turinggear.ssa_kiosk/.MyDeviceAdminReceiver
移除owner
adb  -e shell dpm remove-active-admin com.turinggear.ssa_kiosk/.MyDeviceAdminReceiver
android studio重新运行前需要移除owner
 */
class DeviceAdminManager {
    private lateinit var mActivity: Activity
    private lateinit var mAdminComponentName: ComponentName
    private lateinit var mDevicePolicyManager: DevicePolicyManager

    constructor(ctx: Activity)
    {
        mActivity = ctx
        mAdminComponentName = MyDeviceAdminReceiver.getComponentName(ctx)
        mDevicePolicyManager = ctx.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    }

    @Deprecated("")
    fun enableOwner() {
        runShellCommand(listOf("su", "-c", "dpm set-device-owner ${mActivity.packageName}/.MyDeviceAdminReceiver"));
    }

    @Deprecated("")
    fun disableOwner() {
        runShellCommand(listOf("su", "-c", "dpm remove-active-admin ${mActivity.packageName}/.MyDeviceAdminReceiver"));
    }

    @Deprecated("")
    private fun runShellCommand(commands: List<String>) {
        try {
            ProcessBuilder(commands)
//                .redirectOutput(ProcessBuilder.Redirect.INHERIT)
                .start()
                .waitFor()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun reboot()
    {
        mDevicePolicyManager.reboot(mAdminComponentName)
    }

    // A Device Owner is an application that runs as a device administrator
    fun isOwner() = mDevicePolicyManager.isDeviceOwnerApp(mActivity.packageName)

    fun setKioskMode(enable: Boolean) {
        setRestrictions(enable)
//        enableStayOnWhilePluggedIn(enable)
        setUpdatePolicy(enable)
        setAsHomeApp(enable)
//        setKeyGuardEnabled(enable)
//        setLockTask(enable, true)
        // 如果固件支持隐藏顶部status bar和底部navigation bar,*不要*也不再需要调用此函数
//        setImmersiveMode(enable)

        // 禁用截屏
        mActivity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
    }

    // region restrictions
    private fun setRestrictions(disallow: Boolean) {
//        setUserRestriction(UserManager.DISALLOW_SAFE_BOOT, disallow)
//        setUserRestriction(UserManager.DISALLOW_FACTORY_RESET, disallow)
        setUserRestriction(UserManager.DISALLOW_ADD_USER, disallow)
//        setUserRestriction(UserManager.DISALLOW_MOUNT_PHYSICAL_MEDIA, disallow)
        setUserRestriction(UserManager.DISALLOW_ADJUST_VOLUME, false)
//        mDevicePolicyManager.setStatusBarDisabled(mAdminComponentName, disallow)

        mDevicePolicyManager.clearUserRestriction(mAdminComponentName, UserManager.DISALLOW_INSTALL_APPS);
        mDevicePolicyManager.clearUserRestriction(mAdminComponentName, UserManager.DISALLOW_INSTALL_UNKNOWN_SOURCES);

    }

    private fun setUserRestriction(restriction: String, disallow: Boolean) = if (disallow) {
        mDevicePolicyManager.addUserRestriction(mAdminComponentName, restriction)
    } else {
        mDevicePolicyManager.clearUserRestriction(mAdminComponentName, restriction)
    }
    // endregion

    private fun enableStayOnWhilePluggedIn(active: Boolean) = if (active) {
        mDevicePolicyManager.setGlobalSetting(
            mAdminComponentName,
            Settings.Global.STAY_ON_WHILE_PLUGGED_IN,
            (BatteryManager.BATTERY_PLUGGED_AC
                    or BatteryManager.BATTERY_PLUGGED_USB
                    or BatteryManager.BATTERY_PLUGGED_WIRELESS).toString()
        )
    } else {
        mDevicePolicyManager.setGlobalSetting(mAdminComponentName, Settings.Global.STAY_ON_WHILE_PLUGGED_IN, "0")
    }

    private fun setLockTask(start: Boolean, isAdmin: Boolean) {
        if (isAdmin) {
            mDevicePolicyManager.setLockTaskPackages(
                mAdminComponentName, if (start) arrayOf(mActivity.packageName) else arrayOf()
            )
        }
        if (start) {
            mActivity.startLockTask()
        } else {
            mActivity.stopLockTask()
        }
    }

    private fun setUpdatePolicy(enable: Boolean) {
        if (enable) {
            mDevicePolicyManager.setSystemUpdatePolicy(
                mAdminComponentName,
                SystemUpdatePolicy.createWindowedInstallPolicy(60, 120)
            )
        } else {
            mDevicePolicyManager.setSystemUpdatePolicy(mAdminComponentName, null)
        }
    }

    private fun setAsHomeApp(enable: Boolean) {
        if (enable) {
            val intentFilter = IntentFilter(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
                addCategory(Intent.CATEGORY_DEFAULT)
            }
            // TODO: MainActivity动态获取
            mDevicePolicyManager.addPersistentPreferredActivity(
                mAdminComponentName, intentFilter, ComponentName(mActivity.packageName, MainActivity::class.java.name)
            )
        } else {
            mDevicePolicyManager.clearPackagePersistentPreferredActivities(
                mAdminComponentName, mActivity.packageName
            )
        }
    }

    private fun setKeyGuardEnabled(enable: Boolean) {
        mDevicePolicyManager.setKeyguardDisabled(mAdminComponentName, !enable)
    }

     private fun setImmersiveMode(enable: Boolean) {
         if (enable) {
             val flags = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                     or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                     or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                     or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                     or View.SYSTEM_UI_FLAG_FULLSCREEN
                     or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
             mActivity.window.decorView.systemUiVisibility = flags
         } else {
             val flags = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                     or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                     or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
             mActivity.window.decorView.systemUiVisibility = flags
         }
     }
}