package com.turinggear.ssa_kiosk.util

import android.app.Application
import com.siupo.usbdriver.UsbPrinter
import com.siupo.usbdriver.UsbPrinter.*
import com.turinggear.ssa_kiosk.*
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.Order
import com.turinggear.ssa_shared.model.TicketGoods
import com.turinggear.ssa_shared.util.formatTicketQrcode
import java.nio.charset.Charset
import kotlin.text.contains
import kotlin.text.toByteArray

// 讯普打印机
class SiupoPrinterGroup: PrinterGroup {
    private val TAGS: String = "PrinterGroup"

    private var usbPrinter: UsbPrinter = UsbPrinter()
    override fun init() {
    }

    override fun print(
        ticket: Ticket,
        order: Order?,
        good: Good?,
        isReprint: <PERSON>olean,
        qrcodeFormat: Int,
        extraData: Map<String, Any>
    ) {
        var goodId = good?.id
        var goodName = good?.name
        var goodPrice = good?.price
        var goodCategory = good?.category
        var orderNo = order?.order_no
        ticket.goods?.let {
            goodId = it.id
            goodName = it.name
            goodPrice = it.price
            goodCategory = it.category
        }
        ticket.order?.let {
            orderNo = it.order_no
        }

        var title = RECEIPT_TITLE
        if (isReprint) {
            title = "（补打）\n$title"
        }
        var qrcodeStr = formatTicketQrcode(goodId, orderNo, ticket, qrcodeFormat)
        val qrcode: ByteArray = qrcodeStr.toByteArray(charset = Charset.forName("GBK"))

        usbPrinter.POS_SetLineSpace(8.toByte())
        usbPrinter.POS_SetAlign(ALIGN._MIDDLE)
        usbPrinter.POS_PrintString(title)
        usbPrinter.POS_PrintAndFeedNDotLine(FEED_DIR._NORMAL, 10.toByte())
        usbPrinter.POS_SetFont(0.toByte(), 2.toByte(), 2.toByte())
        usbPrinter.POS_PrintString(CURRENT_STATION_NAME)
        usbPrinter.POS_Print2DBarcode(BAR_TYPE_2D._QR, 10, qrcode)
        if (goodCategory in arrayOf(CATEGORY_BUS, CATEGORY_TRAIN)) {
            goodName?.let {
                if (!it.contains("单")) {
                    usbPrinter.POS_SetFont(0.toByte(), 1.toByte(), 1.toByte())
                    usbPrinter.POS_PrintString("[在自助售票机扫码叫车]")
                    usbPrinter.POS_PrintAndFeedNDotLine(FEED_DIR._NORMAL, 10.toByte())
                }
            }
        }
        usbPrinter.POS_SetFont(0.toByte(), 2.toByte(), 2.toByte())
        val categoryName = GOOD_CATEGORY_MAP[goodCategory] ?: ""
        usbPrinter.POS_PrintString(categoryName)
        usbPrinter.POS_SetUnderLine(FONT_UNDERLINE._ENABLE)
        usbPrinter.POS_PrintString(goodName)
        usbPrinter.POS_SetUnderLine(FONT_UNDERLINE._DISABLE)
        usbPrinter.POS_SetFont(0.toByte(), 1.toByte(), 1.toByte())
        usbPrinter.POS_PrintAndFeedNLine(FEED_DIR._NORMAL, 1.toByte())

        usbPrinter.POS_SetAlign(ALIGN._LEFT)
        usbPrinter.POS_PrintString("------------------------------------------------")
        usbPrinter.POS_PrintString("单价：${goodPrice}元")
        usbPrinter.POS_PrintString("编号：${ticket.ticket_no}")
        usbPrinter.POS_PrintString("购票时间：${ticket.created_at}")
        usbPrinter.POS_PrintString("------------------------------------------------")

        // 温馨提示
        val ticketRules = GLOBAL_POLL?.data?.machineStaticConfig?.ticket_rules ?: ""
        usbPrinter.POS_PrintString(ticketRules)
        usbPrinter.POS_PrintAndFeedNLine(FEED_DIR._NORMAL, 2.toByte())
        usbPrinter.POS_CutPaper(CUT_MODE.TYPE_FEED2CUT, 0.toByte())
//        Log.e(TAGS, "current printer serial " + usbPrinter.PrinterModel)
    }

    override fun printTest() {
        val ticket = Ticket(1, "SZJAKHDKL", 0, 10, 10, "2022-01-01", TicketGoods(1, 10, "测试10次票(shiyuan)", "", "100.0", 10))
        val order = Order(1, "2022010101283", "100", "100", 10, 10, 10, "2022-01-01")
        print(ticket, order, null, false)
        return
    }

    override fun updatePrinterStatus(application: Application?): List<PrinterStatus> {
//        Log.e(TAGS, "update printer status")

        val printerStatuses = mutableListOf<PrinterStatus>()

//        val scope = CoroutineScope(Dispatchers.Main)
//        scope.launch {
            usbPrinter = UsbPrinter()
            usbPrinter.PrinterModel = 1
            usbPrinter.init(application)
            PRINTER_1_STATUS = usbPrinter.GetPortStatus()
            PRINTER_1_STATUS_STRING = when (PRINTER_1_STATUS) {
                24 -> "打印机¹正常"
                56 -> "打印机¹缺纸"
                -1 -> "打印机¹未连接"
                else -> "打印机¹异常"
            }

            printerStatuses.add(PrinterStatus(0, "", PRINTER_1_STATUS, PRINTER_1_STATUS_STRING))

//            delay(2000)
            usbPrinter = UsbPrinter()
            usbPrinter.PrinterModel = 2
            usbPrinter.init(application)
            PRINTER_2_STATUS = usbPrinter.GetPortStatus()
            PRINTER_2_STATUS_STRING = when (PRINTER_2_STATUS) {
                24 -> "打印机²正常"
                56 -> "打印机²缺纸"
                -1 -> "打印机²未连接"
                else -> "打印机²异常"
            }
            printerStatuses.add(PrinterStatus(1, "", PRINTER_2_STATUS, PRINTER_2_STATUS_STRING))

            if (PRINTER_1_STATUS == 24) {
                usbPrinter = UsbPrinter()
                usbPrinter.PrinterModel = 1
                usbPrinter.init(application)
            }
            usbPrinter.tipUsbEnable = false

//            Log.e(TAGS, printerStatuses.toString())
//            Log.e(TAGS, "current printer serial " + usbPrinter.PrinterModel)
//        }
        return listOf()
    }

    override fun updateAllPrinterStatus(application: Application?): List<PrinterStatus> {
        return updatePrinterStatus(application)
    }
}