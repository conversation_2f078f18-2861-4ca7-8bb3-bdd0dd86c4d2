package com.turinggear.ssa_kiosk.util

import android.app.Application
import android.content.Context
import android.content.Intent
import android.util.Log
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.service.ScheduleService
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.Order
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.util.KtorClient
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

object PrinterManager {
    private val TAGS: String = "PrinterManager"
    private lateinit var printerGroup: PrinterGroup

    /**
    打印机获取状态操作耗时 (不包括事件上报)

    注意实际的执行包括检查打印机和上报事件, 这里因为无需上报所以返回快
    对于购票等地方,不用等到事件上报结束

    检查单个打印机的延迟为15ms
    2022-10-23 21:54:11.359 28684-28684 ScheduleService         com.turinggear.ssa_kiosk             E  start
    2022-10-23 21:54:11.369 28684-28684 ScheduleService         com.turinggear.ssa_kiosk             E  insert task
    2022-10-23 21:54:11.374 28684-28684 ScheduleService         com.turinggear.ssa_kiosk             E  task finished

    检查两个打印机的延迟为 150ms 或者 63ms
    app启动时候
    2022-10-23 21:53:45.780 28684-28684 ScheduleService         com.turinggear.ssa_kiosk             E  run
    2022-10-23 21:53:45.833 28684-28684 ScheduleService         com.turinggear.ssa_kiosk             E  result 打印机¹正常 / 打印机²正常
    循环运行时候
    2022-10-23 21:58:45.834 28684-28684 ScheduleService         com.turinggear.ssa_kiosk             E  run
    2022-10-23 21:58:45.887 28684-28684 ScheduleService         com.turinggear.ssa_kiosk             E  result 打印机¹正常 / 打印机²正常
     */
    public const val PRINTER_CHECK_TIME_MS: Long = 100

    fun init(context: Context)
    {
        printerGroup = when(BuildConfig.FLAVOR) {
            "main" -> DummyPrinterGroup()
            "yqh" -> IcodPrinterGroup(context)
            "shiyuan" -> SiupoPrinterGroup()
            "jiuzhouwa" -> SiupoPrinterGroup()
            "cssh" -> IcodMixedPrinterGroup(context)
            "csshboat" -> IcodMixedPrinterGroup(context)
            "shg" -> IcodMixedPrinterGroup(context)
            "waterpark" -> DummyPrinterGroup()
            "hdboat" -> SiupoPrinterGroup()
            "lihuboat" -> CaysnPrinterGroup(context)
            "fhxdboat" -> IcodPrinterGroup(context)
            else -> SiupoPrinterGroup()
        }
        if (BuildConfig.DEBUG) printerGroup = DummyPrinterGroup()
        printerGroup.init()
    }

    // extraData用于附加用来打印的数据
    fun print(
        ticket: Ticket,
        order: Order? = null,
        good: Good? = null,
        isReprint: Boolean = false,
        qrcodeFormat: Int,
        extraData: Map<String, Any> = emptyMap()
    ) {
        printerGroup.print(ticket, order, good, isReprint, qrcodeFormat, extraData)
    }

    // 异步调用
    // 所有打印机查询状态操作都在service里,这样在handler里事件排队,避免并行访问问题
    fun shouldUpdatePrinterStatus(application: Application) {
        val intent = Intent(application.applicationContext, ScheduleService::class.java)
        intent.putExtra("cmd", "printer_check_and_report_manual")
        application.applicationContext.startService(intent)
    }

    fun updatePrinterStatus(application: Application) {
        val ok = intArrayOf(-1, 24)
        if ((PRINTER_1_STATUS !in ok) and (PRINTER_2_STATUS !in ok)) {
            printerGroup.updateAllPrinterStatus(application)
        } else {
            printerGroup.updatePrinterStatus(application)
        }

        if (BuildConfig.FLAVOR == "shiyuan") {
            reportEventAboutPrinterShiyuan()
        } else {
            reportEventAboutPrinter()
        }
    }

    fun updateAllPrinterStatus(application: Application) {
        printerGroup.updateAllPrinterStatus(application)
        if (BuildConfig.FLAVOR == "shiyuan") {
            reportEventAboutPrinterShiyuan()
        } else {
            reportEventAboutPrinter()
        }
    }

    @Serializable
    data class BodyForEvent(val event_type: Int, val event_message: String)

    private fun reportEventAboutPrinter() {
//        if (BuildConfig.DEBUG) {
//            return
//        }

        if (CURRENT_STATION_NAME.isEmpty()) return

        // 0：不上报，10：一台或两台缺纸时上报，30：一台或两台异常时上报 // 未连接状态暂不上报，一般是网络问题
        val ok = intArrayOf(-1, 24)

        var eventType = 0
        var statusString = ""

        if ((PRINTER_1_STATUS !in ok) and (PRINTER_2_STATUS !in ok)) {
            eventType = 30
            statusString = PRINTER_1_STATUS_STRING + " / " + PRINTER_2_STATUS_STRING
        } else if (PRINTER_1_STATUS !in ok) {
            eventType = 10
            statusString = PRINTER_1_STATUS_STRING
        } else if (PRINTER_2_STATUS !in ok) {
            eventType = 10
            statusString = PRINTER_2_STATUS_STRING
        } else {
            eventType = 0
            statusString = PRINTER_1_STATUS_STRING + " / " + PRINTER_2_STATUS_STRING
        }

        Log.e(TAGS, "${eventType} ${statusString}")

        if (eventType == 0) return

        val scope = CoroutineScope(Dispatchers.Main)
        scope.launch {
            delay(3000)
            try {
                KtorClient.httpClient().post(urlString = apiTicketReportEvents()) {
                    contentType(ContentType.Application.Json)
                    setBody(BodyForEvent(
                        event_type = eventType,
                        event_message = "$CURRENT_STATION_NAME: $statusString"
                    ))
                }
            } catch (e: Exception) {
                println(e.localizedMessage)
            }
        }
    }

    private fun reportEventAboutPrinterShiyuan() {
        if (BuildConfig.DEBUG) {
            return
        }

        if (CURRENT_STATION_NAME.isEmpty()) return

        // 0：不上报，10：一台或两台缺纸时上报，30：一台或两台异常时上报 // 未连接状态暂不上报，一般是网络问题
        var eventType = 0
        if ((PRINTER_1_STATUS == 56) or (PRINTER_2_STATUS == 56)) {
            eventType = 10
        }
        if ((PRINTER_1_STATUS != -1) and (PRINTER_1_STATUS != 24) and (PRINTER_1_STATUS != 56) or
            (PRINTER_2_STATUS != -1) and (PRINTER_2_STATUS != 24) and (PRINTER_2_STATUS != 56)) {
            eventType = 30
        }
        var statusString = ""
        if ((PRINTER_1_STATUS != 24) and (PRINTER_1_STATUS != -1)) {
            statusString += PRINTER_1_STATUS_STRING
        }
        if ((PRINTER_2_STATUS != 24) and (PRINTER_2_STATUS != -1)) {
            if (statusString.isNotEmpty()) {
                statusString += " / "
            }
            statusString += PRINTER_2_STATUS_STRING
        }

        if (eventType == 0) return

        val scope = CoroutineScope(Dispatchers.Main)
        scope.launch {
            delay(3000)
            try {
                KtorClient.httpClient().post(urlString = apiTicketReportEvents()) {
                    contentType(ContentType.Application.Json)
                    setBody(BodyForEvent(
                        event_type = eventType,
                        event_message = "$CURRENT_STATION_NAME: $statusString"
                    ))
                }
            } catch (e: Exception) {
                println(e.localizedMessage)
            }
        }
    }

    fun printTest() {
        printerGroup.printTest()
    }
}
