package com.turinggear.ssa_kiosk.util


import android.app.Application
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Typeface
import android.text.format.DateFormat
import android.util.Log
import com.caysn.autoreplyprint.AutoReplyPrint
import com.caysn.autoreplyprint.AutoReplyPrint.CP_OnPortClosedEvent_Callback
import com.caysn.autoreplyprint.AutoReplyPrint.CP_OnPortOpenFailedEvent_Callback
import com.caysn.autoreplyprint.AutoReplyPrint.CP_OnPortOpenedEvent_Callback
import com.caysn.autoreplyprint.AutoReplyPrint.CP_OnPrinterPrintedEvent_Callback
import com.caysn.autoreplyprint.AutoReplyPrint.CP_OnPrinterReceivedEvent_Callback
import com.caysn.autoreplyprint.AutoReplyPrint.CP_OnPrinterStatusEvent_Callback
import com.caysn.autoreplyprint.AutoReplyPrint.CP_Pos_Alignment_HCenter
import com.caysn.autoreplyprint.AutoReplyPrint.CP_Pos_Alignment_Left
import com.caysn.autoreplyprint.AutoReplyPrint.CP_PrinterStatus
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.common.BitMatrix
import com.google.zxing.qrcode.QRCodeWriter
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
import com.sun.jna.Pointer
import com.turinggear.ssa_kiosk.DEFAULT_RULES_TICKET
import com.turinggear.ssa_kiosk.RECEIPT_TITLE
import com.turinggear.ssa_shared.CURRENT_STATION_NAME
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.GOOD_CATEGORY_MAP
import com.turinggear.ssa_shared.PRINTER_1_STATUS
import com.turinggear.ssa_shared.PRINTER_1_STATUS_STRING
import com.turinggear.ssa_shared.PRINTER_2_STATUS
import com.turinggear.ssa_shared.PRINTER_2_STATUS_STRING
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.Order
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.model.TicketGoods
import com.turinggear.ssa_shared.util.TicketQrcode
import com.turinggear.ssa_shared.util.formatTicketQrcode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.Calendar

// 硬纸打印机
class CaysnPrinterGroup: PrinterGroup {
    private val TAGS: String = "CaysnPrinterGroup"

    private var context: Context

    var printer1Status: Int = -1
    var printer2Status: Int = -1

    fun setPrinterStatus(h: Pointer?, status: Int) {
            if (h == printers[0]) {
                printer1Status = status
            } else {
                printer2Status = status
            }
    }

    var opened_callback: CP_OnPortOpenedEvent_Callback =
        CP_OnPortOpenedEvent_Callback { handle, name, private_data ->
            Log.e(TAGS, "opened_callback")
        }

    var openfailed_callback: CP_OnPortOpenFailedEvent_Callback =
        CP_OnPortOpenFailedEvent_Callback { handle, name, private_data ->
            Log.e(TAGS, "openfailed_callback")
        }
    var closed_callback: CP_OnPortClosedEvent_Callback =
        CP_OnPortClosedEvent_Callback { h, private_data ->
            Log.e(TAGS, "closed_callback")

            setPrinterStatus(h, -1)
        }
    var status_callback: CP_OnPrinterStatusEvent_Callback =
        CP_OnPrinterStatusEvent_Callback { h, printer_error_status, printer_info_status, private_data ->
            if (h == printers[0]) {
                Log.e(TAGS, "printer 1 status: $printer_error_status, $printer_info_status")
            } else {
                Log.e(TAGS, "printer 2 status: $printer_error_status, $printer_info_status")
            }

            val status = CP_PrinterStatus(printer_error_status, printer_info_status)
            if (status.ERROR_OCCURED()) {
//                var error_status_string = ""
//                if (status.ERROR_CUTTER()) error_status_string += "[ERROR_CUTTER]"
//                if (status.ERROR_FLASH()) error_status_string += "[ERROR_FLASH]"
//                if (status.ERROR_NOPAPER()) error_status_string += "[ERROR_NOPAPER]"
//                if (status.ERROR_VOLTAGE()) error_status_string += "[ERROR_VOLTAGE]"
//                if (status.ERROR_MARKER()) error_status_string += "[ERROR_MARKER]"
//                if (status.ERROR_ENGINE()) error_status_string += "[ERROR_MOVEMENT]"
//                if (status.ERROR_OVERHEAT()) error_status_string += "[ERROR_OVERHEAT]"
//                if (status.ERROR_COVERUP()) error_status_string += "[ERROR_COVERUP]"
//                if (status.ERROR_MOTOR()) error_status_string += "[ERROR_MOTOR]"
//                Log.e(TAGS, "printer: $error_status_string")

                if (status.ERROR_NOPAPER() || status.ERROR_MOTOR()) {
                    setPrinterStatus(h, 56)
                } else {
                    setPrinterStatus(h, (printer_info_status and 0xffffL).toInt())
                }
            } else {
                setPrinterStatus(h, 24)
                Log.e(TAGS, "printer: ok")
            }
        }
    var received_callback: CP_OnPrinterReceivedEvent_Callback =
        CP_OnPrinterReceivedEvent_Callback { h, printer_received_byte_count, private_data ->
            val calendar = Calendar.getInstance()
            val calendarDate = calendar.time
            val time = DateFormat.format("yyyy-MM-dd kk:mm:ss", calendarDate).toString()
            Log.e(TAGS, "$time PrinterReceived: $printer_received_byte_count")
        }
    var printed_callback: CP_OnPrinterPrintedEvent_Callback =
        CP_OnPrinterPrintedEvent_Callback { h, printer_printed_page_id, private_data ->
            val calendar = Calendar.getInstance()
            val calendarDate = calendar.time
            val time = DateFormat.format("yyyy-MM-dd kk:mm:ss", calendarDate).toString()
            Log.e(TAGS, "$time PrinterPrinted: $printer_printed_page_id")
        }

    private fun AddCallback() {
//        AutoReplyPrint.INSTANCE.CP_Port_AddOnPortOpenedEvent(opened_callback, Pointer.NULL)
//        AutoReplyPrint.INSTANCE.CP_Port_AddOnPortOpenFailedEvent(openfailed_callback, Pointer.NULL)
        AutoReplyPrint.INSTANCE.CP_Port_AddOnPortClosedEvent(closed_callback, Pointer.NULL)
        AutoReplyPrint.INSTANCE.CP_Printer_AddOnPrinterStatusEvent(status_callback, Pointer.NULL)
//        AutoReplyPrint.INSTANCE.CP_Printer_AddOnPrinterReceivedEvent(received_callback, Pointer.NULL)
//        AutoReplyPrint.INSTANCE.CP_Printer_AddOnPrinterPrintedEvent(printed_callback, Pointer.NULL)
    }

    private fun RemoveCallback() {
//        AutoReplyPrint.INSTANCE.CP_Port_RemoveOnPortOpenedEvent(opened_callback)
//        AutoReplyPrint.INSTANCE.CP_Port_RemoveOnPortOpenFailedEvent(openfailed_callback)
        AutoReplyPrint.INSTANCE.CP_Port_RemoveOnPortClosedEvent(closed_callback)
        AutoReplyPrint.INSTANCE.CP_Printer_RemoveOnPrinterStatusEvent(status_callback)
//        AutoReplyPrint.INSTANCE.CP_Printer_RemoveOnPrinterReceivedEvent(received_callback)
//        AutoReplyPrint.INSTANCE.CP_Printer_RemoveOnPrinterPrintedEvent(printed_callback)
    }

    private fun EnumUsb(): ArrayList<String> {
        var usbDevices = ArrayList<String>()
        usbDevices.clear()
        val devicePaths = AutoReplyPrint.CP_Port_EnumUsb_Helper.EnumUsb()
        if (devicePaths != null) {
            for (i in devicePaths.indices) {
                val name = devicePaths[i]
                usbDevices.add(name)
            }
        }
        return usbDevices
    }

    private fun EnumCom(): ArrayList<String> {
        var comDevices = ArrayList<String>()
        comDevices.clear()
        val devicePaths = AutoReplyPrint.CP_Port_EnumCom_Helper.EnumCom()
        if (devicePaths != null) {
            for (i in devicePaths.indices) {
                val name = devicePaths[i]
                comDevices.add(name)
            }
        }
        return comDevices
    }

    var printers = arrayOfNulls<Pointer?>(2)
    // 当前打印机索引 0/1
    // 固定0为usb 1为串口
    var currentPrinterIndex = 0

    var currentPrinter: Pointer? = null

    constructor(context: Context) {
        this.context = context
    }

    override fun init() {
        AddCallback()

        Log.e(TAGS, "init")
        var usbDevices = EnumUsb()
        // 只找第一个
        for ( d in usbDevices) {
            Log.e(TAGS, "printer: " + d)

            val h = AutoReplyPrint.INSTANCE.CP_Port_OpenUsb(d, 1)
            if (h != null){
                Log.e(TAGS, "usb printer: " + d)
                printers[0] = h
                break
            }
        }

        val comDevices = EnumCom()
        // 只找第一个
        for ( d in comDevices) {
            Log.e(TAGS, "com printer: " + d)
            val h = AutoReplyPrint.INSTANCE.CP_Port_OpenCom(
                d,
                115200,
                AutoReplyPrint.CP_ComDataBits_8,
                AutoReplyPrint.CP_ComParity_NoParity,
                AutoReplyPrint.CP_ComStopBits_One,
                0,
                1
            )
            if (h != null){
                printers[1] = h
                break
            }
        }

        for (printer in printers) {
            if (printer != null) {
                currentPrinter = printer
                currentPrinterIndex = printers.indexOf(printer)
                break
            }
        }

        // 不用设置 调用本行会在纸上打印ENABLE字样
//        if (h != null) {
//            AutoReplyPrint.INSTANCE.CP_BlackMark_EnableBlackMarkMode(h)
//        }
    }

    // 调试
    fun saveBitmapToFile(bitmap: Bitmap, filePath: String) {
        val file = File(filePath)
        var out: FileOutputStream? = null
        try {
            out = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            try {
                out?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    // 注意: 生成后去掉了边框
    private fun generateQRCode(content: String, size: Int): Bitmap {
        val writer = QRCodeWriter()
        val hints = mapOf(
            EncodeHintType.CHARACTER_SET to "UTF-8",
            EncodeHintType.ERROR_CORRECTION to ErrorCorrectionLevel.Q
        )
        val bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, size, size, hints)

        // Find the enclosing rectangle of the QR code
        val width = bitMatrix.width
        val height = bitMatrix.height
        var left = width
        var top = height
        var right = 0
        var bottom = 0

        for (x in 0 until width) {
            for (y in 0 until height) {
                if (bitMatrix[x, y]) {
                    if (x < left) left = x
                    if (x > right) right = x
                    if (y < top) top = y
                    if (y > bottom) bottom = y
                }
            }
        }

        // Create a new BitMatrix without the white margins
        val newWidth = right - left + 1
        val newHeight = bottom - top + 1
        val trimmedMatrix = BitMatrix(newWidth, newHeight)
        for (x in 0 until newWidth) {
            for (y in 0 until newHeight) {
                if (bitMatrix[x + left, y + top]) {
                    trimmedMatrix.set(x, y)
                }
            }
        }

        // Create the Bitmap from the trimmed BitMatrix
        val bitmap = Bitmap.createBitmap(newWidth, newHeight, Bitmap.Config.ARGB_8888)
        for (x in 0 until newWidth) {
            for (y in 0 until newHeight) {
                bitmap.setPixel(x, y, if (trimmedMatrix[x, y]) Color.BLACK else Color.WHITE)
            }
        }

        return bitmap
    }

    // 计算字体高度
    fun currentFontHeight(paint: Paint): Int {
        val fontMetrics = paint.fontMetrics
        val lineHeight = (fontMetrics.descent - fontMetrics.ascent + fontMetrics.leading).toInt()
        return lineHeight
    }

    fun createTicketBitmap(
        title: String,
        categoryName: String,
        goodName: String,
        price: String,
        ticketNo: String,
        qrcode: String,
        purchaseTime: String,
        stationName: String,
        rules: String?,
        isReprint: Boolean
    ): Bitmap {
        // 注意下纸张是1000x500 这里是700x500 是实际大量打印后凑出来的数,包括字体的数值
        val baseFontSize = 10
        val width = 700
        val height = 500

        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        // bg
        canvas.drawColor(Color.WHITE)

        val paint = Paint().apply {
            isAntiAlias = true
            color = Color.BLACK
        }

        // 左栏
        val column1Start = 0f
        val column1Width = 450f

        var yPoint = height * 0.3f
        // Title
        // 自定义字体
//        val tfContent = Typeface.createFromAsset(context.assets, "fonts" + File.separator + "Zfull-GB.ttf")
        paint.apply {
            textSize = (2.5 * baseFontSize).toFloat()
            typeface = Typeface.DEFAULT
//            typeface = tfContent
        }
        var lineHeight = currentFontHeight(paint)
        if (isReprint) {
            canvas.drawText("(补打) $title", 0f, yPoint, paint)
        } else {
            canvas.drawText(title, 100f, yPoint, paint)
        }

        yPoint += lineHeight

        paint.apply {
            textSize = (2 * baseFontSize).toFloat()
        }

        lineHeight = currentFontHeight(paint)

        // Ticket
        canvas.drawText(categoryName + " " + goodName.replace("\\n", " ").replace("\n", " "), column1Start, yPoint, paint)

        paint.strokeWidth = 1f
        yPoint += lineHeight / 2
        canvas.drawLine(column1Start, yPoint, column1Width, yPoint, paint)

        yPoint += lineHeight
        canvas.drawText("单价：${price} 元", column1Start, yPoint, paint)
        yPoint += lineHeight
        canvas.drawText("编号：$ticketNo", column1Start, yPoint, paint)
        yPoint += lineHeight
        canvas.drawText("购票时间：$purchaseTime", column1Start, yPoint, paint)

        paint.strokeWidth = 1f
        yPoint += lineHeight / 2
        canvas.drawLine(column1Start, yPoint, column1Width, yPoint, paint)

        if (rules != null && rules.length > 0) {
            val lines = rules.replace("\\n", "\n").split("\n")

            lines.forEach { text ->
                if (text.trim().length > 0) {
                    yPoint += lineHeight
                    canvas.drawText(text.trim(), column1Start, yPoint, paint)
                }
            }
        }

        yPoint += lineHeight / 2

        // 右栏
        val column2Start = 500f - lineHeight

        paint.textSize = (2.5 * baseFontSize).toFloat()
        lineHeight = currentFontHeight(paint)
        yPoint = height * 0.3f
        canvas.drawText(stationName, column2Start, yPoint, paint)

        paint.textSize = (2 * baseFontSize).toFloat()
        yPoint += lineHeight
        lineHeight = currentFontHeight(paint)

//        goodName?.let {
//            if (!it.contains("单")) {
//                canvas.drawText("[在自助售票机扫码叫车]", column2Start, yPoint, paint)
//            }
//        }
        yPoint += lineHeight

        // QR code
        val qrSize = 290
        val qrBitmap = generateQRCode(qrcode, qrSize)
        canvas.drawBitmap(qrBitmap, column2Start, yPoint, paint)

        return bitmap
    }

    override fun print(
        ticket: Ticket,
        order: Order?,
        good: Good?,
        isReprint: Boolean,
        qrcodeFormat: Int,
        extraData: Map<String, Any>
    ) {
        if (currentPrinter == null) {
            Log.i(TAGS, "no connected printer")
            return
        }

        if (currentPrinterIndex == 0 && PRINTER_1_STATUS != 24) {
            Log.i(TAGS, "printer 1 status: $PRINTER_1_STATUS")
            return
        } else if (currentPrinterIndex == 1 && PRINTER_2_STATUS != 24) {
            Log.i(TAGS, "printer 2 status: $PRINTER_2_STATUS")
            return
        }

        var goodId = good?.id
        var goodName = good?.name
        var goodPrice = good?.price
        var goodCategory = good?.category
        var orderNo = order?.order_no
        ticket.goods?.let {
            goodId = it.id
            goodName = it.name
            goodPrice = it.price
            goodCategory = it.category
        }
        ticket.order?.let {
            orderNo = it.order_no
        }

        var title = RECEIPT_TITLE

        val categoryName = GOOD_CATEGORY_MAP[goodCategory] ?: ""

        AutoReplyPrint.INSTANCE.CP_Pos_ResetPrinter(currentPrinter)
        AutoReplyPrint.INSTANCE.CP_Pos_SetMultiByteMode(currentPrinter)
        AutoReplyPrint.INSTANCE.CP_Pos_SetMultiByteEncoding(
            currentPrinter,
            AutoReplyPrint.CP_MultiByteEncoding_UTF8
        )

//        var rules = GLOBAL_POLL?.data?.machineStaticConfig?.ticket_rules ?: DEFAULT_RULES_TICKET
        // debug
        val rules = "1. 智驾游艇最多8人乘船。\\n2. 船票当日有效，逾期不予退款。\\n3. 当日未核销的，联系客服电话，后台系统原路退款。\\n4. 过了当日未乘视为不退款。\\n" + "为了您的人身安全，请不要将身体部位伸到外边，行驶\\n" + "期间不要随意走动，不要开启应急出口，不要打斗嬉闹\\n" + "应急服务电话：400-805-3787"
//        val rules = "1. 游客乘车必须遵守乘车秩序，服从工作人员管理。\\n 2. 严格按规定定员乘坐，不得超载，以确保安全。\\n 3. 1.3米以下儿童必须有成人陪护，不得单独乘坐。 \\n 4. 凡患心脏病、高血压及酗酒者谢绝乘坐。\\n 5. 严禁乘客携带易燃、易爆等危险品乘坐车上禁止吸烟。\\n 6. 文明旅游，车上不得打闹，以免发生危险。\\n 7. 自觉维护上、下车秩序。\\n 8. 请自觉保持车内清洁卫生。\\n 9. 本票据仅限当日有效，购票当日持本票据客可在园区观光车站点，按规定乘降。"

        var qrcodeStr = formatTicketQrcode(goodId, orderNo, ticket, qrcodeFormat)
        var bitmap = createTicketBitmap(title!!, categoryName, goodName!!, goodPrice!!,
            ticket.ticket_no, qrcodeStr,
            ticket.created_at, CURRENT_STATION_NAME, rules, isReprint)

        // 旋转
        val matrix = Matrix()
        matrix.postRotate(90f)
        val rotatedBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        bitmap = rotatedBitmap

        // debug
//        saveBitmapToFile(bitmap, "/sdcard/1.png")

        AutoReplyPrint.CP_Pos_PrintRasterImageFromData_Helper.PrintRasterImageFromBitmap(currentPrinter,
            bitmap.getWidth(),
            bitmap.getHeight(),
            bitmap,
            AutoReplyPrint.CP_ImageBinarizationMethod_Thresholding, // 厂家要求的参数
            AutoReplyPrint.CP_ImageCompressionMethod_None);

        AutoReplyPrint.INSTANCE.CP_BlackMark_FullCutBlackMarkPaper(currentPrinter)
    }

    // 与之前一样的格式
    fun print2( ticket: Ticket, order: Order?, good: Good?, isReprint: Boolean, qrcodeFormat: Int, extraData: Map<String, Any> ) {
        var goodId = good?.id
        var goodName = good?.name
        var goodPrice = good?.price
        var goodCategory = good?.category
        var orderNo = order?.order_no
        ticket.goods?.let {
            goodId = it.id
            goodName = it.name
            goodPrice = it.price
            goodCategory = it.category
        }
        ticket.order?.let {
            orderNo = it.order_no
        }
        var title = RECEIPT_TITLE
        if (isReprint) {
            title = "（补打）\n$title"
        }
        var qrcodeStr = formatTicketQrcode(goodId, orderNo, ticket, qrcodeFormat)
        Log.e(TAGS, "qrcode: " + qrcodeStr)

        AutoReplyPrint.INSTANCE.CP_Pos_ResetPrinter(currentPrinter)
        AutoReplyPrint.INSTANCE.CP_Pos_SetMultiByteMode(currentPrinter)
        AutoReplyPrint.INSTANCE.CP_Pos_SetMultiByteEncoding(
            currentPrinter,
            AutoReplyPrint.CP_MultiByteEncoding_UTF8
        )

        val paperWidth = 50 * 8 // 50mm纸
        val marginDots = 16 // 注意单位1mm = 8dots

        AutoReplyPrint.INSTANCE.CP_Pos_SetPrintAreaWidth(currentPrinter, paperWidth - marginDots * 2)
        AutoReplyPrint.INSTANCE.CP_Pos_SetPrintAreaLeftMargin(currentPrinter, marginDots)

        AutoReplyPrint.INSTANCE.CP_Pos_SetAlignment(currentPrinter, CP_Pos_Alignment_HCenter)

        AutoReplyPrint.INSTANCE.CP_Pos_SetTextScale(currentPrinter, 1, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, title)
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_SetTextScale(currentPrinter, 0, 0)
        AutoReplyPrint.INSTANCE.CP_Pos_SetTextBold(currentPrinter, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, CURRENT_STATION_NAME)
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)

        AutoReplyPrint.INSTANCE.CP_Pos_SetTextBold(currentPrinter, 0)

        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_SetBarcodeUnitWidth(currentPrinter, 8)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintQRCode(currentPrinter, 0, AutoReplyPrint.CP_QRCodeECC_H, qrcodeStr)
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)

//        goodName?.let {
//            if (!it.contains("单")) {
//                AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, "[在自助售票机扫码叫车]")
//                AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 2)
//            }
//        }
        val categoryName = GOOD_CATEGORY_MAP[goodCategory] ?: ""
        AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, categoryName)
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_SetTextScale(currentPrinter, 1, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, goodName)
        AutoReplyPrint.INSTANCE.CP_Pos_SetTextScale(currentPrinter, 0, 0)
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 2)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintHorizontalLine(currentPrinter, 60, paperWidth - 60)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, "单价：${goodPrice}元")
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, "编号：${ticket.ticket_no}")
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, "购票时间：${ticket.created_at}")
        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)
        AutoReplyPrint.INSTANCE.CP_Pos_PrintHorizontalLine(currentPrinter, 60, paperWidth - 60)

        AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 1)

        AutoReplyPrint.INSTANCE.CP_Pos_SetAlignment(currentPrinter, CP_Pos_Alignment_Left)
        var ticketRules = GLOBAL_POLL?.data?.machineStaticConfig?.ticket_rules ?: DEFAULT_RULES_TICKET
        if (ticketRules != null && ticketRules.length > 0) {
            AutoReplyPrint.INSTANCE.CP_Pos_PrintText(currentPrinter, ticketRules)
            AutoReplyPrint.INSTANCE.CP_Pos_FeedLine(currentPrinter, 4)
        }

        AutoReplyPrint.INSTANCE.CP_BlackMark_FullCutBlackMarkPaper(currentPrinter)

        val result = AutoReplyPrint.INSTANCE.CP_Pos_QueryPrintResult(currentPrinter, 30000)
        if (!result) {
            Log.e(TAGS, "Print Failed")
        } else {
            Log.e(TAGS, "Print Success")
        }
        return
    }

    override fun printTest() {
        RECEIPT_TITLE = "智驾游艇购票凭条（蠡湖景区）"
//        RECEIPT_TITLE = "雁栖湖观光车购票凭条"
//        CURRENT_STATION_NAME = "一号门站"
        CURRENT_STATION_NAME = "蠡湖一号码头"
//        DEFAULT_RULES_TICKET = "1. 游客乘车必须遵守乘车秩序，服从工作人员管理。\\n 2. 严格按规定定员乘坐，不得超载，以确保安全。\\n 3. 1.3米以下儿童必须有成人陪护，不得单独乘坐。 \\n 4. 凡患心脏病、高血压及酗酒者谢绝乘坐。\\n 5. 严禁乘客携带易燃、易爆等危险品乘坐车上禁止吸烟。\\n 6. 文明旅游，车上不得打闹，以免发生危险。\\n 7. 自觉维护上、下车秩序。\\n 8. 请自觉保持车内清洁卫生。\\n 9. 本票据仅限当日有效，购票当日持本票据客可在园区观光车站点，按规定乘降。"

        val ticket = Ticket(1, "2iS3m0PplhtVjftp9IQCibvIce2", 0, 10, 10, "2022-01-01", TicketGoods(1, 10, "外环-单次票-10", "", "100.0", 10))
        val order = Order(1, "2022010101283", "100", "100", 10, 10, 10, "2022-01-01")
        var good = Good(1, 10, "单程票", "100", 1, 1, null)
        print(ticket, order, good, false, TicketQrcode.FORMAT_V2)
        return
    }

    override fun updatePrinterStatus(application: Application?): List<PrinterStatus> {
        Log.e(TAGS, "printer updatePrinterStatus")

        val scope = CoroutineScope(Dispatchers.Main)
        scope.launch {
            PRINTER_1_STATUS = printer1Status
            PRINTER_1_STATUS_STRING = when (PRINTER_1_STATUS) {
                24 -> "打印机¹正常"
                56 -> "打印机¹缺纸"
                -1 -> "打印机¹未连接"
                else -> "打印机¹异常"
            }
            PRINTER_2_STATUS = printer2Status
            PRINTER_2_STATUS_STRING = when (PRINTER_2_STATUS) {
                24 -> "打印机²正常"
                56 -> "打印机²缺纸"
                -1 -> "打印机²未连接"
                else -> "打印机²异常"
            }

            Log.e(TAGS, "printer updatePrinterStatus: $PRINTER_1_STATUS, $PRINTER_2_STATUS")
            Log.e(TAGS, "printer updatePrinterStatus: $PRINTER_1_STATUS_STRING, $PRINTER_2_STATUS_STRING")

            var needSwitch = false
            if (currentPrinterIndex == 0) {
                if (PRINTER_1_STATUS != 24 && PRINTER_2_STATUS == 24) {
                    needSwitch = true
                }
            } else {
                if (PRINTER_1_STATUS == 24 && PRINTER_2_STATUS != 24) {
                    needSwitch = true
                }
            }
            if (needSwitch) {
                val nextIndex = 1 - currentPrinterIndex
                currentPrinter = printers[nextIndex]
                currentPrinterIndex = nextIndex
            }
        }

        return listOf()
    }

    override fun updateAllPrinterStatus(application: Application?): List<PrinterStatus> {
        return updatePrinterStatus(application)
    }
}