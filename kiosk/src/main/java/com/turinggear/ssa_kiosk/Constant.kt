package com.turinggear.ssa_kiosk

import androidx.compose.ui.graphics.Color
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.R

val OSS_URL = ApplicationContext?.getString(R.string.oss_url)
val URL_WX_QRCODE = "$OSS_URL/images/wx-qrcode.jpeg"
val URL_MAP_1 = "$OSS_URL/images/map-q.png"
val URL_MAP_2 = "$OSS_URL/images/map-r.png"

const val MARGIN = 16

val COLOR_WARNING = Color(0xFFDD8B32)

// 默认值
val DEFAULT_RULES_TICKET = ApplicationContext?.getString(R.string.default_rules_ticket)
val DEFAULT_RULES_PASSENGER = ApplicationContext?.getString(R.string.default_rules_passenger)
var RECEIPT_TITLE = ApplicationContext?.getString(R.string.receipt_title)