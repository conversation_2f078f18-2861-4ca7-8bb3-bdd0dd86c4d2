package com.turinggear.ssa_kiosk

import android.util.Log
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.turinggear.ssa_kiosk.util.CaysnPrinterGroup
import com.turinggear.ssa_kiosk.util.IcodPrinterGroup
import com.turinggear.ssa_kiosk.util.PrinterGroup
import com.turinggear.ssa_kiosk.util.PrinterStatus
import com.turinggear.ssa_shared.ApplicationContext

import org.junit.Test
import org.junit.runner.RunWith

import org.junit.Assert.*

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class CaysnPrinterInstrumentedTest {
    @Test
    fun testPrint() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        val printerGroup = CaysnPrinterGroup(appContext)
        printerGroup.init()
        printerGroup.printTest()
    }
}