plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlinx-serialization'
}

apply plugin: 'com.github.alexfu.androidautoversion'

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.turinggear.ssa_kiosk"
        minSdk 25
        targetSdk 31
        versionName androidAutoVersion.versionName
        versionCode androidAutoVersion.versionCode

        // build/outputs/apk/yqh/release/kiosk-0.4.8-yqh-release.apk
        setProperty("archivesBaseName", "kiosk-$versionName-b$versionCode")

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion compose_version
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    testOptions {
        unitTests {
            includeAndroidResources = true

            // 避免单元测试提示Log.d等函数未mock
            returnDefaultValues = true
        }
    }
    signingConfigs {
        zc339a {
            storeFile file('zc339a.jks')
            storePassword '12345678'
            keyAlias = 'key0'
            keyPassword '12345678'
        }
        debug {
            storeFile file('zc339a.jks')
            storePassword '12345678'
            keyAlias = 'key0'
            keyPassword '12345678'
        }
        rk3288 {
            storeFile file('platform.jks')
            storePassword '123456'
            keyAlias = 'system-lztek'
            keyPassword '123456'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
//            signingConfig signingConfigs.release
        }
    }
    flavorDimensions += "version"
    productFlavors {
        yqh {
            dimension = "version"
            resValue "string", "app_name", "雁栖湖/大屏"
            signingConfig signingConfigs.zc339a
        }
        shiyuan {
            dimension = "version"
            resValue "string", "app_name", "世园/大屏"
            signingConfig signingConfigs.zc339a
        }
        waterpark {
            dimension = "version"
            resValue "string", "app_name", "水上公园/大屏"
            signingConfig signingConfigs.zc339a
        }
        jiuzhouwa {
            dimension = "version"
            resValue "string", "app_name", "九州洼/大屏"
            signingConfig signingConfigs.zc339a
        }
        haituo {
            dimension = "version"
            resValue "string", "app_name", "海坨山谷/大屏"
            signingConfig signingConfigs.zc339a
        }
        cssh {
            dimension = "version"
            resValue "string", "app_name", "尚湖/大屏"
            signingConfig signingConfigs.rk3288
        }
        hdboat {
            dimension = "version"
            resValue "string", "app_name", "横店无人船/大屏"
            signingConfig signingConfigs.zc339a
        }
        main {
            dimension = "version"
            resValue "string", "app_name", "测试/大屏"
            signingConfig signingConfigs.zc339a
        }

        csshboat {
            dimension = "version"
            resValue "string", "app_name", "尚湖无人船/大屏"
    
            signingConfig signingConfigs.rk3288
            }

        shg {
            dimension = "version"
            resValue "string", "app_name", "山海关/大屏"
    
            signingConfig signingConfigs.rk3288
            }

        lihuboat {
            dimension = "version"
            resValue "string", "app_name", "蠡湖智能游船/大屏"
    
            signingConfig signingConfigs.rk3288
            }

        fhxdboat {
            dimension = "version"
            resValue "string", "app_name", "汾湖心岛智能游船/大屏"
    
            signingConfig signingConfigs.zc339a
            }
        // add flavor here
    }

    // 打印签名文件
    applicationVariants.all { variant ->
        if (variant.buildType.name == 'release') {
            variant.assemble.doLast {
                println("Signing Config for ${variant.name}:")
                println("Store File: ${variant.signingConfig.storeFile}")
                println("Key Alias: ${variant.signingConfig.keyAlias}")
            }
        }
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.9.0'
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.material:material:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.1'
    implementation 'androidx.activity:activity-compose:1.5.0'
    implementation 'com.google.android.material:material:1.6.1'
    implementation files('libs/icod_3.1.6.jar')
    implementation files('libs/autoreplyprint.aar')
    implementation 'androidx.test:core-ktx:1.4.0'
    testImplementation 'junit:junit:4.13.2'
    testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.4"
    // testImplementation 'org.robolectric:robolectric:4.6'
    // Core library
    androidTestImplementation("androidx.test:core:1.4.0")
    // AndroidJUnitRunner and JUnit Rules
    androidTestImplementation("androidx.test:runner:1.4.0")
    androidTestImplementation("androidx.test:rules:1.4.0")
    // Assertions
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.ext:truth:1.4.0'
    androidTestImplementation 'com.google.truth:truth:1.1.3'
    // ........
    androidTestImplementation 'androidx.test.ext:junit-ktx:1.1.3'

    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    implementation "androidx.window:window:1.0.0"
    implementation "io.coil-kt:coil-compose:2.0.0"
    implementation "com.airbnb.android:lottie-compose:4.2.2"
    implementation 'androidx.compose.material:material-icons-extended:1.1.1'
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.5.1"
    implementation "androidx.datastore:datastore-preferences:1.0.0"
//    implementation 'com.github.FunnySaltyFish.ComposeDataSaver:data-saver:v1.0.2'
    implementation 'org.hashids:hashids:1.0.3'

    def ktor_version = '2.0.3'
    implementation "io.ktor:ktor-client-okhttp:$ktor_version"
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation "io.ktor:ktor-client-logging-jvm:$ktor_version"

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'
    implementation files('libs/UsbPrinterDriverV232.jar')
    implementation 'io.sentry:sentry-android:5.7.3'
    implementation project(path: ':shared')
    implementation 'com.google.android.exoplayer:exoplayer:2.18.0'
//    implementation 'com.danikula:videocache:2.7.1'
    implementation 'com.google.zxing:core:3.4.1'
}
