plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

apply plugin: 'com.github.alexfu.androidautoversion'

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.turinggear.ssa_console"
        minSdk 25
        targetSdk 32
        versionName androidAutoVersion.versionName
        versionCode androidAutoVersion.versionCode

        setProperty("archivesBaseName", "console-$versionName-b$versionCode")

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    signingConfigs {
//        debug {
//            storeFile file('s906.jks')
//            storePassword '12345678'
//            keyAlias = 'key0'
//            keyPassword '12345678'
//        }
        zc339a {
            storeFile file('zc339a.jks')
            storePassword '12345678'
            keyAlias = 'key0'
            keyPassword '12345678'
        }
        s906 {
            storeFile file('s906.jks')
            storePassword '12345678'
            keyAlias = 'key0'
            keyPassword '12345678'
        }
        rk3288 {
            storeFile file('platform.jks')
            storePassword '123456'
            keyAlias = 'system-lztek'
            keyPassword '123456'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
//    flavorDimensions += "version"
    flavorDimensions "version", "board"
    productFlavors {
        zc339a {
            dimension = "board"
            signingConfig signingConfigs.zc339a
        }

        s906 {
            dimension = "board"
            signingConfig signingConfigs.s906
        }

        rk3288 {
            dimension = "board"
            signingConfig signingConfigs.rk3288
        }

        yqh {
            dimension = "version"
        }
        shiyuan {
            dimension = "version"
        }
        waterpark {
            dimension = "version"
        }
        jiuzhouwa {
            dimension = "version"
        }
        haituo {
            dimension = "version"
        }
        cssh {
            dimension = "version"
        }
        hdboat {
            dimension = "version"
        }
        main {
            dimension = "version"
        }

        csshboat {
            dimension = "version"
        }

        shg {
            dimension = "version"
        }

        lihuboat {
            dimension = "version"
        }

        fhxdboat {
            dimension = "version"
        }
    }

}

dependencies {

    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.1'

    implementation project(path: ':shared')
}
