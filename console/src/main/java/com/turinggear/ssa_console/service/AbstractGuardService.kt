package com.turinggear.ssa_console.service

import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import com.turinggear.IRemoteService
import java.util.concurrent.atomic.AtomicBoolean


abstract class AbstractGuardService : Service() {
    abstract fun getPeerPackageName(): String?
    abstract fun getPeerServiceName(): String?
    abstract fun whenDisconnected()

    private var mIsBound = false // Track binding state

    private var mBinderFromPeer: IRemoteService? = null

    private val conn: ServiceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName) {
            Log.e("XXX", getPeerPackageName() + " disconnect")
            mBinderFromPeer = null
            mIsBound = false
//            bindPeer() //TODO: 是否需要?
            if (serviceIsRunning) {
                bindPeer() // Rebind only if service is supposed to be running
            }
            whenDisconnected()
        }

        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            mBinderFromPeer = IRemoteService.Stub.asInterface(service)
            mIsBound = true
            try {
                Log.d(
                    "XXX",
                    ("收到${getPeerPackageName()}消息：name="
                            + mBinderFromPeer?.getName())
                )
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }
    private fun bindPeer() {
        if (!serviceIsRunning) {
            Log.w("XXX", "Service is stopping, skipping bindPeer.")
            return
        }

        val packageName = getPeerPackageName()
        val serviceName = getPeerServiceName()
        if (packageName == null || serviceName == null)
        {
            Log.e("XXX", "skipped bind")
            return
        }
        val serverIntent = Intent()
        serverIntent.setClassName(packageName, serviceName)
        bindService(serverIntent, conn, Context.BIND_AUTO_CREATE)
    }

    private fun unbindPeer() {
        if (mIsBound) {
            try {
                unbindService(conn)
                mIsBound = false
                mBinderFromPeer = null
                Log.d("XXX", "Unbound from peer service.")
            } catch (e: IllegalArgumentException) {
                // Handle case where service might not be registered
                Log.w("XXX", "Error unbinding, service not registered? ${e.message}")
            }
        }
    }

    protected fun isPeerServiceBound(): Boolean {
        return mIsBound && mBinderFromPeer != null
    }

    override fun onCreate() {
        super.onCreate()

        Log.e("XXX", "${applicationContext.packageName} create")
        bindPeer()
    }

    override fun onDestroy() {
        Log.d("XXX", "${applicationContext.packageName} onDestroy")
        serviceIsRunning = false // Ensure flag is false on destruction
        unbindPeer()
        super.onDestroy()
    }

    override fun onBind(intent: Intent): IBinder? {
                 // Return null if the service is not running or stopping
        return if (serviceIsRunning) {
            Log.e("XXX", "${applicationContext.packageName} bind")
            binder
        } else {
            Log.w("XXX", "${applicationContext.packageName} requested bind, but service is stopped/stopping. Returning null.")
            null
        }
    }

    private val binder = object : IRemoteService.Stub() {
        override fun getName(): String {
            return applicationContext.packageName
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent?.action == ACTION_STOP_SERVICE) {
            Log.d("XXX", "Received stop action.")
            stopServiceInternal()
            return START_NOT_STICKY // Don't restart after stopping
        }
        // 这里bind很关键
        bindPeer()
        return START_STICKY
    }

    private fun stopServiceInternal() {
        Log.d("XXX", "Initiating stopServiceInternal...")
        serviceIsRunning = false
        unbindPeer()
        stopSelf() // Request the service be stopped
        Log.d("XXX", "stopSelf() called.")
    }

    companion object {
        const val ACTION_STOP_SERVICE = "com.turinggear.ssa_console.service.ACTION_STOP_SERVICE"

        @Volatile
        private var serviceIsRunning = true

        fun isRunning(): Boolean {
            return serviceIsRunning
        }
    }
}
