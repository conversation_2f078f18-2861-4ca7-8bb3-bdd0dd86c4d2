package com.turinggear.ssa_console.service

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.IBinder
import android.util.Log
import java.lang.Exception
import java.util.*


const val STOP_SERVICE = "com.turinggear.ssa_console.STOP_GUARD"

@Deprecated("")
class GuardService1 : Service() {

    var retryLoopTask: Thread? = null

    private val stopReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            if (intent.action == STOP_SERVICE) {
                <EMAIL>()
            }
        }
    }

    override fun onBind(intent: Intent): IBinder {
        TODO("Return the communication channel to the service.")
    }

    fun beginRetry()
    {
        if(retryLoopTask == null)
        {
            retryLoopTask = Thread({
                try {
                    var current = 1
                    val total = 20
                    while(!Thread.currentThread().isInterrupted() && current<total)
                    {
                        Thread.sleep(5000)
                        Log.e("XXX", "retry " + current)
                        val intent = getPackageManager().getLaunchIntentForPackage("com.turinggear.ssa_car");
                        startActivity(intent);

                        current += 1
                    }
                } catch (e: InterruptedException)
                {
                    e.printStackTrace()
                } catch (e: Exception)
                {
                    e.printStackTrace()
                }
            })
            retryLoopTask!!.start()
        } else {
            Log.e("XXX", "skip creating task")
        }
    }

    fun beginRetry2()
    {
        Timer().schedule(object : TimerTask() {
            override fun run() {
                reboot()
            }
        }, 30000)
    }

    override fun onCreate() {
        super.onCreate()
        Log.e("XXX", "create")

        val theFilter = IntentFilter();
        theFilter.addAction(STOP_SERVICE);
        registerReceiver(stopReceiver, theFilter);
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.e("XXX", "start")
//        beginRetry()
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onDestroy() {
        Log.e("XXX", "stop")

        unregisterReceiver(stopReceiver)
        retryLoopTask?.interrupt()
        retryLoopTask = null

        super.onDestroy()
    }

    fun reboot()
    {
        try {
            Runtime.getRuntime().exec("reboot");
        }
        catch (e: Exception) {
            e.printStackTrace()
        }
    }
}