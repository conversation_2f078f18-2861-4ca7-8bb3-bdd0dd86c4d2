package com.turinggear.ssa_console.service

import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import com.turinggear.IRemoteService
import com.turinggear.ssa_shared.util.OSUtils
import java.util.*


class GuardService : AbstractGuardService() {
    override fun getPeerPackageName(): String? {
        return OSUtils.findInstalledPackageName(applicationContext)
    }

    override fun getPeerServiceName(): String? {
        val packageName = OSUtils.findInstalledPackageName(applicationContext)
        if (packageName == null) {
            return null
        }
        return "${packageName}.service.GuardService"
    }

    override fun whenDisconnected()
    {
        if (!isRunning()) {
            Log.e("XXX", "ignore disconnected")
            return
        }
        Timer().schedule(object : TimerTask() {
            override fun run() {
                val packageName = OSUtils.findInstalledPackageName(applicationContext)
                if (packageName == null)
                {
                    Log.e("XXX", "skipped start activity")
                    return
                }
                // TODO: 无效
//                if (isPeerServiceBound()) {
//                    Log.e("XXX", "skipped rebound activity")
//                    return
//                }
                Log.e("XXX", "revive activity " + packageName)
                val intent = getPackageManager().getLaunchIntentForPackage(packageName);
                startActivity(intent);
            }
        }, 6000)
    }
}
