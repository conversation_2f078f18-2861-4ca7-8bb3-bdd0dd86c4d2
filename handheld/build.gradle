plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlinx-serialization'
}

apply plugin: 'com.github.alexfu.androidautoversion'

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.turinggear.ssa_hand"
        minSdk 25
        targetSdk 31
        versionName androidAutoVersion.versionName
        versionCode androidAutoVersion.versionCode

        setProperty("archivesBaseName", "handheld-$versionName-b$versionCode")

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion compose_version
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    signingConfigs {
        release {
            storeFile file('keystore')
            storePassword 'password'
            keyAlias = 'key0'
            keyPassword 'password'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    flavorDimensions += "version"
    productFlavors {
        yqh {
            dimension = "version"
            resValue "string", "app_name", "雁栖湖/手持"
        }
        shiyuan {
            dimension = "version"
            resValue "string", "app_name", "世园/手持"
        }
        waterpark {
            dimension = "version"
            resValue "string", "app_name", "水上公园/手持"
        }
        jiuzhouwa {
            dimension = "version"
            resValue "string", "app_name", "九州洼/手持"
        }
        haituo {
            dimension = "version"
            resValue "string", "app_name", "海坨山谷/手持"
        }
        nbh {
            dimension = "version"
            resValue "string", "app_name", "南北湖/手持"
        }
        cssh {
            dimension = "version"
            resValue "string", "app_name", "尚湖/手持"
        }
        hdboat {
            dimension = "version"
            resValue "string", "app_name", "横店无人船/手持"
        }
        main {
            dimension = "version"
            resValue "string", "app_name", "测试/手持"
        }


        csshboat {
            dimension = "version"
            resValue "string", "app_name", "尚湖无人船/手持"
    
            }

        shg {
            dimension = "version"
            resValue "string", "app_name", "山海关/手持"
    
            }

        lihuboat {
            dimension = "version"
            resValue "string", "app_name", "蠡湖智能游船/手持"
    
            }

        fhxdboat {
            dimension = "version"
            resValue "string", "app_name", "汾湖心岛智能游船/手持"
    
            }
    // add flavor here

    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.9.0'
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.material:material:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.1'
    implementation 'androidx.activity:activity-compose:1.5.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    implementation 'androidx.compose.material:material-icons-extended:1.1.1'
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.5.1"
    implementation("androidx.navigation:navigation-compose:2.5.0")
    implementation 'org.hashids:hashids:1.0.3'

    def ktor_version = '2.0.3'
    implementation "io.ktor:ktor-client-okhttp:$ktor_version"
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation "io.ktor:ktor-client-logging-jvm:$ktor_version"

    implementation "io.coil-kt:coil-compose:2.0.0"
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'com.sunmi:printerlibrary:1.0.18'
    implementation "com.sunmi:SunmiEID-SDK:1.3.11"
    implementation files('libs/core-3.3.0.jar')
    implementation 'io.sentry:sentry-android:5.7.3'
    implementation project(path: ':shared')
}
