package com.turinggear.ssa_hand

import com.turinggear.ssa_shared.util.TicketQrcodeParser
import org.junit.Test

import org.junit.Assert.*

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun testx() {
        var parsed = TicketQrcodeParser.parse("123")
        assertNull(parsed)

        parsed = TicketQrcodeParser.parse("123.1.*")
        assertNull(parsed)

        parsed = TicketQrcodeParser.parse("1.2024062714382307556351232.34")
        assertEquals("34", parsed?.getIdentifier())

        parsed = TicketQrcodeParser.parse("1.2024062714382307556351232.2iS3m0PplhtVjftp9IQCibvIce2")
        assertEquals("2iS3m0PplhtVjftp9IQCibvIce2", parsed?.getIdentifier())

        parsed = TicketQrcodeParser.parse("T-2iS3m0PplhtVjftp9IQCibvIce2")
        assertEquals("T-2iS3m0PplhtVjftp9IQCibvIce2", parsed?.getIdentifier())
        assertEquals("T", parsed?.codeType)
        assertEquals("2iS3m0PplhtVjftp9IQCibvIce2", parsed?.codeValue)
    }
}