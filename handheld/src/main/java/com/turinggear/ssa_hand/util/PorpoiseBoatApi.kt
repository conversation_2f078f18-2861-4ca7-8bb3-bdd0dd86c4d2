package com.turinggear.ssa_hand.util

import android.util.Log
import androidx.compose.runtime.MutableState
import com.turinggear.ssa_hand.BuildConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import org.json.JSONArray

const val PB_STATUS_OFFLINE = 0
const val PB_STATUS_AVAILABLE = 1
const val PB_STATUS_HUMAN_DRIVING = 2
const val PB_STATUS_AUTOPILOTING = 3

const val PB_LOCK_STATUS_LOCKED = 0
const val PB_LOCK_STATUS_UNLOCKED = 1
const val PB_LOCK_STATUS_UNLOCK_FAILED = 2

fun getStatusDescription(status: Int): String {
    return when (status) {
        0 -> "离线"
        1 -> "空闲"
        2 -> "手动驾驶中"
        3 -> "自动驾驶中"
        else -> "未知状态"
    }
}

fun getLockStatusDescription(lockStatus: Int): String {
    return when (lockStatus) {
        0 -> "锁: 关 "
        1 -> "锁: 开 "
        2 -> "开锁失败"
        else -> "锁:"
    }
}

fun descBoat(boat: Boat): String {
    val statusDescription = getStatusDescription(boat.status)
    val lockStatusDescription = getLockStatusDescription(boat.lockStatus)
    return "${boat.boatCode} $lockStatusDescription ($statusDescription)"
}


val client = OkHttpClient()
val mediaType = "application/json; charset=utf-8".toMediaType()

const val TOKEN = "d2922225f88d4f0b969017efa7f2dbc7"

var API_BASE = "https://porpoise-boat.com"
//val API_BASE = "http://************:3000" //dbg

@Serializable
data class Boat(
    val boatCode: String,
    val lockStatus: Int,
    val boatId: Long,
    val status: Int
)

@Serializable
data class BoatInfoResponse(
    val msg: String,
    val code: Int,
    val data: Boat
)

@Serializable
data class BoatListResponse(
    val msg: String,
    val code: Int,
    var data: List<Boat>? = null
)

@Serializable
data class SimpleResponse(
    val msg: String,
    val code: Int,
)

suspend fun getBoatList(accessKey: String = TOKEN): BoatListResponse {
    return withContext(Dispatchers.IO) {
        val json = JSONObject()
        json.put("accessKey", accessKey)

        val body = json.toString().toRequestBody(mediaType)

        val request = Request.Builder()
            .url("$API_BASE/hengdian/getBoatList")
            .addHeader("park", BuildConfig.FLAVOR)
            .post(body)
            .build()

        client.newCall(request).execute().use { response ->
            if (!response.isSuccessful) throw IOException("Unexpected code $response")

            val responseBody = response.body!!.string()
            val boatListResponse = Json.decodeFromString<BoatListResponse>(responseBody)
            boatListResponse.data = boatListResponse.data ?: emptyList()
            return@withContext boatListResponse
        }
    }
}

suspend fun getBoatInfo(boatId: Long, accessKey: String = TOKEN): BoatInfoResponse {
    return withContext(Dispatchers.IO) {
        val json = JSONObject()
        json.put("accessKey", accessKey)
        json.put("boatId", boatId)

        val body = json.toString().toRequestBody(mediaType)

        val request = Request.Builder()
            .url("$API_BASE/hengdian/getBoatInfo")
            .addHeader("park", BuildConfig.FLAVOR)
            .post(body)
            .build()

        client.newCall(request).execute().use { response ->
            if (!response.isSuccessful) throw IOException("Unexpected code $response")

            val responseBody = response.body!!.string()
            return@withContext Json.decodeFromString<BoatInfoResponse>(responseBody)
        }
    }
}

// 延迟3s
suspend fun loopGetBoatInfo(isLoopActive: MutableState<Boolean>, boatId: Long, interval: Long=3000): BoatInfoResponse? {
    while (isLoopActive.value) {
//    for (i in 1..loopCnt) {
        val boatInfoResponse = getBoatInfo(boatId)

        // 出错返回
        if (boatInfoResponse.code != 200) {
            return boatInfoResponse
        }

        if (boatInfoResponse.data.lockStatus == 1) {
            // 开锁成功
            return boatInfoResponse
        } else if (boatInfoResponse.data.lockStatus == 0) {
            // 继续
            delay(interval)
            continue
        } else {
            // 锁异常返回
            return boatInfoResponse
        }
    }
    return null
}

suspend fun unlockBoat(boatId: Long, orderId: Long, orderPrice: Float,
                       orderCreateTime: String, tickets: List<String>, accessKey: String= TOKEN): SimpleResponse {
    return withContext(Dispatchers.IO) {
        val json = JSONObject()
        json.put("accessKey", accessKey)
        json.put("boatId", boatId)
        json.put("orderId", orderId)
        json.put("orderPrice", orderPrice)
        json.put("orderCreateTime", orderCreateTime)
        json.put("tickets", JSONArray(tickets))

        val body = json.toString().toRequestBody(mediaType)

        val request = Request.Builder()
            .url("$API_BASE/hengdian/unlockBoat")
            .addHeader("park", BuildConfig.FLAVOR)
            .post(body)
            .build()

        client.newCall(request).execute().use { response ->
            if (!response.isSuccessful) throw IOException("Unexpected code $response")

            val responseBody = response.body!!.string()
            return@withContext Json.decodeFromString<SimpleResponse>(responseBody)
        }
    }
}
