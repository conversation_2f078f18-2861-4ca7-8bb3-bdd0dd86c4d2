package com.turinggear.ssa_hand.util

import android.content.Context
import android.util.Log
import com.turinggear.ssa_hand.BuildConfig
import com.turinggear.ssa_shared.ACCESS_TOKEN
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.util.DisableCert
import com.turinggear.ssa_shared.util.SerialNumber
import io.ktor.client.*
import io.ktor.client.call.body
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.statement.HttpResponse
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.util.InternalAPI
import io.ktor.util.cio.writeChannel
import io.ktor.utils.io.copyAndClose
import kotlinx.serialization.json.Json
import java.io.File

@OptIn(InternalAPI::class)
suspend fun HttpClient.downloadFile(
    file: File,
    url: String,
    callback: suspend (boolean: Boolean) -> Unit,
) {
    val call: HttpResponse = this.request {
        url(url)
        method = HttpMethod.Get
    }.body()
    if (!call.status.isSuccess()) {
        callback(false)
    }
    call.content.copyAndClose(file.writeChannel())
    return callback(true)
}

object KtorClient {
    fun httpClient(withAuth: Boolean = true): HttpClient {
        return HttpClient(OkHttp) {
            expectSuccess = true
            if (withAuth) {
                defaultRequest {
                    url {
                        protocol = URLProtocol.HTTPS
                    }
                    header("MachineNo", SerialNumber.hashedSerial())
                    if (ACCESS_TOKEN.isNotEmpty()) {
                        header("Authorization", "Bearer $ACCESS_TOKEN")
                    }
                }
            }
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                })
            }
            install(Logging) {
                logger = object : Logger {
                    override fun log(message: String) {
                        Log.d("Network Message", "log: $message")
                    }
                }
                level = LogLevel.ALL
            }
            engine {
                config {

                    // 临时禁止证书校验
                    sslSocketFactory(
                        DisableCert.unsafeSslContext.socketFactory,
                        DisableCert.trustAllCertificates[0]
                    )

                    /*
                    // 设置 SSL Pinning
                    val certificatePinner = CertificatePinner.Builder().add(
                        "ssa-api-test.turinggear.com",
                        "sha256/dLCOtJr2Vzw57FZGYcDkHhX+MevALZfOXFiyHJt7QrA="
                    ).build()
                    certificatePinner(certificatePinner)
                    */
                }
            }
            HttpResponseValidator {
                validateResponse { response ->
                    val headers = response.headers
                    val auth = headers["Authorization"]
                    auth?.let { it ->
                        if (it.isNotEmpty()) {
                            val token = it.replace("Bearer ", "")
                            ACCESS_TOKEN = token
                            ApplicationContext?.let { applicationContext ->
                                val sharedPref = applicationContext.getSharedPreferences(
                                    BuildConfig.APPLICATION_ID,
                                    Context.MODE_PRIVATE
                                )
                                val edit = sharedPref.edit()
                                edit.putString("ACCESS_TOKEN", ACCESS_TOKEN)
                                edit.apply()
                            }
                        }
                    }
                }
                handleResponseExceptionWithRequest { exception, _ ->
                    if (exception is ClientRequestException) {
                        val exceptionResponse = exception.response
                        if (exceptionResponse.status.value == 401) {
                            ACCESS_TOKEN = ""
                            ApplicationContext?.let {
                                val sharedPref = it.getSharedPreferences(BuildConfig.APPLICATION_ID, Context.MODE_PRIVATE)
                                sharedPref.edit().remove("ACCESS_TOKEN").apply()
                            }
                        }
                    }
                }
            }
        }
    }
}