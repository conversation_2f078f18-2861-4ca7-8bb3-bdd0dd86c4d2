package com.turinggear.ssa_hand.util

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.widget.Toast
import com.turinggear.ssa_hand.sunmi.SunmiPrinterManager
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.*
import com.turinggear.ssa_shared.util.SerialNumber
import com.turinggear.ssa_shared.util.TicketQrcode
import com.turinggear.ssa_shared.util.TicketQrcodeParser
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.http.content.TextContent
import kotlinx.coroutines.delay
import kotlinx.serialization.decodeFromString
import com.turinggear.ssa_shared.R


object RequestManager {

    @SuppressLint("HardwareIds")
    @Suppress("DEPRECATION")
    suspend fun registerDevice() {
        val random = (1000..9999).random()
        try {
            val x: Register = KtorClient.httpClient(withAuth = false).post(urlString = apiRegisterMachine()) {
                contentType(ContentType.Application.Json)
                setBody(BodyForRegister(
                    serial = SerialNumber.originalSerial(),
                    type = 10,
                    random = "$random"
                ))
            }.body()
            HasTriedToRegisterAfterLaunched = true
            if (x.data != "已注册") {
                RANDOM_REGISTER_CODE = "$random"
                SHOW_ALERT_FOR_REGISTER = true
            }
        } catch (e: ClientRequestException) {
            REGISTER_ERROR_STRING = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            //SHOW_ALERT_FOR_REGISTER = true
        } catch (e: Exception) {
            REGISTER_ERROR_STRING = e.localizedMessage
            //SHOW_ALERT_FOR_REGISTER = true
        }
    }

    suspend fun queryTicketDetail(barcode: String): String? {

        SCANNED_GOOD = null
        SCANNED_TICKET = null

        val parsed = TicketQrcodeParser.parse(barcode)
        if (parsed == null) {
            return "验票失败（二维码无效）"
        }
        Log.e("XXX", "parsed: ${parsed.code} ${parsed.format}")

        var msg: String? = null

        try {
            val x: ResponseTicketList = KtorClient.httpClient()
                .get(urlString = "${apiTicketTicketDetail()}/$barcode").body()
            x.data?.let { it ->
                it.forEach { ticket ->
                    SCANNED_TICKET = ticket
                    ticket.goods?.let { it ->
                        // 转换类型
                        SCANNED_GOOD = Good(
                            id = it.id,
                            category = it.category,
                            name = it.name,
                            price = it.price,
                            status = it.status,
                            sort_order = 0,
                            metadata = it.metadata
                        )
                    }
                }
            }
        } catch (e: ClientRequestException) {
            val s = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            if (msg.isNullOrEmpty()) {
                msg = s
            } else {
                msg += "\n" + s
            }
        } catch (e: Exception) {
            if (msg.isNullOrEmpty()) {
                msg = e.localizedMessage
            } else {
                msg += "\n" + e.localizedMessage
            }
        }
        return msg
    }

    suspend fun verifyTicketWith(barcode: String): String? {

        SCANNED_GOOD = null
        SCANNED_TICKET = null

        val parsed = TicketQrcodeParser.parse(barcode)
        if (parsed == null) {
            return "验票失败（二维码无效）"
        }

        val identifier = parsed.getIdentifier()

//        val array = barcode.split(".")
//        val goodId = if (array.isNotEmpty()) array[0] else ""
//        val orderNo = if (array.count() > 1) array[1] else ""
////        val ticketId = if (array.count() > 2) array[2] else ""
//        val ticketNo = if (array.count() > 2) array[2] else ""
        val goodId = parsed.goodId
        val orderNo = parsed.orderNo
        var msg: String? = null

        try {
            val x: ResponseGood = KtorClient.httpClient()
                .get(urlString = "${apiTicketGoods()}/$goodId").body()
            SCANNED_GOOD = x.data
        } catch (e: ClientRequestException) {
            msg = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
//                Log.e("XXX", "err good: " + msg)
                e.localizedMessage
            }
        } catch (e: Exception) {
//            Log.e("XXX", "err good: " + msg)
            msg = e.localizedMessage
        }
        try {
            val x: ResponseTicketList = KtorClient.httpClient()
                .get(urlString = apiTicketTickets(orderNo)).body()
            x.data?.let { it ->
                it.forEach { ticket ->
                    // 根据二维码格式判断
                    if (parsed.format == TicketQrcode.FORMAT_ID && ticket.id.toString() == parsed.ticketId) {
                        SCANNED_TICKET = ticket
                    } else if (parsed.format == TicketQrcode.FORMAT_NO && ticket.ticket_no == parsed.ticketNo){
                        SCANNED_TICKET = ticket
                    }
                }
            }
        } catch (e: ClientRequestException) {
            val s = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            if (msg.isNullOrEmpty()) {
                msg = s
            } else {
                msg += "\n" + s
            }
        } catch (e: Exception) {
            if (msg.isNullOrEmpty()) {
                msg = e.localizedMessage
            } else {
                msg += "\n" + e.localizedMessage
            }
        }
        return msg
    }

    suspend fun consumeTicketWith(barcode: String): String? {
        val parsed = TicketQrcodeParser.parse(barcode)
        if (parsed == null) {
            return "验票失败（二维码无效）"
        }

        val identifier = parsed.getIdentifier()
        try {
            val x: ResponseTicket = KtorClient.httpClient()
                .put(urlString = apiTicketConsume() + "/${identifier}").body()
            IsLoadingConsuming = false
            return if (x.code == 200) {
                SCANNED_TICKET = x.data
                "核销成功"
            } else {
                x.msg
            }
        } catch (e: ClientRequestException) {
            IsLoadingConsuming = false
            return try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                val text = "${responseMsg.code}: ${responseMsg.msg}"
                text
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            IsLoadingConsuming = false
            return e.localizedMessage
        }
    }

//    suspend fun refundOrderWith(barcode: String, amount: String = ""): String? {
//
//        val array = barcode.split(".")
//        val orderNo = if (array.count() > 1) array[1] else ""
//
//        try {
//            val x: ResponseMsg = KtorClient.httpClient()
//                .put(urlString = apiTicketRefund()) {
//                    contentType(ContentType.Application.Json)
//                    setBody(BodyForRefund(
//                        order = orderNo,
//                        refunded = amount
//                    ))
//                }.body()
//            if (x.code == 200) {
//                verifyTicketWith(barcode = barcode)
//            }
//            return x.msg
//        } catch (e: ClientRequestException) {
//            return try {
//                val responseMsg =
//                    JsonFormat.decodeFromString<ResponseMsg>(
//                        e.response.bodyAsText()
//                    )
//                val text = "${responseMsg.code}: ${responseMsg.msg}"
//                text
//            } catch (e: Exception) {
//                e.localizedMessage
//            }
//        } catch (e: Exception) {
//            return e.localizedMessage
//        }
//    }

    suspend fun refundTicketWith(barcode: String): String? {

//        val array = barcode.split( ".")
//        val ticketId = if (array.count() > 2) array[2] else ""

        val parsed = TicketQrcodeParser.parse(barcode)
        if (parsed == null) {
            return "二维码无效"
        }
        val identifier = parsed.getIdentifier()

        try {
            val x: ResponseMsg = KtorClient.httpClient()
                .put(urlString = "${apiTicketRefund()}/$identifier").body()
            if (x.code == 200) {
                verifyTicketWith(barcode = barcode)
            }
            return x.msg
        } catch (e: ClientRequestException) {
            return try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                val text = "${responseMsg.code}: ${responseMsg.msg}"
                text
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            return e.localizedMessage
        }
    }

    suspend fun refundMoneyWith(barcode: String, amount: String): String? {

//        val array = barcode.split( ".")
//        val ticketId = if (array.count() > 2) array[2] else ""

        val parsed = TicketQrcodeParser.parse(barcode)
        if (parsed == null) {
            return "二维码无效"
        }
        val identifier = parsed.getIdentifier()

        try {
            val x: ResponseMsg = KtorClient.httpClient()
                .put(urlString = "${apiTicketRefundMoney()}/$identifier/$amount").body()
            if (x.code == 200) {
                verifyTicketWith(barcode = barcode)
            }
            return x.msg
        } catch (e: ClientRequestException) {
            return try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                val text = "${responseMsg.code}: ${responseMsg.msg}"
                text
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            return e.localizedMessage
        }
    }

    suspend fun wechatPayWith(context: Context, authCode: String, goodsId: Int, amount: Int, pay_platform: Int = 10) {
        // 先拿到支付码，再做所有后续请求。所有 2xx 会在 try 里，其他 code 进入 catch
        try {
            // one. 创建订单
            PAYMENT_STATE = PAYMENTSTATE.MAKING_ORDER
            val one: ResponseOrder = KtorClient.httpClient()
                .post(urlString = apiTicketOrders() + "/${goodsId}") {
                    contentType(ContentType.Application.Json)
                    setBody(BodyForOrderMake(
                        pay_platform = pay_platform,
                        num = amount
                    ))
                }.body()
            val orderNew = one.data
            // two. 支付订单（尝试支付，但不一定成功，比如未输入密码。即使成功，生成车票也需要一定时间）
            PAYMENT_STATE = PAYMENTSTATE.USERPAYING
            val two: ResponseOrder = KtorClient.httpClient()
                .put(urlString = apiTicketOrdersPay()) {
                    contentType(ContentType.Application.Json)
                    setBody(BodyForOrderPay(order = orderNew.order_no, authCode))
                }.body()
            BARCODE = ""
            val orderTriedPaid = two.data
            // three. 轮询结果（3秒钟1次，最多15次为了富余。有结果就结束并打印，一直无结果则给出相应提示）
            for (index in 1..15){
                // 若用户关闭窗口，则不再轮询
                if (PAYMENT_STATE == PAYMENTSTATE.UNSTARTED) {
                    break
                }
                delay(3000)
                val codeFormat = context.resources.getInteger(R.integer.ticket_qrcode_format)
                val three: ResponseTicketList = KtorClient.httpClient()
                    .get(urlString = apiTicketTickets(orderTriedPaid.order_no)).body()
                val tickets = three.data
                if (tickets != null) {
                    PAYMENT_STATE = PAYMENTSTATE.SUCCEED
                    tickets.forEach { ticket ->
                        SELECTED_GOOD?.let {
                            SunmiPrinterManager.print(qrcodeFormat = codeFormat, ticket = ticket, order = orderTriedPaid, good = it)
                        }
                    }
                    delay(3000)
                    SHOW_POPUP = false
                    SCAN_TYPE = SCANTYPE.NONE
                    SELECTED_AMOUNT = 1
                    PAYMENT_STATE = PAYMENTSTATE.UNSTARTED
                    break
                }
                // 超时则提示失败
                if (index == 15) {
                    PAYMENT_STATE = PAYMENTSTATE.FAILED
                }
            }
        } catch (e: ClientRequestException) {
            PAYMENT_STATE = PAYMENTSTATE.FAILED
            val msg = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            Toast.makeText(
                ApplicationContext,
                msg,
                Toast.LENGTH_SHORT
            ).show()
        } catch (e: Exception) {
            PAYMENT_STATE = PAYMENTSTATE.FAILED
            Toast.makeText(
                ApplicationContext,
                e.localizedMessage,
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    // 注意 这个函数里边不要弹Toast,因为不在UI线程执行
    suspend fun requestTicketsWithNationId(nationID: String): String? {
        TicketsForExchange = null

//        val fakeGood = TicketGoods (
//            id = 1,
//            category = 1,
//            name = "Fake Good",
//            description = "",
//            price = "100.0",
//            status = 1,
//        )
//
//        val fakeTickets = listOf(
//            Ticket(ticket_no = "123", created_at = "2022-01-01", status = 10, goods = fakeGood, id=1, use = 0, use_max = 1),
//        )
//
//        TicketsForExchange = fakeTickets

        try {
            val x: ResponseTicketList = KtorClient.httpClient()
                .get(urlString = "${apiTicketTicketsForNationId()}/$nationID").body()
            x.data?.let {
                TicketsForExchange = it
            }
            return "请求成功"
        } catch (e: ClientRequestException) {
            try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                return "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                return e.localizedMessage
            }
        } catch (e: Exception) {
            return e.localizedMessage
        }
    }

    suspend fun requestTicketsWith(orderNo: String) {
        ReprintTickets = null
        try {
            val x: ResponseTicketList = KtorClient.httpClient()
                .get(urlString = apiTicketTickets(orderNo)).body()
            x.data?.let {
                ReprintTickets = it
            }
        } catch (e: ClientRequestException) {
            val msg = try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
            Toast.makeText(
                ApplicationContext,
                msg,
                Toast.LENGTH_SHORT
            ).show()
        } catch (e: Exception) {
            Toast.makeText(
                ApplicationContext,
                e.localizedMessage,
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    suspend fun poll(): Poll? {
        return try {
            KtorClient.httpClient().post(urlString = apiTicketPoll()).body()
        } catch (e: Exception) {
            null
        }
    }

    suspend fun createPBoatOrder(): PBoatOrderResp? {
        return KtorClient.httpClient().post(urlString = apiPorpoiseBoatOrderCreate()) {
                contentType(ContentType.Application.Json)
            }.body()
    }

    suspend fun updatePBoatOrder(order: PBoatOrder): ResponseMsg? {
        return try {
            KtorClient.httpClient().post(urlString = "${apiPorpoiseBoatOrderUpdate()}") {
                contentType(ContentType.Application.Json)
                setBody(order)
            }.body()
        } catch (e: Exception) {
            Log.e("XXX", e.localizedMessage)
            null
        }
    }


    suspend fun requestInternet(): Int {
        return try {
            val x: HttpResponse = KtorClient.httpClient(withAuth = false).head(urlString = "https://status.aliyun.com").body()
            x.status.value
        } catch (e: Exception) {
            0
        }
    }

    suspend fun requestHealthApi(): Int {
        return try {
            val x: HttpResponse = KtorClient.httpClient(withAuth = false).head(urlString = apiHealthCheck()).body()
            x.status.value
        } catch (e: Exception) {
            0
        }
    }

    suspend fun startup(version_code: Int): AppVersion? {
        return try {
            STARTUP = KtorClient.httpClient().post(urlString = apiTicketStartup()) {
                setBody(
                    TextContent(
                    text = "{\"version_code\": $version_code}",
                    contentType = ContentType.Application.Json
                )
                )
            }.body()
            STARTUP?.data?.appVersion
        } catch (e: Exception) {
            Log.e("XXX", e.localizedMessage)
            null
        }
    }

}