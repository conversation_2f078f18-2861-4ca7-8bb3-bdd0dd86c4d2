package com.turinggear.ssa_hand

import androidx.compose.runtime.mutableStateListOf
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.R

const val MARGIN = 20

val DEFAULT_RULES_TICKET = ApplicationContext?.getString(R.string.default_rules_ticket)
val RECEIPT_TITLE = ApplicationContext?.getString(R.string.receipt_title)
val APP_TITLE = ApplicationContext?.getString(R.string.mobile_title)

val PORPOISE_SEATS_CAPICITY = 8
val GROUP_MINIMAL_PASSENGERS = 1

// 验票tab
const val PERM_TICKET_CHECK = "ticket_checking"
const val PERM_REFUND = "refund"
const val PERM_CONSUME = "consume"
// 换票tab
const val PERM_EXCHANGE_FOR_TICKET = "exchange_for_ticket"
// 购票tab
const val PERM_BUY_TICKET = "buy_tickets"
const val PERM_CASH_PAYMENT = "cash_payment"
// 补票tab
const val PERM_REPRINT = "reprint"
