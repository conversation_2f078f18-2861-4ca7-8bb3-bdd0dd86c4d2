package com.turinggear.ssa_hand.viewmodel

import androidx.lifecycle.ViewModel
import com.turinggear.ssa_hand.util.KtorClient
import com.turinggear.ssa_shared.ACCESS_TOKEN
import com.turinggear.ssa_shared.CURRENT_USER
import com.turinggear.ssa_shared.JsonFormat
import com.turinggear.ssa_shared.apiTicketLogin
import com.turinggear.ssa_shared.model.ResponseMsg
import com.turinggear.ssa_shared.model.ResponseToken
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import kotlinx.serialization.decodeFromString

class LoginViewModel : ViewModel() {

    suspend fun loginWith(name: String, password: String): String {
        return try {
            val responseToken: ResponseToken = KtorClient.httpClient().post(urlString = apiTicketLogin()) {
                parameter("phone", name)
                parameter("password", password)
            }.body()
            if (responseToken.data.access_token.isNotEmpty()) {
                ACCESS_TOKEN = responseToken.data.access_token
                CURRENT_USER = responseToken.data.user
                "登录成功"
            } else {
                "登录失败"
            }
        } catch (e: ClientRequestException) {
            try {
                val responseMsg =
                    JsonFormat.decodeFromString<ResponseMsg>(
                        e.response.bodyAsText()
                    )
                "${responseMsg.code}: ${responseMsg.msg}"
            } catch (e: Exception) {
                e.localizedMessage
            }
        } catch (e: Exception) {
            e.localizedMessage ?: "登录失败"
        }
    }
}