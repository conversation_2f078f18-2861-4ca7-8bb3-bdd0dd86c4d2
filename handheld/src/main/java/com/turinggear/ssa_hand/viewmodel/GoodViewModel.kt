package com.turinggear.ssa_hand.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.turinggear.ssa_hand.util.KtorClient
import com.turinggear.ssa_shared.JsonFormat
import com.turinggear.ssa_shared.apiTicketGoods
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.ResponseGoods
import com.turinggear.ssa_shared.model.ResponseMsg
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.decodeFromString

class GoodViewModel : ViewModel() {

    val goodsFlow = MutableStateFlow<List<Good>?>(null)
    val msgFlow = MutableStateFlow<String?>(null)

    fun update() {
        viewModelScope.launch {
            try {
                val x: ResponseGoods = KtorClient.httpClient().get(urlString = "${apiTicketGoods()}?visibility=30").body()
                goodsFlow.value = x.data.data
            } catch (e: ClientRequestException) {
                msgFlow.value = try {
                    val responseMsg =
                        JsonFormat.decodeFromString<ResponseMsg>(
                            e.response.bodyAsText()
                        )
                    "${responseMsg.code}: ${responseMsg.msg}"
                } catch (e: Exception) {
                    e.localizedMessage
                }
            } catch (e: Exception) {
                msgFlow.value = e.localizedMessage
            }
        }
    }
}