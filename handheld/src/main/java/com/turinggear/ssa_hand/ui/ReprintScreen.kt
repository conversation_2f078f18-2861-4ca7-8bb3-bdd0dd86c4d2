package com.turinggear.ssa_hand.ui

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Print
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_hand.MARGIN
import com.turinggear.ssa_hand.sunmi.SunmiPrinterManager
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.ReprintTickets
import com.turinggear.ssa_shared.model.ticketStatusColorFrom
import com.turinggear.ssa_shared.model.ticketStatusStringFrom
import com.turinggear.ssa_shared.R

@Composable
fun ReprintScreen() {
    val context = LocalContext.current
    Column(
        Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxHeight(fraction = 1 / 10f),
            contentAlignment = Alignment.Center
        ) {
            Text(
                "补打小票",
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold)
            )
        }

        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .padding(horizontal = MARGIN.dp)
                .verticalScroll(scrollState)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 20.dp)
                    .padding(horizontal = MARGIN.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "1. 打开微信支付凭证\n2. 点击查看账单详情\n3. 扫描页面上的条码",
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.5f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.subtitle1
                )
            }
            val codeFormat = context.resources.getInteger(R.integer.ticket_qrcode_format)
            ReprintTickets?.let {
                it.forEach { ticket ->
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 15.dp)
                            .border(
                                0.5.dp,
                                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                                RoundedCornerShape(8.dp)
                            )
                            .background(
                                MaterialTheme.colors.onSurface.copy(alpha = 0.04f),
                                RoundedCornerShape(8.dp)
                            )
                            .padding(horizontal = 15.dp, vertical = 12.dp),
                        contentAlignment = Alignment.BottomEnd
                    ) {
                        val text =
                            "编号：${ticket.ticket_no}\n时间：${ticket.created_at}\n票种：${ticket.goods?.description}\n单价：${ticket.goods?.price}元"
                        Column(Modifier.fillMaxWidth()) {
                            Text(text = text, style = MaterialTheme.typography.caption)
                            Text(
                                text = "状态：${ticketStatusStringFrom(ticket)}",
                                color = ticketStatusColorFrom(ticket),
                                style = MaterialTheme.typography.caption
                            )
                        }
                        var enabled = false
                        var tintColor = Color.LightGray
                        if (ticket.status == 10) {
                            enabled = true
                            tintColor = Color(0xFF53A949)
                        }
                        IconButton(
                            onClick = {
                                SunmiPrinterManager.print(
                                    qrcodeFormat = codeFormat,
                                    ticket = ticket, header = "(补打)")
                                Toast.makeText(
                                    ApplicationContext,
                                    "正在出票…",
                                    Toast.LENGTH_SHORT
                                ).show()
                            },
                            modifier = Modifier
                                .background(
                                    tintColor.copy(alpha = 0.2f),
                                    RoundedCornerShape(8.dp)
                                ),
                            enabled = enabled
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Print,
                                contentDescription = null,
                                tint = tintColor.copy(alpha = 0.8f)
                            )
                        }
                    }
                }
            }
        }
    }
}