package com.turinggear.ssa_hand.ui

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Close
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.turinggear.ssa_hand.*
import com.turinggear.ssa_hand.BuildConfig
import com.turinggear.ssa_shared.model.ticketStatusColorFrom
import com.turinggear.ssa_shared.model.ticketStatusStringFrom
import com.turinggear.ssa_hand.util.RequestManager
import com.turinggear.ssa_shared.*
import kotlinx.coroutines.launch

@Composable
fun TicketConsumeView(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MARGIN.dp)
            .padding(bottom = 5.dp)
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colors.onSurface.copy(alpha = 0.04f))
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(fraction = 3 / 4f),
            contentAlignment = Alignment.TopEnd
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .padding(horizontal = 24.dp)
                    .padding(vertical = 5.dp),
                verticalArrangement = Arrangement.spacedBy(
                    10.dp,
                    alignment = Alignment.CenterVertically
                )
            ) {
                var numberString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var timeString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var typeString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var priceString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var statusString = "‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧‧"
                var textColor = MaterialTheme.colors.onSurface.copy(0.2f)
                var statusColor = textColor
                var payPlatformString = ""

                SCANNED_TICKET?.let { it ->
                    textColor = MaterialTheme.colors.onSurface
                    statusColor = ticketStatusColorFrom(it)
                    SCANNED_GOOD?.let { good ->
                        typeString = good.name
//                        priceString = "${good.price}元"
                        priceString = if (good.price == "0") "-" else "${good.price}元"
                    }
                    numberString = it.ticket_no
                    timeString = if (it.created_at.isEmpty()) "-" else it.created_at
                    statusString = ticketStatusStringFrom(it)
                    it.order?.pay_platform.let {
                        payPlatformString = "(" + PAY_PLATFORM_MAP.getOrDefault(it, "-") + ")"
                    }
                }

                Text(
                    text = "票编号：$numberString",
                    color = textColor,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    style = MaterialTheme.typography.body1
                )
                Text(
                    text = "购票时间：$timeString",
                    color = textColor,
                    style = MaterialTheme.typography.body1
                )
                Text(
                    text = "购票种类：$typeString",
                    color = textColor,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    style = MaterialTheme.typography.body1
                )
                Text(
                    text = "购买单价：$priceString",
                    color = textColor,
                    style = MaterialTheme.typography.body1
                )
                Text(
                    text = "当前状态：$statusString",
                    color = statusColor,
                    style = MaterialTheme.typography.body1
                )
            }
            SCANNED_TICKET?.let {
                TextButton(
                    onClick = {
                        SCANNED_GOOD = null
                        SCANNED_TICKET = null
                    },
                    modifier = Modifier
                        .padding(all = 6.dp)
                        .size(32.dp)
                        .aspectRatio(1f),
                    colors = ButtonDefaults.textButtonColors(backgroundColor = Color.Transparent),
                    contentPadding = PaddingValues(all = 0.dp)
                ) {
                    Icon(
                        Icons.Outlined.Close,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colors.onSurface.copy(0.4f)
                    )
                }
            }
        }
        Divider(thickness = 0.5.dp)
        Row (
        ) {
            val scope = rememberCoroutineScope()
            val context = LocalContext.current
            TextButton(
                onClick = {
                    scope.launch {
                        if (BuildConfig.FLAVOR == "waterpark") {
                            SHOW_REFUND = true
                        } else {
                            val msg = RequestManager.refundTicketWith(BARCODE)
                            msg?.let {
                                Toast.makeText(
                                    context,
                                    it,
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                    }
                },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(4.dp),
                enabled = (SCANNED_TICKET?.status == 10)
            ) {
                Text(text = "退款", style = MaterialTheme.typography.h6.copy(fontSize = 18.sp))
            }
            Divider(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(0.5.dp)
            )
            TextButton(
                onClick = {
                    IsLoadingConsuming = true
                    scope.launch {
                        SCANNED_TICKET?.let { ticket ->
                            val ret = canUpdateGroupedTicket(ticket)
                            if (ret == AFTER_CONSUME_EXCEED_CAPACITY) {
                                val currentTotalSeats = consumedTickets.sumBy { ticket -> ticket.goods?.metadata?.seats ?: 0 }
                                val msg = "拼船票人过多 ${currentTotalSeats}+${ticket.goods!!.metadata!!.seats}>$PORPOISE_SEATS_CAPICITY 人"
                                Toast.makeText( context, msg, Toast.LENGTH_SHORT ).show()

                                IsLoadingConsuming = false
                            } else {
                                val msg = RequestManager.consumeTicketWith(BARCODE)
                                msg?.let {
                                    Toast.makeText(
                                        context,
                                        it,
                                        Toast.LENGTH_SHORT
                                    ).show()

                                    if (msg == "核销成功") {
                                        val ticket = SCANNED_TICKET!!.copy()
                                        val msg = updateGroupedTicket(scanned = ticket)
                                        if (msg != "") {
                                            Toast.makeText( context, msg, Toast.LENGTH_SHORT ).show()
                                        }
                                    }
                                }
                            }

                        }
                    }
                },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(4.dp),
                enabled = ((SCANNED_TICKET?.status == 10) and (!IsLoadingConsuming)),
                colors = ButtonDefaults.textButtonColors(contentColor = Color(0xFF53A949))
            ) {
                Text(text = "核销", style = MaterialTheme.typography.h6.copy(fontSize = 18.sp))
            }
        }
    }
}