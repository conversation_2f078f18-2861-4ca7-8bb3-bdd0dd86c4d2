package com.turinggear.ssa_hand.ui

import android.content.Context
import android.content.res.Resources
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.AlertDialog
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.RadioButton
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.turinggear.ssa_hand.BuildConfig
import com.turinggear.ssa_hand.GROUP_MINIMAL_PASSENGERS
import com.turinggear.ssa_hand.MARGIN
import com.turinggear.ssa_hand.PORPOISE_SEATS_CAPICITY
import com.turinggear.ssa_hand.util.Boat
import com.turinggear.ssa_hand.util.PB_LOCK_STATUS_LOCKED
import com.turinggear.ssa_hand.util.PB_LOCK_STATUS_UNLOCKED
import com.turinggear.ssa_hand.util.PB_LOCK_STATUS_UNLOCK_FAILED
import com.turinggear.ssa_hand.util.PB_STATUS_AVAILABLE
import com.turinggear.ssa_hand.util.PB_STATUS_HUMAN_DRIVING
import com.turinggear.ssa_hand.util.RequestManager
import com.turinggear.ssa_hand.util.SimpleResponse
import com.turinggear.ssa_hand.util.loopGetBoatInfo
import com.turinggear.ssa_hand.util.descBoat
import com.turinggear.ssa_hand.util.getBoatList
import com.turinggear.ssa_hand.util.unlockBoat
import com.turinggear.ssa_shared.model.PBoatOrder
import com.turinggear.ssa_shared.model.Ticket
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.turinggear.ssa_shared.R


class CurrentPBoatOrder() {
    companion object {
        const val CREATED: Int = 10
        const val UNLOCKING: Int = 20
        const val UNLOCKED: Int = 30
    }

    var id: Int = 0
    var created_at: String? = null
//    var boat_type: String? = null
    var boat_id: Long? = null
    var boat_code: String? = null
    var tickets: List<String>? = null
//    var money: String? = null

    var status: Int = CREATED

    fun init(id: Int, created_at: String?) {
        this.id = id
        this.created_at = created_at
    }

    fun unlocking(boat_id: Long, boat_code: String, ticketNos: List<String>) {
        this.boat_id = boat_id
        this.boat_code = boat_code
        this.status = UNLOCKING
        this.tickets = ticketNos
    }

    fun unlocked() {
        this.status = UNLOCKED
    }
}

class AppState() {
    var currentPBoatOrder: CurrentPBoatOrder? = null

    fun initCurrentPBoatOrder(orderId: Int, created_at: String?) {
        currentPBoatOrder = CurrentPBoatOrder()
        currentPBoatOrder!!.init(orderId, created_at)
    }

    fun isExistsOrder(): Boolean {
        return currentPBoatOrder != null
    }

    fun clearCurrentPBoatOrder() {
        currentPBoatOrder = null
    }
}

val appState = AppState()

suspend fun fetchBoatList(scope: CoroutineScope, context: Context): List<Boat> {
    return scope.async {
        try {
            val boatResp = getBoatList()
            if (boatResp.data.isNullOrEmpty()) {
                Toast.makeText(context, "错误: 获取船只列表失败", Toast.LENGTH_LONG).show()
                return@async listOf<Boat>()
            } else {
                return@async boatResp.data!!
            }
        } catch (e: Exception) {
            Toast.makeText(context, "错误: 获取船只列表失败 ${e.localizedMessage}", Toast.LENGTH_LONG).show()
            return@async listOf<Boat>()
        }
    }.await()
}

// 点击开锁后用 在循环查询状态时候显示
@Composable
fun LoadingDialog(showDialog: MutableState<Boolean>, isLoopActive: MutableState<Boolean>) {
    if (showDialog.value) {
        Dialog(onDismissRequest = { }) {
            Column(modifier = Modifier.size(100.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
                ) {
                CircularProgressIndicator() // Loading indicator
                TextButton( modifier = Modifier.background(Color.Gray.copy(alpha = 0.8f)),
                    onClick = {
                    showDialog.value = false
                    isLoopActive.value = false
                }) {
                    Text("取消等待游船屏幕解锁")
                }
            }
        }
    }
}

fun isLastTried(selectedBoat: Boat?): Boolean {
    return appState.currentPBoatOrder?.boat_id == selectedBoat?.boatId
}

fun isHumanDriving(selectedBoat: Boat?): Boolean {
    return (selectedBoat?.lockStatus == PB_LOCK_STATUS_UNLOCKED && selectedBoat.status == PB_STATUS_HUMAN_DRIVING)
}

fun canUnlock(selectedBoat: Boat?): Boolean {
    if (selectedBoat == null) {
        return false
    }
    val locked = selectedBoat?.lockStatus != null
            && selectedBoat?.lockStatus in intArrayOf( PB_LOCK_STATUS_LOCKED, PB_LOCK_STATUS_UNLOCK_FAILED)
            && selectedBoat.status == PB_STATUS_AVAILABLE

    if (isLastTried(selectedBoat)) {
        // 延迟开锁成功这种情况下也可用
        // 但只针对自己上次开锁的船
        return locked || isHumanDriving(selectedBoat)
    } else {
        return locked
    }
}

val TEXT_NORMAL = "点击手动刷新"
val TEXT_PROCESSING = "正在刷新列表..."
val TEXT_ERROR = "加载异常,点击重试"

@Composable
fun UnlockBoatDialog(showDialog: MutableState<Boolean>) {
    val showClearAlert = remember { mutableStateOf(false) }
    val clearAlertTitle = remember { mutableStateOf("!!!确认!!!") }

    // 所有查询到的船 包括任何状态的
    var boats by remember { mutableStateOf(listOf<Boat>()) }

    var progressReloadBoatList by remember { mutableStateOf(TEXT_NORMAL) }

    // radio button 选中的船的编号
    var selectedBoatCode by remember { mutableStateOf("") }

    // 转圈
    val showLoadingDialog = remember { mutableStateOf(false) }

    val isLoopActive = remember { mutableStateOf(true) }

    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    if (showDialog.value) {
        // 弹出窗口后, 刷新船只列表
        LaunchedEffect(key1 = Unit) {
            // 重设
            progressReloadBoatList = TEXT_PROCESSING

            boats = listOf()
            selectedBoatCode = ""

            scope.launch {
                boats = fetchBoatList(scope, context)
                if (boats.isNotEmpty()) {
                    selectedBoatCode = boats[0].boatCode

                    progressReloadBoatList = TEXT_NORMAL
                } else {
                    progressReloadBoatList = TEXT_ERROR
                }
            }
        }

        Dialog(onDismissRequest = {  }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .padding(16.dp)
                    .background(Color.White),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Spacer(modifier = Modifier.weight(1f))
                    Text(text = "解锁船只", fontSize = 20.sp, fontWeight = FontWeight.Bold, textAlign = TextAlign.Center)
                    Spacer(modifier = Modifier.weight(1f))
                    Text(text = "${appState.currentPBoatOrder?.id ?: "-"}", textAlign = TextAlign.End)
                }

                Spacer(modifier = Modifier.height(16.dp))

                TextButton(onClick = {
                    // 重设
                    progressReloadBoatList = TEXT_PROCESSING

                    boats = listOf()
                    selectedBoatCode = ""

                    scope.launch {
                        boats = fetchBoatList(scope, context)
                        if (boats.isNotEmpty()) {
                            selectedBoatCode = boats[0].boatCode

                            progressReloadBoatList = TEXT_NORMAL
                        } else {
                            progressReloadBoatList = TEXT_ERROR
                        }
                    }
                }) {
                    Text(progressReloadBoatList)
                }

                boats.forEach { boat ->
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = (boat.boatCode == selectedBoatCode),
                                onClick = { selectedBoatCode = boat.boatCode }
                            ),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        RadioButton(
                            selected = (boat.boatCode == selectedBoatCode),
                            onClick = { selectedBoatCode = boat.boatCode }
                        )
                        val text = buildAnnotatedString {
                            append(descBoat(boat))
                            if (isLastTried(boat)) {
                                withStyle(style = SpanStyle(color = Color.Blue, fontWeight = FontWeight.Bold)) {
                                    append("\n" + "上次选择的船")
                                }
                            }
                        }

                        Text(
                            text = text,
                            style = MaterialTheme.typography.body1.merge(),
                            modifier = Modifier.padding(start = 16.dp)
                        )
                    }
                }

                val selectedBoat = boats.find { it.boatCode == selectedBoatCode }
                TextButton(
                    onClick = {
                        showDialog.value = true
                        scope.launch {
                            if (selectedBoat != null) {
                                showLoadingDialog.value = true
                                isLoopActive.value = true

                                val ticketNos = consumedTickets.map { it.ticket_no }
                                val totalPrice = consumedTickets.sumByDouble { it.goods?.price?.toDouble() ?: 0.0 }

                                // 1. 请求解锁
                                var requestUnlockSuccess: Boolean
                                if (isLastTried(selectedBoat) && isHumanDriving(selectedBoat))  {
                                    // 如果延迟解锁清空 则认为已经请求过
                                    requestUnlockSuccess = true
                                } else {
                                    // 请求解锁
                                    try {
                                        var resultUnlock: SimpleResponse? = null
                                        resultUnlock = unlockBoat(selectedBoat.boatId, appState.currentPBoatOrder!!.id.toLong(),
                                            totalPrice.toFloat(), appState.currentPBoatOrder!!.created_at!!, ticketNos)

                                        withContext(Dispatchers.Main) {
                                            if (resultUnlock == null) {
                                                requestUnlockSuccess = false
                                                Toast.makeText(
                                                    context,
                                                    "开锁请求失败 1",
                                                    Toast.LENGTH_LONG
                                                ).show()
                                            } else if (resultUnlock!!.code == 200) {
                                                requestUnlockSuccess = true
                                                Toast.makeText(
                                                    context,
                                                    "已请求开锁,正在查询锁状态",
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            } else {
                                                requestUnlockSuccess = false
                                                Toast.makeText(
                                                    context,
                                                    "开锁请求失败${resultUnlock!!.code} ${resultUnlock!!.msg}",
                                                    Toast.LENGTH_LONG
                                                ).show()
                                            }
                                        }

                                    } catch (e: Exception) {
                                        requestUnlockSuccess = false
                                        withContext(Dispatchers.Main) {
                                            Toast.makeText(
                                                context,
                                                "请求失败 ${e.localizedMessage}",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                    }
                                }

                                // 仅当请求开锁成功
                                if (requestUnlockSuccess && appState.isExistsOrder()) {
                                    // 2. 上报给SSA后端

                                    val order = PBoatOrder(
                                        id = appState.currentPBoatOrder!!.id,
                                        boat_code = selectedBoat.boatCode,
                                        boat_type = "无人游船",
                                        boat_id = selectedBoat.boatId,
                                        money = totalPrice.toString(),
                                        tickets = ticketNos
                                    )
                                    RequestManager.updatePBoatOrder(order);

                                    // 3. 更新本地状态
                                    appState.currentPBoatOrder?.unlocking(selectedBoat.boatId, selectedBoat.boatCode, ticketNos)

                                    // 4. 轮询锁状态
                                    // 仅当请求开锁成功
                                    // 注意对于延迟开锁的 仍进行一次查询 为了严格
                                    try {
                                        val resultBoat = loopGetBoatInfo(isLoopActive, selectedBoat.boatId)

                                        withContext(Dispatchers.Main) {
                                            if (resultBoat == null) {
                                                Toast.makeText(context, "开锁失败432,请刷新列表", Toast.LENGTH_SHORT).show()
                                            } else if (resultBoat.code == 200 && resultBoat.data.lockStatus == 1) {
                                                Toast.makeText(context, "成功开锁", Toast.LENGTH_SHORT).show()

                                                // 弹出确认窗口
                                                showClearAlert.value = true
                                                clearAlertTitle.value = "开锁成功!!!"

                                                appState.currentPBoatOrder?.unlocked()
                                            } else {
                                                Toast.makeText(context, "开锁失败${resultBoat.code} ${resultBoat.msg}", Toast.LENGTH_SHORT).show()
                                            }
                                        }
                                    }
                                    catch (e: Exception) {
                                        withContext(Dispatchers.Main) {
                                            Toast.makeText(context, "查询锁状态失败 ${e.localizedMessage}", Toast.LENGTH_LONG).show()
                                        }
                                    }

                                }

                                showLoadingDialog.value = false

                                // 重设
                                progressReloadBoatList = TEXT_PROCESSING

                                boats = listOf()
                                selectedBoatCode = ""

                                scope.launch {
                                    boats = fetchBoatList(scope, context)
                                    if (boats.isNotEmpty()) {
                                        selectedBoatCode = boats[0].boatCode

                                        progressReloadBoatList = TEXT_NORMAL
                                    } else {
                                        progressReloadBoatList = TEXT_ERROR
                                    }
                                }
                            }
                        }
                    },
                    enabled = canUnlock(selectedBoat),
                    colors = ButtonDefaults.buttonColors(backgroundColor = Color(0xFF00E676)), // Change button color
                    shape = RoundedCornerShape(10.dp), // Change this value to adjust the roundness of the corners
                    modifier = Modifier
                        .height(80.dp)
                        .padding(8.dp) // Change button padding
                ) {
                    Text(text = if (isLastTried(selectedBoat) && isHumanDriving(selectedBoat)) {
                        "确认$selectedBoatCode"
                    } else {
                        "解锁$selectedBoatCode"
                    }, fontSize = 24.sp)
                }

                val text = buildAnnotatedString {
                    append("点击解锁后会一直等待\n")
                    withStyle(style = SpanStyle(color = Color.Red)) {
                        append("船上屏幕滑动解锁")
                    }
                    append("完成")
                }
                Text(text)

//                Spacer(modifier = Modifier.height(32.dp))

                TextButton(
                    onClick = {
//                        showClearAlert.value = true
//                        clearAlertTitle.value = "!!!确认!!!"

                        // 关闭自己
                        showDialog.value = false
                    },
                    colors = ButtonDefaults.buttonColors(backgroundColor = Color.LightGray),
                    shape = MaterialTheme.shapes.medium,
                    modifier = Modifier.padding(8.dp)
                ) {
                    Text(text = "关闭窗口")
                }
            }

            // Show the loading dialog when showLoadingDialog is true
            if (showLoadingDialog.value) {
                LoadingDialog(showLoadingDialog, isLoopActive)
            }
        }

        if (showClearAlert.value) {
            val text = buildAnnotatedString {
                append("将")
                withStyle(style = SpanStyle(color = Color.Red)) {
                    append("清空本组票并且关闭窗口\n")
                }
                append("之后这些票")
                withStyle(style = SpanStyle(color = Color.Red)) {
                    append("无法再重新检票")
                }
            }
            AlertDialog(
                onDismissRequest = {
//                    showClearAlert.value = false
                },
                title = {
                    Text(text = clearAlertTitle.value, fontSize = 24.sp)
                },
                text = {
                    Text(text, fontSize = 20.sp)
                },
                confirmButton = {
                    TextButton(
                        onClick = {
                            // 清空
                            consumedTickets.clear()
                            appState.clearCurrentPBoatOrder()

                            // 关闭自己
                            showDialog.value = false

                            showClearAlert.value = false
                        }
                    ) {
                        Text("确认")
                    }
                },
//                dismissButton = {
//                    TextButton(
//                        onClick = {
//                            showClearAlert.value = false
//                        }
//                    ) {
//                        Text("取消")
//                    }
//                }
            )
        }

    }
}

// 扫过的票, 会实时更新状态
val consumedTickets = mutableStateListOf<Ticket>()

const val AFTER_CONSUME_NO_ACTION = 10
const val AFTER_CONSUME_ADD = 20
const val AFTER_CONSUME_EXCEED_CAPACITY = 30

fun canUpdateGroupedTicket(scanned: Ticket): Int {
    if (scanned.goods?.metadata?.boat_type == "porpoise") {
        val currentTotalSeats = consumedTickets.sumBy { ticket -> ticket.goods?.metadata?.seats ?: 0 }
//        Log.e("XXX", "current ${currentTotalSeats}")

        if (currentTotalSeats + scanned.goods!!.metadata!!.seats!! <= PORPOISE_SEATS_CAPICITY) {

            return AFTER_CONSUME_ADD
//            // 只加上正常和核销的，不添加已经退款和过期的
//            if (!ticketsContent.any { existingTicket -> existingTicket.ticket_no == scanned.ticket_no }) {
//                ticketsContent.add(scanned)
//            }

            // 移除再添加为了加到list末尾 这样UI上有个动态
//                            myViewModel.ticketsContent.removeIf { ticket -> ticket.ticket_no == it.ticket_no }
//                            myViewModel.ticketsContent.add(it)
//            Log.e("XXX", "add")
        } else if (currentTotalSeats + scanned.goods!!.metadata!!.seats!! > PORPOISE_SEATS_CAPICITY){
//            val s = "拼船票人过多 ${currentTotalSeats}+${scanned.goods!!.metadata!!.seats}>$PORPOISE_SEATS_CAPICITY 人"
//            Log.e("XXX", s)
//            Toast.makeText( LocalContext.current,
//                s,
//                Toast.LENGTH_SHORT
//            ).show()
//            return s

            return AFTER_CONSUME_EXCEED_CAPACITY
        }
    }
    return AFTER_CONSUME_NO_ACTION
}

fun updateGroupedTicket(scanned: Ticket): String {
    val ret = canUpdateGroupedTicket(scanned)

    val currentTotalSeats = consumedTickets.sumBy { ticket -> ticket.goods?.metadata?.seats ?: 0 }
    if (ret == AFTER_CONSUME_ADD) {
        // 只加上正常和核销的，不添加已经退款和过期的
        if (!consumedTickets.any { existingTicket -> existingTicket.ticket_no == scanned.ticket_no }) {
            consumedTickets.add(scanned)
        }

        // 移除再添加为了加到list末尾 这样UI上有个动态
//                            myViewModel.ticketsContent.removeIf { ticket -> ticket.ticket_no == it.ticket_no }
//                            myViewModel.ticketsContent.add(it)
//        Log.e("XXX", "add")
        return ""
    } else if (ret == AFTER_CONSUME_EXCEED_CAPACITY) {
        val s = "拼船票人过多 ${currentTotalSeats}+${scanned.goods!!.metadata!!.seats}>$PORPOISE_SEATS_CAPICITY 人"
        return s
    } else {
        return ""
    }
}

@Composable
fun GroupTicketView(modifier: Modifier = Modifier) {
    val showDialog = remember { mutableStateOf(false) }

    val showAlert = remember { mutableStateOf(false) }

    var IsLoadingUnlockUI = false

//    if (SCANNED_TICKET != null && SCANNED_TICKET !in myViewModel.ticketsContent) {
//        Log.e("XXX", SCANNED_TICKET.toString())
//        Log.e("XXX", "sacnned tickets begin")
//        for (ticket : Ticket in myViewModel.ticketsContent) {
//            Log.e("XXX", ticket.toString())
//        }
//        Log.e("XXX", "sacnned tickets end")
//
//        SCANNED_TICKET?.let { scanned ->
//            val currentTotalSeats = myViewModel.ticketsContent.sumBy { ticket -> ticket.goods?.metadata?.seats ?: 0 }
//            Log.e("XXX", "current ${currentTotalSeats}")
//
//            // 仅对无人船的票处理
////                    if (it.goods?.metadata?.get("boat_type")?.jsonPrimitive?.content == "porpoise") {
//            if (scanned.goods?.metadata?.boat_type == "porpoise") {
//                if (scanned.status in arrayOf(30, 40)) {
//                    // 已经退款和过期的需要删除
//                    myViewModel.ticketsContent.removeIf { ticket -> ticket.ticket_no == scanned.ticket_no }
////                            CurrentBatchTotalSeats -= it.goods!!.metadata!!.seats
//                } else if (scanned.status in arrayOf(20)) {
//                    // 已经核销的不允许使用
//                    Toast.makeText( LocalContext.current, "票已经核销无法使用", Toast.LENGTH_SHORT ).show()
//                } else if (currentTotalSeats + scanned.goods!!.metadata!!.seats!! <= PORPOISE_SEATS_CAPICITY) {
//                    // 只加上正常和核销的，不添加已经退款和过期的
//                    if (!myViewModel.ticketsContent.any { existingTicket -> existingTicket.ticket_no == scanned.ticket_no }) {
//                        myViewModel.ticketsContent.add(scanned)
//                    }
//
//                    // 移除再添加为了加到list末尾 这样UI上有个动态
////                            myViewModel.ticketsContent.removeIf { ticket -> ticket.ticket_no == it.ticket_no }
////                            myViewModel.ticketsContent.add(it)
//                    Log.e("XXX", "add")
//                } else if (currentTotalSeats + scanned.goods!!.metadata!!.seats!! > PORPOISE_SEATS_CAPICITY){
//                    val s = "拼船票人过多 ${currentTotalSeats}+${scanned.goods!!.metadata!!.seats}>$PORPOISE_SEATS_CAPICITY 人"
//                    Toast.makeText( LocalContext.current,
//                        s,
//                        Toast.LENGTH_SHORT
//                    ).show()
//                }
//            }
//        }
//    }


    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MARGIN.dp)
            .padding(bottom = 5.dp)
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFF795548).copy(alpha = 0.1f))
    ) {
        // 总人数 非总票数
        val totalSeats = consumedTickets.sumBy { ticket -> ticket.goods?.metadata?.seats ?: 0 }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(fraction = 3 / 4f),
            contentAlignment = Alignment.TopEnd
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .padding(horizontal = 1.dp)
                    .padding(vertical = 0.dp),
                verticalArrangement = Arrangement.spacedBy(
                    0.dp,
                    alignment = Alignment.CenterVertically
                )
            ) {
//                    Text(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .fillMaxHeight(),
//                        textAlign = TextAlign.Start,
//                        text = ticketsContent.map { ticket -> ticket.ticket_no }.joinToString("\n")
//                    )
                // 这里的状态只有这两种的
                items(items = consumedTickets,
                    key = { ticket -> ticket.ticket_no },
                    itemContent = { ticket ->
                        val color = when (ticket.status) {
                            10 -> Color(0xFF53A949)
                            20 -> Color.Red
                            else-> Color.Black
                        }
                        Text(text = "${ticket.ticket_no}(${ticket.goods?.metadata?.seats})", color = color)
                    }
                )
//                com.turinggear.ssa_hand.ticketsContent.forEach { ticket ->
//                    val color = when (ticket.status) {
//                        10 -> Color(0xFF53A949)
//                        20 -> Color.Red
//                        else-> Color.Black
//                    }
//                    Text(text = "${ticket.ticket_no}(${ticket.goods?.metadata?.seats})", color = color)
//                }
//                Text(text = "1231231241234123121")
//                Text(text = "1231231241234123122")
//                Text(text = "1231231241234123123")
//                Text(text = "1231231241234123124")
//                Text(text = "1231231241234123125")
//                Text(text = "1231231241234123126")
//                Text(text = "1231231241234123127")
//                Text(text = "1231231241234123128")
            }
            Text(
                text = totalSeats.toString(),
//                text = "",
                fontSize = 100.sp,
                color = Color.White.copy(alpha = 0.8f)
            )
        }
        Divider(thickness = 0.5.dp)
        Row (
        ) {
            val scope = rememberCoroutineScope()
            val context = LocalContext.current
            TextButton(
                onClick = {
                    showAlert.value = true
                },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(4.dp),
                enabled = consumedTickets.size > 0
            ) {
                Text(text = "清空本组拼船", style = MaterialTheme.typography.h6.copy(fontSize = 18.sp))
            }
            Divider(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(0.5.dp)
            )
            TextButton(
                onClick = {
                    scope.launch {
                        if (appState.isExistsOrder()) {
                            // 直接显示
                            showDialog.value = true
                        } else {
                            if (consumedTickets.isNotEmpty()) {
                                IsLoadingUnlockUI = true
                                try {
                                    val newOrder = RequestManager.createPBoatOrder()
                                    newOrder?.let {
//                                Log.e("XXX", grp.toString())
                                        appState.initCurrentPBoatOrder(newOrder.data.id, newOrder.data.created_at)
                                    }
                                    showDialog.value = true
                                } catch (e: Exception) {
                                    Toast.makeText(context, "请重试! 错误信息 ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
                                }
                                IsLoadingUnlockUI = false
                            } else {
                                // 不存在此情况
                            }
                        }
                    }
                },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(4.dp),
                enabled = ((totalSeats >= GROUP_MINIMAL_PASSENGERS) && (!IsLoadingUnlockUI)),
                colors = ButtonDefaults.textButtonColors(contentColor = Color(0xFF53A949))
            ) {
                Text(text = "上船", style = MaterialTheme.typography.h6.copy(fontSize = 18.sp))
            }
        }
    }
    UnlockBoatDialog(showDialog)

    if (showAlert.value) {
        val text = buildAnnotatedString {
            append("注意: 清空后本批票")
            withStyle(style = SpanStyle(color = Color.Red)) {
                append("无法再重新检票")
            }
            append(",是否确认?")
        }

        AlertDialog(
            onDismissRequest = {
//                showAlert.value = false
            },
            title = {
                Text(text = "!!!清空确认!!!", fontSize = 24.sp)
            },
            text = {
                Text(text, fontSize = 20.sp)
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        consumedTickets.clear()
                        appState.clearCurrentPBoatOrder()

                        showAlert.value = false
                    }
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showAlert.value = false
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }
}



@Composable
fun CheckScreen() {
    LaunchedEffect(Unit) {
    }
    Column(
        Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Box(
            contentAlignment = Alignment.Center
        ) {
            Text(
                "按次侧面按键扫码验票",
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold, fontSize = 12.sp)
            )
        }
        TicketConsumeView(modifier = Modifier.fillMaxHeight(fraction = 5 / 9f))
        val context = LocalContext.current
        val showGroupedTicket = context.resources.getBoolean(R.bool.show_grouped_ticket)
        if (showGroupedTicket) {
            GroupTicketView(modifier = Modifier.fillMaxHeight())
        }
    }
}