package com.turinggear.ssa_hand.ui

import android.os.Build
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_hand.ui.theme.HandssaTheme
import com.turinggear.ssa_hand.util.RequestManager
import com.turinggear.ssa_shared.BARCODE
import com.turinggear.ssa_shared.SCANNED_TICKET
import com.turinggear.ssa_shared.SHOW_REFUND
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter


@RequiresApi(Build.VERSION_CODES.O)
@Composable
fun RefundView() {

    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    var amount by rememberSaveable { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = { },
        text = {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.BottomCenter
            ) {
                Text(text = "\n\n\n\n")
                Column {
                    SCANNED_TICKET?.let { it ->
                        val createdAtString = it.created_at
                        if (createdAtString.isNotEmpty()) {
                            val pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                            val createdAt = LocalDateTime.parse(createdAtString, pattern)
                            val interval = System.currentTimeMillis()/1000 - createdAt.atZone(ZoneId.systemDefault()).toEpochSecond()
                            val hours: Long = interval / 3600
                            val minutes: Long = interval % 3600 / 60
                            var intervalString = ""
                            if (hours>0) {
                                intervalString += "${hours}小时"
                            }
                            if (minutes>0) {
                                intervalString += "${minutes}分钟"
                            }
                            Text(text = "购票时间：$createdAtString",
                                modifier = Modifier.padding(bottom = 5.dp),
                                style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold))
                            Text(text = "游玩时长：$intervalString",
                                modifier = Modifier.padding(bottom = 10.dp),
                                color = Color.Red,
                                style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold))
                        }
                    }

                    OutlinedTextField(
                        value = amount,
                        onValueChange = { amount = it },
                        label = { Text("退款金额") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    scope.launch {
                        val msg = RequestManager.refundMoneyWith(BARCODE, amount)
                        msg?.let {
                            Toast.makeText(
                                context,
                                it,
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                        SHOW_REFUND = false
                    }
                },
                modifier = Modifier
                    .padding(vertical = 15.dp)
                    .padding(horizontal = 10.dp)
            ) {
                Text("退款", style = MaterialTheme.typography.h6)
            }
        },
        dismissButton = {
            OutlinedButton(
                onClick = {
                    SHOW_REFUND = false
                },
                modifier = Modifier.padding(vertical = 15.dp)
            ) {
                Text("返回", style = MaterialTheme.typography.h6)
            }
        }
    )
}

@RequiresApi(Build.VERSION_CODES.O)
@Preview(showBackground = true)
@Composable
fun RefundPreview() {
    HandssaTheme {
        RefundView()
    }
}