package com.turinggear.ssa_hand.ui

import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_hand.APP_TITLE
import com.turinggear.ssa_hand.BuildConfig
import com.turinggear.ssa_hand.ui.theme.HandssaTheme
import com.turinggear.ssa_hand.viewmodel.LoginViewModel
import com.turinggear.ssa_shared.ACCESS_TOKEN
import com.turinggear.ssa_shared.GLOBAL_POLL
import kotlinx.coroutines.launch

@Composable
fun LoginView(vm: LoginViewModel = viewModel()) {

    var phone by rememberSaveable { mutableStateOf("") }
    val focusManager = LocalFocusManager.current
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        val mobileTitle = GLOBAL_POLL?.data?.machineStaticConfig?.mobile_title ?: APP_TITLE ?: ""
                        Text(
                            mobileTitle,
                            style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold)
                        )
                        Text(
                            "Ver ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                            color = MaterialTheme.colors.onPrimary.copy(0.7f),
                            style = MaterialTheme.typography.overline.copy(fontWeight = FontWeight.Medium)
                        )
                    }
                }
            )
        },
        content = {
            Box(
                Modifier
                    .fillMaxSize()
                    .clickable { focusManager.clearFocus() },
                contentAlignment = Alignment.TopCenter
            ) {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(fraction = 0.37f),
                    verticalArrangement = Arrangement.spacedBy(
                        32.dp,
                        alignment = Alignment.CenterVertically
                    ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    OutlinedTextField(
                        value = phone,
                        onValueChange = { phone = it },
                        modifier = Modifier.padding(horizontal = 40.dp),
                        label = { Text("手机号") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                    )
                    OutlinedButton(
                        onClick =
                        {
                            focusManager.clearFocus()
                            scope.launch {
                                val result = vm.loginWith(phone, "shiyuan220405")
                                Toast.makeText(
                                    context,
                                    result,
                                    Toast.LENGTH_SHORT
                                ).show()
                                if (result == "登录成功") {
                                    val sharedPref = context.getSharedPreferences(
                                        BuildConfig.APPLICATION_ID,
                                        Context.MODE_PRIVATE
                                    )
                                    val edit = sharedPref.edit()
                                    edit.putString("ACCESS_TOKEN", ACCESS_TOKEN)
                                    edit.apply()
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 39.dp)
                            .height(57.dp),
                        enabled = phone.isNotEmpty()
                    ) {
                        Text(
                            text = "登录",
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.h6
                        )
                    }
                }
            }
        }
    )
}

@Preview(showBackground = true)
@Composable
fun LoginPreview() {
    HandssaTheme {
        LoginView()
    }
}