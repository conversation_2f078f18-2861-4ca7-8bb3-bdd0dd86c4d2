package com.turinggear.ssa_hand.ui

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.turinggear.ssa_hand.viewmodel.GoodViewModel
import com.turinggear.ssa_shared.GOOD_CATEGORY_MAP
import com.turinggear.ssa_shared.SCANTYPE
import com.turinggear.ssa_shared.SCAN_TYPE
import com.turinggear.ssa_shared.SELECTED_GOOD
import com.turinggear.ssa_shared.SHOW_POPUP
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.ui.TicketCard

@Composable
fun TicketBuyView(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colors.onSurface.copy(alpha = 0.04f))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {

            val goodVM: GoodViewModel = viewModel()
            val goodsFlow = goodVM.goodsFlow.collectAsState()
            val msgFlow = goodVM.msgFlow.collectAsState()
            if (goodsFlow.value == null) {
                goodVM.update()
                val msg = msgFlow.value
                msg?.let {
                    if (it.isNotEmpty()) {
                        Toast.makeText(
                            LocalContext.current,
                            it,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }

            val goods = goodsFlow.value ?: listOf()

            val distinctCategoryValues = goods.map { it.category }.distinct().sorted()
            val firstTwoCategories = distinctCategoryValues.take(2)

            var goodsForOne: List<Good> = listOf()
            var goodsForTwo: List<Good> = listOf()
            var titleOne = ""
            var titleOneAmount = ""
            var titleTwo = ""
            var titleTwoAmount = ""

            if (firstTwoCategories.size > 0) {
                goodsForOne = goods.filter { it.category == firstTwoCategories.get(0) }
                titleOne = GOOD_CATEGORY_MAP.get(goodsForOne.get(0).category)?: ""
                titleOneAmount = "(共${goodsForOne.count()}种票)"
            }
            if (firstTwoCategories.size > 1) {
                goodsForTwo = goods.filter { it.category == firstTwoCategories.get(1) }
                titleTwo = GOOD_CATEGORY_MAP.get(goodsForTwo.get(0).category)?: ""
                titleTwoAmount = "(共${goodsForTwo.count()}种票)"
            }

            Column(modifier = Modifier
                .weight(1f)
                .padding(horizontal = 6.dp)) {
                Row(
                    modifier = Modifier
                        .padding(horizontal = 6.dp)
                        .fillMaxHeight(fraction = 0.2f)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = titleOne,
                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                        style = MaterialTheme.typography.caption
                    )
                    Spacer(modifier = Modifier.weight(1.0f))
                    Text(
                        text = titleOneAmount,
                        color = MaterialTheme.colors.onBackground.copy(0.6f),
                        style = MaterialTheme.typography.overline
                    )
                }
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .horizontalScroll(rememberScrollState()),
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    goodsForOne.forEachIndexed { _, good ->
                        TicketCard(
                            onClick = {
                                SHOW_POPUP = true
                                SCAN_TYPE = SCANTYPE.WECHAT_PAY
                                SELECTED_GOOD = good
                            },
                            Modifier
                                .padding(horizontal = 6.dp)
                                .padding(bottom = 12.dp)
                                .clip(RoundedCornerShape(6.dp))
                                .background(MaterialTheme.colors.primary.copy(alpha = 0.1f)),
                            good = good
                        )
                    }
                }
            }
            Divider(
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                thickness = 0.5.dp
            )
            Column(modifier = Modifier
                .weight(1f)
                .padding(horizontal = 6.dp)) {
                Row(
                    modifier = Modifier
                        .padding(horizontal = 6.dp)
                        .fillMaxHeight(fraction = 0.2f)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = titleTwo,
                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                        style = MaterialTheme.typography.caption
                    )
                    Spacer(modifier = Modifier.weight(1.0f))
                    Text(
                        text = titleTwoAmount,
                        color = MaterialTheme.colors.onBackground.copy(0.6f),
                        style = MaterialTheme.typography.overline
                    )
                }
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .horizontalScroll(rememberScrollState()),
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    goodsForTwo.forEachIndexed { _, good ->
                        TicketCard(
                            onClick = {
                                SHOW_POPUP = true
                                SCAN_TYPE = SCANTYPE.WECHAT_PAY
                                SELECTED_GOOD = good
                            },
                            Modifier
                                .padding(horizontal = 6.dp)
                                .padding(bottom = 12.dp)
                                .clip(RoundedCornerShape(6.dp))
                                .background(MaterialTheme.colors.primary.copy(alpha = 0.1f)),
                            good = good
                        )
                    }
                }
            }
        }
    }
}