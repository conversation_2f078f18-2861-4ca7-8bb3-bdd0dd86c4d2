package com.turinggear.ssa_hand.ui

import android.app.Activity
import android.content.Context
import android.util.Log
import android.widget.Toast
import android.widget.Toast.LENGTH_SHORT
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.turinggear.ssa_hand.BuildConfig
import com.turinggear.ssa_hand.MainActivity
import com.turinggear.ssa_hand.util.RequestManager
import com.turinggear.ssa_shared.ACCESS_TOKEN
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.CURRENT_USER
import com.turinggear.ssa_shared.STARTUP
import com.turinggear.ssa_shared.util.UpgradeUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun SettingsScreen() {
    Column(
        Modifier
            .fillMaxSize()
            .padding(horizontal = 30.dp),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxHeight(fraction = 1 / 10f),
            contentAlignment = Alignment.Center
        ) {
            Text(
                "设置",
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold)
            )
        }
        CURRENT_USER?.let {
            Box(
                modifier = Modifier
                    .fillMaxHeight(fraction = 1 / 10f),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    "当前用户:" + (it?.real_name) + "(" +  (it?.name) + ")",
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold)
                )
            }
        }
        Divider()
        val scope = rememberCoroutineScope()
        val ctx = LocalContext.current as Activity
        TextButton(
            onClick = {
                scope.launch {
                    val appVersion = RequestManager.startup(BuildConfig.VERSION_CODE)
                    if (appVersion == null) {
                        Toast.makeText(ctx, "无需更新", Toast.LENGTH_SHORT).show()
                        return@launch
                    } else {
                        if (!appVersion.down_url.endsWith(".apk", ignoreCase = true)) {
                            return@launch
                        }
                        val message = "开始到版本(${appVersion.code})的更新"
                        Toast.makeText(ctx, message, Toast.LENGTH_LONG).show()

                        val url = appVersion.down_url
//                        UpgradeManager.downloadAndUpgrade(
//                            ctx,
//                            url,
//                            isSilent = false
//                        )

                        var ret = false
                        try {
                            ret = UpgradeUtil.download(ctx, ctx.packageName, url)
                            Log.e("XXX", "downloaded")
                            if (ret) {
                                ret = UpgradeUtil.installInteractively(ctx, ctx.packageName)
                            }
                        } catch (e: Exception) {
                            Log.e("XXX", "error ${e.localizedMessage}")
                            Toast.makeText(ctx, e.localizedMessage, LENGTH_SHORT).show()

                            ret = false
                        }
                        Log.e("XXX", "download result $ret")
                        if (ret) {
                            Toast.makeText(ctx, "更新成功", LENGTH_SHORT).show()
                        }
                    }
                }
            },
            modifier = Modifier.padding(vertical = 5.dp)
        ) {
            Text(
                text = "检查新版本",
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                modifier = Modifier.padding(horizontal = 10.dp),
                style = MaterialTheme.typography.subtitle1.copy()
            )
        }
        Divider()
        TextButton(
            onClick = {
                ApplicationContext?.let {
                    ACCESS_TOKEN = ""
                    val sharedPref =
                        it.getSharedPreferences(BuildConfig.APPLICATION_ID, Context.MODE_PRIVATE)
                    sharedPref.edit().remove("ACCESS_TOKEN").apply()
                    Toast.makeText(
                        it,
                        "已登出",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            },
            modifier = Modifier.padding(vertical = 5.dp)
        ) {
            Text(
                text = "退出账号",
                modifier = Modifier.padding(horizontal = 10.dp),
                style = MaterialTheme.typography.subtitle1
            )
        }
    }
}