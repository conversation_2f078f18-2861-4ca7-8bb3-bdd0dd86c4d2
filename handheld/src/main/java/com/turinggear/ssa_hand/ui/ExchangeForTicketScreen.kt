package com.turinggear.ssa_hand.ui

import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.AlertDialog
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Print
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.gson.Gson
import com.sunmi.eidlibrary.EidConstants
import com.sunmi.eidlibrary.EidSDK
import com.sunmi.eidlibrary.bean.BaseInfo
import com.sunmi.eidlibrary.bean.ResultInfo
import com.turinggear.ssa_hand.MARGIN
import com.turinggear.ssa_hand.sunmi.SunmiPrinterManager
import com.turinggear.ssa_hand.util.RequestManager
import com.turinggear.ssa_shared.ApplicationContext
import com.turinggear.ssa_shared.TicketsForExchange
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.model.TicketGoods
import com.turinggear.ssa_shared.model.ticketStatusColorFrom
import com.turinggear.ssa_shared.model.ticketStatusStringFrom
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.util.Locale
import com.turinggear.ssa_shared.R

// 基本过程
// 初始化sdk
// 初始提示 请刷卡，刷卡时请勿移动卡片
// 正在读卡 开始读卡，请勿移动
// 正在解码 读卡成功，reqId：$msg (网络请求解码)
// 解码成功 获取身份证号
// 启用全部打印按钮
// 请求后端接口  查询身份证关联的票 列出 需要接口
// 清空界面 并禁用全部打印按钮
//
// 商米文档
// 功能说明 [证件云解](https://developer.sunmi.com/zh-CN/ability/eid/)
// 对接 [证件云解安卓SDK对接·SUNMI](https://developer.sunmi.com/docs/zh-CN/cdixeghjk491/qqdeghjk524)
// 注意示例使用该文档里的 [EID-Demo/EidCardDemo-OpenApi at develop\_v2](https://github.com/EID-Demo/EidCardDemo-OpenApi/tree/develop_v2)
// 设备是V2s [V2s文档](https://developer.sunmi.com/docs/zh-CN/ceghjk502/fxdeghjk524)

val TAG = "EIDLINK"
val APP_ID = "8b8311873054409f86d348808b9aba5d";
val APP_KEY = "c43ce77c48ff4e769237b1beb73f8056";

fun clearData() {
    MsgPair = Pair(0, "请放上身份证，直到读取完成再移开")
//    CardInfo = null
    TicketsForExchange = null
}

// 当前EID SDK返回的消息
// 0 正常步骤提示 -1 错误 1 已经读取到身份证信息
var MsgPair by mutableStateOf(Pair<Int, String>(0, ""))
// 读到的身份证的信息
//var CardInfo: BaseInfo? by mutableStateOf(null)

fun colorForMsg(pair: Pair<Int, String>): Color {
    when (pair.first) {
        1 -> return Color.Green
        -1 -> return Color.Red
        else -> return Color.Black
    }
}

@Composable
fun ExchangeForTicketScreen() {
    val context = LocalContext.current
    val codeFormat = context.resources.getInteger(R.integer.ticket_qrcode_format)

    // 对话框状态
    var showIdInputDialog by remember { mutableStateOf(false) }
    var idNumber by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    // 界面显示时候
    LaunchedEffect(Unit) {
        clearData()
        EidSDK.setDebuggable(true)
        // 初始化
        EidSDK.init(
            context, APP_ID
        ) { code, msg -> MsgPair = Pair(0, "请等待,正在初始化 $msg($code)") }

        // 启动即读卡
        startCheckCard(context as Activity)
    }

    // 界面销毁时候
    DisposableEffect(Unit) {
        onDispose {
            EidSDK.stopCheckCard(context as Activity);
            clearData()

            // 销毁
            EidSDK.destroy();
        }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    "使用身份证换票",
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold),
                    modifier = Modifier.padding(bottom = 10.dp)
                )
//            Text(
//                text = "请放上身份证，直到读取完成再移开",
//                color = MaterialTheme.colors.onSurface.copy(alpha = 0.5f),
//                textAlign = TextAlign.Center,
//                style = MaterialTheme.typography.subtitle1
//            )

                // 显示读卡消息/错误
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 60.dp),
                    text = MsgPair.second,
                    color = colorForMsg(MsgPair),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.subtitle1
                )
            }

            val scrollState = rememberScrollState()

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MARGIN.dp)
                    .padding(bottom = 5.dp)
            ) {
                // 打印时候禁用按钮用
                var isPrinting by remember { mutableStateOf(false) }
                val coroutineScope = rememberCoroutineScope()

                Button(
                    modifier = Modifier
                        .fillMaxWidth() // Makes the button full width
                        .height(60.dp), // Adjust the height as needed
                    onClick = {
                        isPrinting = true
                        Toast.makeText(
                            ApplicationContext,
                            "正在出票…",
                            Toast.LENGTH_SHORT
                        ).show()
                        TicketsForExchange?.forEach { ticket ->
                            SunmiPrinterManager.print(qrcodeFormat = codeFormat,
                                ticket = ticket, header = "(换票)")
                        }

                        coroutineScope.launch {
                            delay(1500)
                            isPrinting = false

                            clearData()
                        }
                    },
                    colors = ButtonDefaults.buttonColors(backgroundColor = Color(0xFF53A949)),
//                enabled = !isPrinting //debug
                    enabled = (MsgPair.first == 1) && !isPrinting
                ) {
                    Text("全部打印", style = MaterialTheme.typography.h6.copy(fontSize = 18.sp))
                }
            }

            // 票列表
            Column(
                modifier = Modifier
                    .padding(horizontal = MARGIN.dp)
                    .verticalScroll(scrollState)
            ) {
                TicketsForExchange?.let {
                    it.forEach { ticket ->
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 15.dp)
                                .border(
                                    0.5.dp,
                                    MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                                    RoundedCornerShape(8.dp)
                                )
                                .background(
                                    MaterialTheme.colors.onSurface.copy(alpha = 0.04f),
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(horizontal = 15.dp, vertical = 12.dp),
                            contentAlignment = Alignment.BottomEnd
                        ) {
                            val text =
                                "编号：${ticket.ticket_no}\n时间：${ticket.created_at}\n票种：${ticket.goods?.description}\n单价：${ticket.goods?.price}元"
                            Column(Modifier.fillMaxWidth()) {
                                Text(text = text, style = MaterialTheme.typography.caption)
                            }
                            var tintColor = Color.LightGray
                            if (ticket.status == 10) {
                                tintColor = Color(0xFF53A949)
                            }
                            Box(
                                modifier = Modifier
                                    .padding(8.dp)
                                    .background(
                                        tintColor.copy(alpha = 0.2f)
                                    )
                            ) {
                                Text(
                                    text = " ${ticketStatusStringFrom(ticket)}",
                                    color = ticketStatusColorFrom(ticket),
                                    style = MaterialTheme.typography.caption
                                )
                            }
                        }
                    }
                }
            }
        }

        // 浮动按钮：手动输入身份证号
        FloatingActionButton(
            onClick = {
                showIdInputDialog = true
                idNumber = ""
            },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp)
                .size(56.dp),
            backgroundColor = MaterialTheme.colors.primary
        ) {
            Icon(
                Icons.Filled.Edit,
                contentDescription = "手动输入身份证号",
                tint = Color.White
            )
        }
    }

    // 身份证号输入对话框
    if (showIdInputDialog) {
        AlertDialog(
            onDismissRequest = {
                if (!isLoading) {
                    showIdInputDialog = false
                    idNumber = ""
                }
            },
            title = {
                Text("手动输入身份证号")
            },
            text = {
                Column {
                    Text(
                        text = "请输入身份证号码",
                        style = MaterialTheme.typography.body2,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    OutlinedTextField(
                        value = idNumber,
                        onValueChange = {
                            // 只允许输入数字和X，最多18位
                            if (it.length <= 18 && it.all { char -> char.isDigit() || char.uppercase() == "X" }) {
                                idNumber = it.uppercase()
                            }
                        },
                        label = { Text("身份证号") },
                        singleLine = true,
                        enabled = !isLoading,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (idNumber.length == 18) {
                            isLoading = true
                            coroutineScope.launch {
                                try {
                                    val result = RequestManager.requestTicketsWithNationId(idNumber)
                                    withContext(Dispatchers.Main) {
                                        Toast.makeText(context, result, Toast.LENGTH_LONG).show()
                                        if (result == "请求成功") {
                                            showIdInputDialog = false
                                            idNumber = ""

                                            MsgPair = Pair(1, "手动查询身份证号成功")
                                        } else {
                                            AlertDialog.Builder(context)
                                                .setTitle("查询结果")
                                                .setMessage(result)
                                                .setPositiveButton("确定", null)
                                                .show()
                                        }
                                    }
                                } catch (e: Exception) {
                                    withContext(Dispatchers.Main) {
                                        Toast.makeText(
                                            context,
                                            "查询失败: ${e.localizedMessage}",
                                            Toast.LENGTH_LONG
                                        ).show()
                                    }
                                } finally {
                                    isLoading = false
                                }
                            }
                        } else {
                            Toast.makeText(
                                context,
                                "请输入完整的18位身份证号",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    },
                    enabled = !isLoading && idNumber.length == 18
                ) {
                    if (isLoading) {
                        Text("查询中...")
                    } else {
                        Text("查询")
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        if (!isLoading) {
                            showIdInputDialog = false
                            idNumber = ""
                        }
                    },
                    enabled = !isLoading
                ) {
                    Text("取消")
                }
            }
            )
    }
}

private fun startCheckCard(ctx: Activity) {
    //Step 2 开启读卡 -> 调用startCheckCard方法，通过回调结果处理业务逻辑
    //注：默认循环读卡，只会回调一次EidConstants.READ_CARD_READY
    //是否需要获取图片，不获取图片要快些，大约300-500ms
    val map: MutableMap<String?, Any?> = HashMap()
    map[EidSDK.PARAMS_IS_READ_PICTURE] = false
    EidSDK.startCheckCard(ctx, { code, msg ->
        Log.e(
            TAG,
            "onCallData-$code , $msg"
        )
        when (code) {
            EidConstants.ERR_NFC_NOT_SUPPORT -> {
                Log.e(TAG, "机器不支持NFC")
                // 该机器不支持NFC功能，无法使用SDK
                MsgPair = Pair(-1, String.format(Locale.getDefault(), "机器不支持NFC"))
            }

            EidConstants.ERR_NETWORK_NOT_CONNECTED -> {
                Log.e(
                    TAG,
                    "网络未连接，连接网络后重新调用 startCheckCard 方法"
                )
                MsgPair = Pair(-1, String.format(Locale.getDefault(), "网络未连接，请联网后重试"))
            }

            EidConstants.ERR_NFC_CLOSED -> {
                Log.e(
                    TAG,
                    "NFC 未打开，打开后重试 ：$msg"
                )
                MsgPair = Pair(-1, String.format(Locale.getDefault(), "NFC未打开，请打开后重试"))
            }

            EidConstants.READ_CARD_READY -> {
                //Step 3 读卡准备完成 -> 业务方可以引导用户开始进行刷卡操作
                Log.e(TAG, "SDK准备完成，请刷卡")
                clearData()
                MsgPair = Pair(0, "请刷卡，刷卡时请勿移动卡片")
            }

            EidConstants.READ_CARD_START -> {
                //Step 4 读卡中 -> 业务方可以提醒用户"读卡中，请勿移动卡片"
//                timeMillis = System.currentTimeMillis()
                Log.e(TAG, "开始读卡，请勿移动")
                clearData()
                MsgPair = Pair(0, "开始读卡，请勿移动")
            }

            EidConstants.READ_CARD_SUCCESS -> {
//                readCardTimeMillis = (System.currentTimeMillis() - timeMillis)
                //Step 5 读卡成功 -> 返回的msg为reqId，通过 reqId 业务方走云对云方案获取身份证信息
                //注：如不需要循环读卡，可在此处调用stopCheckCard方法
                Log.e(
                    TAG,
                    "请等待... 准备解码，reqId：$msg"
                )
                MsgPair = Pair(0, String.format("可以移除卡片,请等待解码: %s", msg))
                decode(ctx, msg)
            }

            EidConstants.READ_CARD_FAILED -> {
                //*** 异常处理： 读卡失败，请重新读卡 ***
                Log.e(
                    TAG,
                    "读卡失败：$msg"
                )
                MsgPair = Pair(-1, String.format(Locale.getDefault(), "读卡错误,请重新贴卡：%s", msg))
            }

            else -> {
                //*** 异常处理： 其他失败 - code为错误码，msg为详细错误原因 需要重新调用 startCheckCard 方法 （手动触发，非自动）***
                Log.e(
                    TAG,
                    "读卡失败：code:$code,msg:$msg"
                )
                MsgPair = Pair(-1, String.format(Locale.getDefault(), "其他错误：%d,%s", code, msg))
            }
        }
    }, map)
}

fun maskString(input: String): String {
    if (input.length <= 10) {
        return input
    }
    val firstFive = input.substring(0, 5)
    val lastFour = input.substring(input.length - 4)
    return "$firstFive*****$lastFour"
}

fun decode(context: Context, reqId: String?) {
    //调用SDK的解码，存在泄漏key的风险，建议使用云对云方案
    //传入读卡获取的reqId，商米partner平台上的appkey，以及结果callback
    return EidSDK.getIDCardInfo(reqId, APP_KEY) { code, data ->
        //EidConstants.DECODE_SUCCESS 解码成功，data为身份证信息的gson格式，可直接解析成SDK中提供的 ResultInfo 实体类
        if (code == EidConstants.DECODE_SUCCESS) {
            val result: ResultInfo =
                Gson().fromJson(data, ResultInfo::class.java)
            val info = result!!.info
            MsgPair = Pair(1, "读取成功 ${maskString(info.idnum)}\n可以移除身份证")
//            CardInfo = info
            // 调用我们sdk
            GlobalScope.launch {
                val result = RequestManager.requestTicketsWithNationId(info.idnum)
//                val result = RequestManager.requestTicketsWithNationId("130324198111110636") //dbg
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, result, Toast.LENGTH_LONG).show()
                    if (result != "请求成功") {
                        AlertDialog.Builder(context)
                            .setTitle("警告")
                            .setMessage(result)
                            .setPositiveButton("确定") { _, _ -> clearData() }
                            .setCancelable(false)
                            .show()
                    }
                }
            }
        } else {
            Log.e(TAG, "解码失败：$code $data")
            MsgPair = Pair(-1, "解码失败$code,请重新放置卡片")
        }
    }
}