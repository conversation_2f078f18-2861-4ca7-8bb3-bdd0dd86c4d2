package com.turinggear.ssa_hand.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Done
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_shared.PAYMENTSTATE
import com.turinggear.ssa_shared.PAYMENT_STATE

@Composable
fun WechatPayButton(modifier: Modifier = Modifier) {

    val tipText = when (PAYMENT_STATE) {
        PAYMENTSTATE.MAKING_ORDER -> "正在创建订单..."
        PAYMENTSTATE.USERPAYING -> "正在支付，请稍等..."
        PAYMENTSTATE.SUCCEED -> "支付成功"
        PAYMENTSTATE.FAILED -> "支付失败，请重试"
        else -> "请在扫码口出示微信付款码"
    }

    Column(
        modifier = modifier
            .fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(
            12.dp,
            alignment = Alignment.CenterVertically
        ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = tipText,
            color = Color(0xFFFFAB00),
            style = MaterialTheme.typography.overline
        )
        Button(
            onClick = { PAYMENT_STATE = PAYMENTSTATE.USER_TAPPED },
            enabled = (PAYMENT_STATE == PAYMENTSTATE.UNSTARTED),
            colors = ButtonDefaults.outlinedButtonColors(
                backgroundColor = Color(
                    color = 0xFF57AB6D
                )
            )
        ) {
            when {
                (PAYMENT_STATE > PAYMENTSTATE.UNSTARTED) and (PAYMENT_STATE < PAYMENTSTATE.SUCCEED) -> CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = Color.White
                )
                PAYMENT_STATE == PAYMENTSTATE.SUCCEED -> Icon(
                    Icons.Outlined.Done,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = Color.White
                )
                else -> Text(
                    text = "微信付款",
                    color = Color.White,
                    style = MaterialTheme.typography.body1.copy(fontWeight = FontWeight.Bold)
                )
            }
        }
    }
}