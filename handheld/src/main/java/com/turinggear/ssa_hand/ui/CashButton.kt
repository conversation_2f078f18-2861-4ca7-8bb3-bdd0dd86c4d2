package com.turinggear.ssa_hand.ui

import android.widget.Toast
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_hand.sunmi.SunmiPrinterManager
import com.turinggear.ssa_hand.util.KtorClient
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.decodeFromString
import com.turinggear.ssa_shared.R

@Composable
fun CashButton(
    // 现金或者换票类 无需支付的
    pay_platform: Int,
    paymentText: String,
    modifier: Modifier = Modifier) {

    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        OutlinedButton(
            onClick = {
                isLoading = true
                scope.launch {
                    try {
                        // one. 创建订单（现金类型）
                        val one: ResponseOrder = KtorClient.httpClient()
                            .post(urlString = apiTicketOrders() + "/${SELECTED_GOOD?.id}") {
                                contentType(ContentType.Application.Json)
                                setBody(BodyForOrderMake(
                                    pay_platform = pay_platform, // 现金支付
                                    num = SELECTED_AMOUNT
                                ))
                            }.body()
                        val orderNew = one.data
                        // two. 支付订单（现金类型的订单也需要调用此接口）
                        val two: ResponseOrder = KtorClient.httpClient()
                            .put(urlString = apiTicketOrdersPay()) {
                                contentType(ContentType.Application.Json)
                                setBody(BodyForOrderPay(order = orderNew.order_no, null))
                            }.body()
                        val orderPaid = two.data
                        delay(3000)
                        // three. 获取车票，并打印
                        val codeFormat = context.resources.getInteger(R.integer.ticket_qrcode_format)
                        val three: ResponseTicketList = KtorClient.httpClient()
                            .get(urlString = apiTicketTickets(orderPaid.order_no)).body()
                        if (three.code == 200) {
                            isLoading = false
                            Toast.makeText(
                                context,
                                "正在出票",
                                Toast.LENGTH_SHORT
                            ).show()
                            three.data?.let {
                                it.forEach { ticket ->
                                    SELECTED_GOOD?.let { g ->
                                        SunmiPrinterManager.print(
                                            qrcodeFormat = codeFormat,
                                            ticket = ticket,
                                            good = g,
                                            order = orderPaid
                                        )
                                    }
                                }
                                SHOW_POPUP = false
                                SCAN_TYPE = SCANTYPE.NONE
                                SELECTED_AMOUNT = 1
                            }
                        }
                    } catch (e: ClientRequestException) {
                        try {
                            val responseMsg =
                                JsonFormat.decodeFromString<ResponseMsg>(
                                    e.response.bodyAsText()
                                )
                            val text =
                                "${responseMsg.code}: ${responseMsg.msg}"
                            Toast.makeText(
                                context,
                                text,
                                Toast.LENGTH_LONG
                            ).show()
                        } catch (e: Exception) {
                            Toast.makeText(
                                context,
                                e.localizedMessage,
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        isLoading = false
                    } catch (e: Exception) {
                        Toast.makeText(
                            context,
                            e.localizedMessage,
                            Toast.LENGTH_LONG
                        ).show()
                        isLoading = false
                    }
                }
            },
            enabled = !isLoading
        ) {
            Box(contentAlignment = Alignment.Center) {
                Text(
                    text = paymentText,
                    color = MaterialTheme.colors.primary.copy(if (isLoading) 0f else 1f)
                )
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}