package com.turinggear.ssa_hand.sunmi;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;

import java.util.Hashtable;

class BytesUtil {

	//字节流转16进制字符串
	public static String getHexStringFromBytes(byte[] data) {
		if (data == null || data.length <= 0) {
			return null;
		}
		String hexString = "0123456789ABCDEF";
		int size = data.length * 2;
		StringBuilder sb = new StringBuilder(size);
		for (int i = 0; i < data.length; i++) {
			sb.append(hexString.charAt((data[i] & 0xF0) >> 4));
			sb.append(hexString.charAt((data[i] & 0x0F) >> 0));
		}
		return sb.toString();
	}

	//单字符转字节
	private static byte charToByte(char c) {
		return (byte) "0123456789ABCDEF".indexOf(c);
	}

	//16进制字符串转字节数组
	@SuppressLint("DefaultLocale")
	public static byte[] getBytesFromHexString(String hexstring){
		if(hexstring == null || hexstring.equals("")){
			return null;
		}
		hexstring = hexstring.replace(" ", "");
		hexstring = hexstring.toUpperCase();
		int size = hexstring.length()/2;
		char[] hexarray = hexstring.toCharArray();
		byte[] rv = new byte[size];
		for(int i=0; i<size; i++){
			int pos = i * 2;
			rv[i] = (byte) (charToByte(hexarray[pos]) << 4 | charToByte(hexarray[pos + 1]));
		}
		return rv;
	}

	//十进制字符串转字节数组
	@SuppressLint("DefaultLocale")
	public static byte[] getBytesFromDecString(String decstring){
		if(decstring == null || decstring.equals("")){
			return null;
		}
		decstring = decstring.replace(" ", "");
		int size = decstring.length()/2;
		char[] decarray = decstring.toCharArray();
		byte[] rv = new byte[size];
		for(int i=0; i<size; i++){
			int pos = i * 2;
			rv[i] = (byte) (charToByte(decarray[pos])*10 + charToByte(decarray[pos + 1]));
		}
		return rv;
	}

	//字节数组组合操作1
	public static byte[] byteMerger(byte[] byte_1, byte[] byte_2) {
		byte[] byte_3 = new byte[byte_1.length + byte_2.length];
		System.arraycopy(byte_1, 0, byte_3, 0, byte_1.length);
		System.arraycopy(byte_2, 0, byte_3, byte_1.length, byte_2.length);
		return byte_3;
	}

	//字节数组组合操作2
	public static byte[] byteMerger(byte[][] byteList) {

		int length = 0;
		for (int i = 0; i < byteList.length; i++) {
			length += byteList[i].length;
		}
		byte[] result = new byte[length];

		int index = 0;
		for (int i = 0; i < byteList.length; i++) {
			byte[] nowByte = byteList[i];
			for (int k = 0; k < byteList[i].length; k++) {
				result[index] = nowByte[k];
				index++;
			}
		}
		for (int i = 0; i < index; i++) {
			// CommonUtils.LogWuwei("", "result[" + i + "] is " + result[i]);
		}
		return result;
	}

	//生成表格字节流
	public static byte[] initTable(int h, int w){
		int hh = h * 32;
		int ww = w * 4;

		byte[] data = new byte[ hh * ww + 5];


		data[0] = (byte)ww;//xL
		data[1] = (byte)(ww >> 8);//xH
		data[2] = (byte)hh;
		data[3] = (byte)(hh >> 8);

		int k = 4;
		int m = 31;
		for(int i=0; i<h; i++){
			for(int j=0; j<w; j++){
				data[k++] = (byte)0xFF;
				data[k++] = (byte)0xFF;
				data[k++] = (byte)0xFF;
				data[k++] = (byte)0xFF;
			}
			if(i == h-1) m =30;
			for(int t=0; t< m; t++){
				for(int j=0; j<w-1; j++){
					data[k++] = (byte)0x80;
					data[k++] = (byte)0;
					data[k++] = (byte)0;
					data[k++] = (byte)0;
				}
				data[k++] = (byte)0x80;
				data[k++] = (byte)0;
				data[k++] = (byte)0;
				data[k++] = (byte)0x01;
			}
		}
		for(int j=0; j<w; j++){
			data[k++] = (byte)0xFF;
			data[k++] = (byte)0xFF;
			data[k++] = (byte)0xFF;
			data[k++] = (byte)0xFF;
		}
		data[k++] = 0x0A;
		return data;
	}

	/**
	 * 生成多个二维码字节流
	 */
	public static byte[] getZXingQRCode(String qr1, String qr2, int size) {
		try {
			Hashtable<EncodeHintType, String> hints = new Hashtable<EncodeHintType, String>();
			hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
			//图像数据转换，使用了矩阵转换
			BitMatrix bitMatrix1 = new QRCodeWriter().encode(qr1, BarcodeFormat.QR_CODE,
					size, size, hints);
			BitMatrix bitMatrix2 = new QRCodeWriter().encode(qr2, BarcodeFormat.QR_CODE,
					size, size, hints);
			return getBytesFromBitMatrix(bitMatrix1, bitMatrix2, 40);
		} catch (WriterException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 合并两个矩阵数据
	 */
	public static byte[] getBytesFromBitMatrix(BitMatrix bits1, BitMatrix bits2, int space) {
		if (bits1 == null || bits2 == null) return null;

		int h1 = bits1.getHeight();
		int w1 = bits1.getWidth();
		int h2 = bits2.getHeight();
		int w2 = bits2.getWidth();
		int h = Math.max(h1, h2);
		int w = (w1 + w2 + space + 7)/8;

		byte[] rv = new byte[h * w + 4];

		rv[0] = (byte) w;//xL
		rv[1] = (byte) (w >> 8);//xH
		rv[2] = (byte) h;
		rv[3] = (byte) (h >> 8);

		int k = 4;
		byte b;
		for (int i = 0; i < h; i++) {
			for (int j = 0; j < w; j++) {
				for (int n = 0; n < 8; n++) {
					int pos = j * 8 + n;
					if(pos < w1) {
						if(i < h1) {
							b = getBitMatrixColor(bits1, pos, i);
						} else {
							b = 0;
						}
						rv[k] += rv[k] + b;
					} else if(pos < (w1 + space)) {
						rv[k] += rv[k];
					} else {
						if(i < h2) {
							b = getBitMatrixColor(bits2, pos - w1 - space, i);
						} else {
							b = 0;
						}
						rv[k] += rv[k] + b;
					}
				}
				k++;
			}
		}
		return rv;
	}

	private static byte getBitMatrixColor(BitMatrix bits, int x, int y) {
		int width = bits.getWidth();
		int height = bits.getHeight();
		if (x >= width || y >= height || x < 0 || y < 0) return 0;
		if (bits.get(x, y)) {
			return 1;
		} else {
			return 0;
		}
	}

	/**
	 * 将bitmap图转换为头四位有宽高的光栅位图
	 */
	public static byte[] getBytesFromBitMap(Bitmap bitmap) {
		int width = bitmap.getWidth();
		int height = bitmap.getHeight();
		int bw = (width - 1) / 8 + 1;

		byte[] rv = new byte[height * bw + 4];
		rv[0] = (byte) bw;//xL
		rv[1] = (byte) (bw >> 8);//xH
		rv[2] = (byte) height;
		rv[3] = (byte) (height >> 8);

		int[] pixels = new int[width * height];
		bitmap.getPixels(pixels, 0, width, 0, 0, width, height);

		for (int i = 0; i < height; i++) {
			for (int j = 0; j < width; j++) {
				int clr = pixels[width * i + j];
				int red = (clr & 0x00ff0000) >> 16;
				int green = (clr & 0x0000ff00) >> 8;
				int blue = clr & 0x000000ff;
				byte gray = (RGB2Gray(red, green, blue));
				rv[bw*i + j/8 + 4] = (byte) (rv[bw*i + j/8 + 4] | (gray << (7 - j % 8)));
			}
		}

		return rv;
	}

	/**
	 * 将bitmap转成按mode指定的N点行数据
	 */
	public static byte[] getBytesFromBitMap(Bitmap bitmap, int mode) {
		int width = bitmap.getWidth();
		int height = bitmap.getHeight();
		int[] pixels = new int[width*height];
		if(mode == 0 || mode == 1){
			byte[] res = new byte[width*height/8 + 5*height/8];
			bitmap.getPixels(pixels, 0, width, 0, 0, width, height);
			for(int i = 0; i < height/8; i++){
				res[0 + i*(width+5)] = 0x1b;
				res[1 + i*(width+5)] = 0x2a;
				res[2 + i*(width+5)] = (byte) mode;
				res[3 + i*(width+5)] = (byte) (width%256);
				res[4 + i*(width+5)] = (byte) (width/256);
				for(int j = 0; j < width; j++){
					byte gray = 0;
					for(int m = 0; m < 8; m++){
						int clr = pixels[j + width*(i*8+m)];
						int red = (clr & 0x00ff0000) >> 16;
						int green = (clr & 0x0000ff00) >> 8;
						int blue = clr & 0x000000ff;
						gray = (byte) ((RGB2Gray(red, green, blue)<<(7-m))|gray);
					}
					res[5 + j + i*(width+5)] = gray;
				}
			}
			return res;
		}else if(mode == 32 || mode == 33){
			byte[] res = new byte[width*height/8 + 5*height/24];
			bitmap.getPixels(pixels, 0, width, 0, 0, width, height);
			for(int i = 0; i < height/24; i++){
				res[0 + i*(width*3+5)] = 0x1b;
				res[1 + i*(width*3+5)] = 0x2a;
				res[2 + i*(width*3+5)] = (byte) mode;
				res[3 + i*(width*3+5)] = (byte) (width%256);
				res[4 + i*(width*3+5)] = (byte) (width/256);
				for(int j = 0; j < width; j++){
					for(int n = 0; n < 3; n++){
						byte gray = 0;
						for(int m = 0; m < 8; m++){
							int clr = pixels[j + width*(i*24 + m + n*8)];
							int red = (clr & 0x00ff0000) >> 16;
							int green = (clr & 0x0000ff00) >> 8;
							int blue = clr & 0x000000ff;
							gray = (byte) ((RGB2Gray(red, green, blue)<<(7-m))|gray);
						}
						res[5 + j*3 + i*(width*3+5) + n] = gray;
					}
				}
			}
			return res;
		}else{
			return new byte[]{0x0A};
		}

	}



	private static byte RGB2Gray(int r, int g, int b) {
		return (((int) (0.29900 * r + 0.58700 * g + 0.11400 * b) < 200)) ? (byte) 1 : (byte) 0;
	}

	/**
	 * 生成间断性黑块数据
	 * @param w : 打印纸宽度, 单位点
	 * @return
	 */
	public static byte[] initBlackBlock(int w){
		int ww = (w + 7)/8 ;
		int n = (ww + 11)/12;
		int hh = n * 24;
		byte[] data = new byte[ hh * ww + 5];

		data[0] = (byte)ww;//xL
		data[1] = (byte)(ww >> 8);//xH
		data[2] = (byte)hh;
		data[3] = (byte)(hh >> 8);

		int k = 4;
		for(int i=0; i < n; i++){
			for(int j=0; j<24; j++){
				for(int m =0; m<ww; m++){
					if(m/12 == i){
						data[k++] = (byte)0xFF;
					}else{
						data[k++] = 0;
					}
				}
			}
		}
		data[k++] = 0x0A;
		return data;
	}

	/**
	 * 生成一大块黑块数据
	 * @param h : 黑块高度, 单位点
	 * @param w : 黑块宽度, 单位点, 8的倍数
	 * @return
	 */
	public static byte[] initBlackBlock(int h, int w){
		int hh = h;
		int ww = (w - 1)/8 + 1;
		byte[] data = new byte[ hh * ww + 6];

		data[0] = (byte)ww;//xL
		data[1] = (byte)(ww >> 8);//xH
		data[2] = (byte)hh;
		data[3] = (byte)(hh >> 8);

		int k = 4;
		for(int i=0; i<hh; i++){
			for(int j=0; j<ww; j++){
				data[k++] = (byte)0xFF;
			}
		}
		data[k++] = 0x00;data[k++] = 0x00;
		return data;
	}

	/**
	 * 百度小票 (Baidu ticket for ESC cmd）
	 */
	public static byte[] getBaiduTestBytes() {
		byte[] rv = new byte[]{
				0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x11, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x1b, 0x45, 0x01, 0x1b, 0x47, 0x01, (byte) 0xb1, (byte) 0xbe
				, (byte) 0xb5, (byte) 0xea, (byte) 0xc1, (byte) 0xf4, (byte) 0xb4, (byte) 0xe6, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x0a
				, 0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x11, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x1b, 0x45, 0x01, 0x1b, 0x47, 0x01, 0x1b, 0x61
				, 0x01, 0x23, 0x31, 0x35, 0x20, (byte) 0xb0, (byte) 0xd9, (byte) 0xb6, (byte) 0xc8, (byte) 0xcd, (byte) 0xe2, (byte) 0xc2, (byte) 0xf4, 0x0a, 0x5b, (byte) 0xbb, (byte) 0xf5, (byte) 0xb5, (byte) 0xbd, (byte) 0xb8, (byte) 0xb6, (byte) 0xbf, (byte) 0xee, 0x5d, 0x0a, 0x1b, 0x4d, 0x00
				, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a
				, 0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x01, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, (byte) 0xc6, (byte) 0xda, (byte) 0xcd, (byte) 0xfb, (byte) 0xcb, (byte) 0xcd, (byte) 0xb4, (byte) 0xef
				, (byte) 0xca, (byte) 0xb1, (byte) 0xbc, (byte) 0xe4, (byte) 0xa3, (byte) 0xba, (byte) 0xc1, (byte) 0xa2, (byte) 0xbc, (byte) 0xb4, (byte) 0xc5, (byte) 0xe4, (byte) 0xcb, (byte) 0xcd, 0x0a, (byte) 0xb6, (byte) 0xa9, (byte) 0xb5, (byte) 0xa5, (byte) 0xb1, (byte) 0xb8, (byte) 0xd7, (byte) 0xa2, (byte) 0xa3, (byte) 0xba, (byte) 0xc7, (byte) 0xeb, (byte) 0xcb
				, (byte) 0xcd, (byte) 0xb5, (byte) 0xbd, (byte) 0xbf, (byte) 0xfc, (byte) 0xbf, (byte) 0xc6, (byte) 0xce, (byte) 0xf7, (byte) 0xc3, (byte) 0xc5, 0x2c, (byte) 0xb2, (byte) 0xbb, (byte) 0xd2, (byte) 0xaa, (byte) 0xc0, (byte) 0xb1, 0x0a, (byte) 0xb7, (byte) 0xa2, (byte) 0xc6, (byte) 0xb1, (byte) 0xd0, (byte) 0xc5, (byte) 0xcf, (byte) 0xa2, (byte) 0xa3
				, (byte) 0xba, (byte) 0xb0, (byte) 0xd9, (byte) 0xb6, (byte) 0xc8, (byte) 0xcd, (byte) 0xe2, (byte) 0xc2, (byte) 0xf4, (byte) 0xb7, (byte) 0xa2, (byte) 0xc6, (byte) 0xb1, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47
				, 0x00, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a
				, 0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, (byte) 0xb6, (byte) 0xa9, (byte) 0xb5, (byte) 0xa5, (byte) 0xb1, (byte) 0xe0, (byte) 0xba, (byte) 0xc5
				, (byte) 0xa3, (byte) 0xba, 0x31, 0x34, 0x31, 0x38, 0x37, 0x31, 0x38, 0x36, 0x39, 0x31, 0x31, 0x36, 0x38, 0x39, 0x0a, (byte) 0xcf, (byte) 0xc2, (byte) 0xb5, (byte) 0xa5, (byte) 0xca, (byte) 0xb1, (byte) 0xbc, (byte) 0xe4, (byte) 0xa3, (byte) 0xba, 0x32
				, 0x30, 0x31, 0x34, 0x2d, 0x31, 0x32, 0x2d, 0x31, 0x36, 0x20, 0x31, 0x36, 0x3a, 0x33, 0x31, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00
				, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a
				, 0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x01, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, (byte) 0xb2, (byte) 0xcb, (byte) 0xc6, (byte) 0xb7, (byte) 0xc3, (byte) 0xfb, (byte) 0xb3, (byte) 0xc6
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, (byte) 0xca, (byte) 0xfd, (byte) 0xc1, (byte) 0xbf, 0x20, 0x20, 0x20, 0x20, 0x20, (byte) 0xbd, (byte) 0xf0, (byte) 0xb6, (byte) 0xee, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61
				, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x01, 0x1b
				, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, (byte) 0xcf, (byte) 0xe3, (byte) 0xc0, (byte) 0xb1, (byte) 0xc3, (byte) 0xe6, (byte) 0xcc, (byte) 0xd7, (byte) 0xb2, (byte) 0xcd, 0x1b, 0x24, (byte) 0xf2, 0x00, 0x31, 0x1b, 0x24, 0x25, 0x01, (byte) 0xa3
				, (byte) 0xa4, 0x34, 0x30, 0x2e, 0x30, 0x30, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x1b, 0x4d, 0x00
				, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x01, 0x1b, 0x45, 0x00, 0x1b
				, 0x47, 0x00, 0x1b, 0x61, 0x00, (byte) 0xcb, (byte) 0xd8, (byte) 0xca, (byte) 0xb3, (byte) 0xcc, (byte) 0xec, (byte) 0xcf, (byte) 0xc2, (byte) 0xba, (byte) 0xba, (byte) 0xb1, (byte) 0xa4, 0x1b, 0x24, (byte) 0xf2, 0x00, 0x31, 0x1b, 0x24, 0x25, 0x01, (byte) 0xa3, (byte) 0xa4
				, 0x33, 0x38, 0x2e, 0x30, 0x30, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x1b, 0x4d, 0x00, 0x1b
				, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00
				, 0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a
				, 0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a, 0x1b, 0x4d, 0x00
				, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x01, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, (byte) 0xd0, (byte) 0xd5, (byte) 0xc3, (byte) 0xfb, (byte) 0xa3, (byte) 0xba, (byte) 0xb0, (byte) 0xd9, (byte) 0xb6, (byte) 0xc8, (byte) 0xb2, (byte) 0xe2, (byte) 0xca
				, (byte) 0xd4, 0x0a, (byte) 0xb5, (byte) 0xd8, (byte) 0xd6, (byte) 0xb7, (byte) 0xa3, (byte) 0xba, (byte) 0xbf, (byte) 0xfc, (byte) 0xbf, (byte) 0xc6, (byte) 0xbf, (byte) 0xc6, (byte) 0xbc, (byte) 0xbc, (byte) 0xb4, (byte) 0xf3, (byte) 0xcf, (byte) 0xc3, 0x0a, (byte) 0xb5, (byte) 0xe7, (byte) 0xbb, (byte) 0xb0, (byte) 0xa3, (byte) 0xba, 0x31
				, 0x38, 0x37, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x0a
				, 0x1b, 0x40, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a, (byte) 0xb0, (byte) 0xd9, (byte) 0xb6
				, (byte) 0xc8, (byte) 0xb2, (byte) 0xe2, (byte) 0xca, (byte) 0xd4, (byte) 0xc9, (byte) 0xcc, (byte) 0xbb, (byte) 0xa7, 0x0a, 0x31, 0x38, 0x37, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d
				, 0x21, 0x00, 0x1b, 0x45, 0x00, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a
				, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a, 0x1b, 0x4d, 0x00, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x00, 0x1b, 0x45, 0x00
				, 0x1b, 0x47, 0x00, 0x1b, 0x61, 0x00, 0x1b, 0x61, 0x01, 0x23, 0x31, 0x35, 0x20, (byte) 0xb0, (byte) 0xd9, (byte) 0xb6, (byte) 0xc8, (byte) 0xcd, (byte) 0xe2, (byte) 0xc2, (byte) 0xf4, 0x20, 0x20, 0x31, 0x31, (byte) 0xd4, (byte) 0xc2, 0x30
				, 0x39, (byte) 0xc8, (byte) 0xd5, 0x20, 0x31, 0x37, 0x3a, 0x35, 0x30, 0x3a, 0x33, 0x30, 0x0a, 0x0a, 0x0a, 0x0a, 0x0a
		};
		return rv;
	}

	/**
	 * 美团小票(Meituan ticket for ESC cmd）
	 */
	public static byte[] getMeituanBill() {
		byte[] rv = new byte[]{
				0x1b, 0x40, 0x1b, 0x61, 0x01, 0x1d, 0x21, 0x11, (byte) 0xa3, (byte) 0xa3, 0x31, 0x20, 0x20, (byte) 0xc3, (byte) 0xc0, (byte) 0xcd, (byte) 0xc5, (byte) 0xb2, (byte) 0xe2, (byte) 0xca, (byte) 0xd4, 0x0a
				, 0x0a, 0x1d, 0x21, 0x00, (byte) 0xd4, (byte) 0xc1, (byte) 0xcf, (byte) 0xe3, (byte) 0xb8, (byte) 0xdb, (byte) 0xca, (byte) 0xbd, (byte) 0xc9, (byte) 0xd5, (byte) 0xc0, (byte) 0xb0, 0x28, (byte) 0xb5, (byte) 0xda, 0x31, (byte) 0xc1, (byte) 0xaa
				, 0x29, 0x0a, 0x1b, 0x21, 0x10, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x2a, 0x20, 0x2a, 0x20, 0x2a, 0x20
				, 0x2a, 0x20, 0x2a, 0x20, 0x2a, 0x20, 0x20, (byte) 0xd4, (byte) 0xa4, (byte) 0xb6, (byte) 0xa9, (byte) 0xb5, (byte) 0xa5, 0x20, 0x20, 0x2a, 0x20, 0x2a, 0x20, 0x2a, 0x20, 0x2a
				, 0x20, 0x2a, 0x20, 0x2a, 0x0a, (byte) 0xc6, (byte) 0xda, (byte) 0xcd, (byte) 0xfb, (byte) 0xcb, (byte) 0xcd, (byte) 0xb4, (byte) 0xef, (byte) 0xca, (byte) 0xb1, (byte) 0xbc, (byte) 0xe4, 0x3a, 0x20, 0x5b, 0x31, 0x38
				, 0x3a, 0x30, 0x30, 0x5d, 0x0a, 0x1d, 0x21, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1b, 0x61, 0x00
				, (byte) 0xcf, (byte) 0xc2, (byte) 0xb5, (byte) 0xa5, (byte) 0xca, (byte) 0xb1, (byte) 0xbc, (byte) 0xe4, 0x3a, 0x30, 0x31, 0x2d, 0x30, 0x31, 0x20, 0x31, 0x32, 0x3a, 0x30, 0x30, 0x0a, 0x1b
				, 0x21, 0x10, (byte) 0xb1, (byte) 0xb8, (byte) 0xd7, (byte) 0xa2, 0x3a, (byte) 0xb1, (byte) 0xf0, (byte) 0xcc, (byte) 0xab, (byte) 0xc0, (byte) 0xb1, 0x0a, 0x1d, 0x21, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, (byte) 0xb2, (byte) 0xcb, (byte) 0xc3, (byte) 0xfb, 0x09, 0x09, 0x20, 0x20, 0x20, (byte) 0xca, (byte) 0xfd, (byte) 0xc1, (byte) 0xbf, 0x09, 0x20, 0x20
				, 0x20, 0x20, (byte) 0xd0, (byte) 0xa1, (byte) 0xbc, (byte) 0xc6, 0x09, 0x0a, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1b, 0x21, 0x10
				, (byte) 0xba, (byte) 0xec, (byte) 0xc9, (byte) 0xd5, (byte) 0xc8, (byte) 0xe2, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x78
				, 0x31, 0x09, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x31, 0x32, 0x0a, 0x1d, 0x21, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x0a, (byte) 0xc5, (byte) 0xe4, (byte) 0xcb, (byte) 0xcd, (byte) 0xb7, (byte) 0xd1, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x35, 0x0a, (byte) 0xb2, (byte) 0xcd, (byte) 0xba, (byte) 0xd0, (byte) 0xb7, (byte) 0xd1, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x31, 0x0a, 0x5b, (byte) 0xb3, (byte) 0xac, (byte) 0xca, (byte) 0xb1, (byte) 0xc5, (byte) 0xe2, (byte) 0xb8, (byte) 0xb6, 0x5d, 0x20, 0x2d, (byte) 0xcf, (byte) 0xea, (byte) 0xbc, (byte) 0xfb, (byte) 0xb6, (byte) 0xa9, (byte) 0xb5
				, (byte) 0xa5, 0x0a, (byte) 0xbf, (byte) 0xc9, (byte) 0xbf, (byte) 0xda, (byte) 0xbf, (byte) 0xc9, (byte) 0xc0, (byte) 0xd6, 0x3a, 0x78, 0x31, 0x0a, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x0a, 0x1b, 0x21, 0x10, (byte) 0xba, (byte) 0xcf, (byte) 0xbc, (byte) 0xc6, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x31, 0x38, (byte) 0xd4, (byte) 0xaa, 0x0a, 0x1b, 0x40, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1d, 0x21, 0x11, (byte) 0xd5, (byte) 0xc5, 0x2a, 0x20, 0x31, 0x38, 0x33, 0x31, 0x32, 0x33, 0x34
				, 0x35, 0x36, 0x37, 0x38, 0x0a, (byte) 0xb5, (byte) 0xd8, (byte) 0xd6, (byte) 0xb7, (byte) 0xd0, (byte) 0xc5, (byte) 0xcf, (byte) 0xa2, 0x0a, 0x1d, 0x21, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x01, 0x1d, 0x21, 0x11, (byte) 0xa3, (byte) 0xa3, 0x31, 0x20, 0x20, (byte) 0xc3, (byte) 0xc0
				, (byte) 0xcd, (byte) 0xc5, (byte) 0xb2, (byte) 0xe2, (byte) 0xca, (byte) 0xd4, 0x0a, 0x1d, 0x21, 0x00, 0x1b, 0x40, 0x0a, 0x0a, 0x0a, 0x1d, 0x56, 0x00
		};
		return rv;
	}

	/**
	 * 饿了么小票(Eleme ticket for ESC cmd）
	 */
	public static byte[] getErlmoData() {
		byte[] rv = new byte[]{
				0x1b, 0x40, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x20, 0x1d, 0x21, 0x11, 0x23, 0x31, 0x1d, 0x21, 0x00, 0x00, (byte) 0xb6, (byte) 0xf6
				, (byte) 0xc1, (byte) 0xcb, (byte) 0xc3, (byte) 0xb4, (byte) 0xcd, (byte) (byte) 0xe2, (byte) 0xc2, (byte) 0xf4, (byte) 0xb5, (byte) 0xa5, 0x20, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a, 0x0a, 0x1b, 0x61
				, 0x01, (byte) 0xbf, (byte) 0xa8, (byte) 0xc8, (byte) 0xf8, (byte) 0xc5, (byte) 0xfb, (byte) 0xc8, (byte) 0xf8, 0x0a, 0x0a, 0x1b, 0x61, 0x00, 0x1b, 0x61, 0x01, 0x1d, 0x21, 0x11, 0x2d, 0x2d
				, (byte) 0xd2, (byte) 0xd1, (byte) 0xd6, (byte) 0xa7, (byte) 0xb8, (byte) 0xb6, 0x2d, 0x2d, 0x1d, 0x21, 0x00, 0x00, 0x0a, 0x0a, 0x1b, 0x61, 0x00, 0x1b, 0x61, 0x01, 0x1d, 0x21
				, 0x11, (byte) 0xd4, (byte) 0xa4, (byte) 0xbc, (byte) 0xc6, 0x31, 0x39, 0x3a, 0x30, 0x30, (byte) 0xcb, (byte) 0xcd, (byte) 0xb4, (byte) (byte) 0xef, 0x1d, 0x21, 0x00, 0x00, 0x0a, 0x0a, 0x1b, 0x61
				, 0x00, 0x5b, (byte) 0xcf, (byte) 0xc2, (byte) 0xb5, (byte) 0xa5, (byte) 0xca, (byte) 0xb1, (byte) 0xbc, (byte) (byte) 0xe4, 0x5d, 0x32, 0x30, 0x31, 0x34, 0x2d, 0x31, 0x32, 0x2d, 0x30, 0x33, 0x20
				, 0x31, 0x36, 0x3a, 0x32, 0x31, 0x0a, 0x5b, (byte) 0xb1, (byte) 0xb8, (byte) 0xd7, (byte) 0xa2, 0x5d, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) 0xbb, (byte) 0xb3, (byte) 0xd4, (byte) 0xc0, (byte) 0xb1, 0x20
				, (byte) 0xc0, (byte) 0xb1, (byte) 0xd2, (byte) 0xbb, (byte) 0xb5, (byte) (byte) 0xe3, 0x20, (byte) 0xb6, (byte) (byte) 0xe0, (byte) 0xbc, (byte) 0xd3, (byte) 0xc3, (byte) 0xd7, 0x20, (byte) 0xc3, (byte) 0xbb, (byte) 0xc1, (byte) (byte) 0xe3, (byte) 0xc7, (byte) 0xae, 0x1d, 0x21
				, 0x00, 0x0a, 0x5b, (byte) 0xb7, (byte) 0xa2, (byte) 0xc6, (byte) 0xb1, 0x5d, (byte) 0xd5, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xc7, (byte) 0xd2, (byte) 0xbb, (byte) 0xb8, (byte) 0xf6, (byte) 0xb7, (byte) 0xa2, (byte) 0xc6, (byte) 0xb1, (byte) 0xcc, (byte) 0xa7
				, (byte) 0xcd, (byte) 0xb7, 0x0a, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, (byte) 0xb2, (byte) 0xcb, (byte) 0xc3, (byte) 0xfb, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, (byte) 0xca, (byte) 0xfd, (byte) 0xc1, (byte) 0xbf, 0x20, 0x20, (byte) 0xd0, (byte) 0xa1
				, (byte) 0xbc, (byte) 0xc6, 0x0a, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x20, 0x31, (byte) 0xba, (byte) 0xc5, (byte) 0xc0, (byte) 0xba, (byte) 0xd7, (byte) 0xd3
				, 0x20, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, (byte) 0xc3, (byte) 0xc0
				, (byte) 0xca, (byte) 0xb3, (byte) 0xd2, (byte) 0xbb, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21
				, 0x01, 0x20, 0x78, 0x34, 0x1d, 0x21, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x34
				, 0x1d, 0x21, 0x00, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, (byte) 0xc3, (byte) 0xc0, (byte) 0xca, (byte) 0xb3, (byte) 0xb6, (byte) 0xfe, 0x1d, 0x21, 0x00, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x36, 0x1d, 0x21, 0x00, 0x1d, 0x21
				, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x36, 0x1d, 0x21, 0x00, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) (byte) 0xe2
				, (byte) 0xca, (byte) 0xd4, (byte) 0xc3, (byte) 0xc0, (byte) 0xca, (byte) 0xb3, (byte) 0xc8, (byte) 0xfd, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x32, 0x1d, 0x21, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x1d, 0x21, 0x01, 0x32, 0x1d, 0x21, 0x00, 0x0a, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x20, 0x32, (byte) 0xba
				, (byte) 0xc5, (byte) 0xc0, (byte) 0xba, (byte) 0xd7, (byte) 0xd3, 0x20, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xb2
				, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, 0x31, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x31, 0x1d, 0x21, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x1d, 0x21, 0x01, 0x31, 0x1d, 0x21, 0x00, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, 0x32, 0x1d, 0x21, 0x00, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x31
				, 0x1d, 0x21, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x31, 0x1d, 0x21, 0x00, 0x0a
				, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, 0x33, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x31, 0x1d, 0x21, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00
				, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x32, 0x33, 0x1d, 0x21, 0x00, 0x0a, 0x1d, 0x21, 0x01, 0x28, 0x2b, 0x29, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca
				, (byte) 0xd4, (byte) 0xd1, (byte) 0xf3, (byte) 0xc6, (byte) 0xf8, (byte) 0xa4, (byte) 0xce, (byte) 0xce, (byte) 0xf7, (byte) 0xca, (byte) 0xbd, (byte) 0xcc, (byte) 0xf0, (byte) 0xb5, (byte) (byte) 0xe3, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x1d, 0x21
				, 0x01, 0x20, 0x78, 0x31, 0x1d, 0x21, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x31
				, 0x1d, 0x21, 0x00, 0x0a, 0x1d, 0x21, 0x01, 0x28, 0x2b, 0x29, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, (byte) 0xcb, (byte) (byte) 0xe1, (byte) 0xc0, (byte) 0xb1, (byte) 0xc4, (byte) 0xbe, (byte) 0xb6, (byte) 0xfa
				, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x31, 0x1d, 0x21, 0x00, 0x1d, 0x21
				, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x38, 0x1d, 0x21, 0x00, 0x0a, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x20, 0x33, (byte) 0xba, (byte) 0xc5, (byte) 0xc0, (byte) 0xba, (byte) 0xd7, (byte) 0xd3, 0x20, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, (byte) 0xb2, (byte) 0xcb, (byte) 0xc6, (byte) 0xb7, (byte) 0xc3, (byte) 0xfb, (byte) 0xd7, (byte) 0xd6, (byte) 0xba, (byte) 0xdc
				, (byte) 0xb3, (byte) 0xa4, (byte) 0xba, (byte) 0xdc, (byte) 0xb3, (byte) 0xa4, (byte) 0xba, (byte) 0xdc, (byte) 0xb3, (byte) 0xa4, (byte) 0xba, (byte) 0xdc, (byte) 0xb3, (byte) 0xa4, (byte) 0xba, (byte) 0xdc, (byte) 0xb3, (byte) 0xa4, 0x1d, 0x21, 0x00, 0x0a
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x31, 0x1d, 0x21, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01
				, 0x33, 0x30, 0x30, 0x1d, 0x21, 0x00, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xb2, (byte) (byte) 0xe2, (byte) 0xca, (byte) 0xd4, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20
				, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x20, 0x78, 0x31, 0x1d, 0x21
				, 0x00, 0x1d, 0x21, 0x01, 0x1d, 0x21, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1d, 0x21, 0x01, 0x31, 0x1d, 0x21, 0x00, 0x0a, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x20, (byte) 0xc6, (byte) (byte) 0xe4, (byte) 0xcb, (byte) 0xfb, (byte) 0xb7, (byte) 0xd1, (byte) 0xd3, (byte) 0xc3, 0x20, 0x2d, 0x2d, 0x2d
				, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1d, 0x21, 0x01, (byte) 0xc5, (byte) (byte) 0xe4, (byte) 0xcb, (byte) 0xcd, (byte) 0xb7, (byte) 0xd1, 0x1d, 0x21, 0x00, 0x20
				, 0x20, 0x20, 0x20, 0x20
		};
		return rv;
	}

	/**
	 * 口碑小票(KouBei ticket for ESC cmd）
	 */
	public static byte[] getKoubeiData() {
		byte[] rv = new byte[]{
				0x1b, 0x40, 0x1b, 0x61, 0x01, 0x1d, 0x21, 0x11, 0x23, 0x34, (byte) 0xbf, (byte) 0xda, (byte) 0xb1, (byte) 0xae, (byte) 0xcd, (byte) 0xe2,
				(byte) 0xc2, (byte) 0xf4, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x01, 0x1d, 0x21, 0x11, 0x0a, 0x1b, 0x40, 0x1b, 0x61,
				0x01, (byte) 0xb7, (byte) 0xeb, (byte) 0xbc, (byte) 0xc7, (byte) 0xbb, (byte) 0xc6, (byte) 0xec, (byte) 0xcb, (byte) 0xbc, (byte) 0xa6, (byte) 0xc3, (byte) 0xd7, (byte) 0xb7, (byte) 0xb9, 0x0a,
				0x1b, 0x40, 0x1b, 0x61, 0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a,
				0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a,
				0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x11, 0x31, 0x37,
				0x3a, 0x32, 0x30, 0x20, (byte) 0xbe, (byte) 0xa1, (byte) 0xbf, (byte) 0xec, (byte) 0xcb, (byte) 0xcd, (byte) 0xb4, (byte) 0xef, 0x0a, 0x1b, 0x40, 0x1b,
				0x61, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x11, 0x31, 0x38, 0x36, 0x31, 0x30, 0x38,
				0x35, 0x38, 0x33, 0x33, 0x37, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x01, (byte) 0xce, (byte) 0xa4, (byte) 0xd0, (byte) 0xa1, (byte) 0xb1,
				(byte) 0xa6, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x11, (byte) 0xb4, (byte) 0xb4, (byte) 0xd6, (byte) 0xc7, (byte) 0xcc, (byte) 0xec,
				(byte) 0xb5, (byte) 0xd8, (byte) 0xb9, (byte) 0xe3, (byte) 0xb3, (byte) 0xa1, 0x37, (byte) 0xba, (byte) 0xc5, (byte) 0xc2, (byte) 0xa5, 0x28, 0x36, 0x30, 0x35, (byte) 0xca,
				(byte) 0xd2, 0x29, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, 0x1d, 0x21, 0x01,
				(byte) 0xcf, (byte) 0xc2, (byte) 0xb5, (byte) 0xa5, (byte) 0xa3, (byte) 0xba, 0x31, 0x36, 0x3a, 0x33, 0x35, 0x0a, 0x1b, 0x40, 0x1b, 0x61,
				0x00, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a,
				0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a, 0x2a,
				0x2a, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, (byte) 0xb2, (byte) 0xcb, (byte) 0xc6, (byte) 0xb7, 0x20, 0x20, 0x20, 0x20, 0x20,
				0x20, 0x20, 0x20, 0x20, 0x20, 0x1b, 0x61, 0x00, (byte) 0xca, (byte) 0xfd, (byte) 0xc1, (byte) 0xbf, 0x1b, 0x61, 0x00, 0x20,
				0x20, (byte) 0xb5, (byte) 0xa5, (byte) 0xbc, (byte) 0xdb, 0x1b, 0x61, 0x00, 0x20, 0x20, (byte) 0xbd, (byte) 0xf0, (byte) 0xb6, (byte) 0xee, 0x0a, 0x1b,
				0x40, 0x1b, 0x61, 0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, (byte) 0xbb, (byte) 0xc6, (byte) 0xec, (byte) 0xcb, (byte) 0xce, (byte) 0xe5, (byte) 0xbb,
				(byte) 0xa8, (byte) 0xc8, (byte) 0xe2, (byte) 0xb7, (byte) 0xb9, (byte) 0xa3, (byte) 0xa8, (byte) 0xb4, (byte) 0xf3, (byte) 0xa3, (byte) 0xa9, 0x28, (byte) 0xb2, (byte) 0xbb, (byte) 0xc0, (byte) 0xb1,
				0x29, 0x0a, 0x1b, 0x40, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
				0x20, 0x20, 0x1b, 0x61, 0x00, 0x20, 0x31, 0x20, 0x20, 0x1b, 0x61, 0x00, 0x20, 0x20, 0x20, 0x20,
				0x32, 0x35, 0x1b, 0x61, 0x00, 0x20, 0x20, 0x20, 0x20, 0x32, 0x35, 0x0a, 0x1b, 0x40, 0x1b, 0x61,
				0x00, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, (byte) 0xc5, (byte) 0xe4, (byte) 0xcb, (byte) 0xcd, (byte) 0xb7, (byte) 0xd1, 0x20, 0x20, 0x20, 0x20,
				0x20, 0x20, 0x20, 0x20, 0x1b, 0x61, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
				0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x32, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x00, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
				0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0a, 0x1b, 0x40, 0x1b,
				0x61, 0x00, 0x1d, 0x21, 0x01, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
				0x20, 0x20, 0x20, 0x20, 0x20, (byte) 0xca, (byte) 0xb5, (byte) 0xb8, (byte) 0xb6, (byte) 0xbd, (byte) 0xf0, (byte) 0xb6, (byte) 0xee, (byte) 0xa3, (byte) 0xba, (byte) 0xa3,
				(byte) 0xa4, 0x32, 0x37, 0x0a, 0x1b, 0x40, 0x0a, 0x1b, 0x40, 0x0a, 0x1b, 0x40, 0x1b, 0x61, 0x01, 0x1d,
				0x21, 0x11, (byte) 0xbf, (byte) 0xda, (byte) 0xb1, (byte) 0xae, (byte) 0xcd, (byte) 0xe2, (byte) 0xc2, (byte) 0xf4, 0x0a, 0x1b, 0x40, 0x0a, 0x1b, 0x40,
				0x1d, 0x56, 0x42, 0x0a, 0x0a
		};
		return rv;
	}

	/**
	 *	Thermal receipt printer esc cmd set
	 */
	public static byte[] customData(){
		byte[] rv = new byte[]{
				(byte) 0xB4, (byte) 0xF2, (byte) 0xD3, (byte) 0xA1, (byte) 0xBB, (byte) 0xFA, (byte) 0xD7, (byte) 0xD4, (byte) 0xBC, (byte) 0xEC, 0x0A,
				//打印机自检（printer selfcheck）
				0x1F, 0X1B, 0x1F, 0x53,
				//初始化打印机 （printer init）
				0x1B, 0x40,
				//分割线---	（Dividing line）
				0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D, 0x0A,
				(byte) 0xB8, (byte) 0xC4, (byte) 0xB1, (byte) 0xE4, (byte) 0xD7, (byte) 0xD6, (byte) 0xBC, (byte) 0xE4, (byte) 0xBE, (byte) 0xE0,0x0A,
				0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x0A,
				//设置字符右间距 （Set character right margin）
				0x30,0x2E, 0x30, 0x3A,(byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, 0x0A,
				0x30,0x2E, 0x35, 0x3A, 0x1B, 0x20, 0x0C,(byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, 0x0A,
				0x1B, 0x20, 0x00,
				0x31,0x2E, 0x30, 0x3A, 0x1B, 0x20, 0x18, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, 0x0A,
				0x1B, 0x20, 0x00,
				0x32,0x2E, 0x30, 0x3A, 0x1B, 0x20, 0x30, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, (byte) 0xB2, (byte) 0xE2, (byte) 0xCA, (byte) 0xD4, 0x0A,
				0x1B, 0x20, 0x00,
				//分割线===	（Dividing line）
				0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D, 0x0A,
				(byte) 0xD7, (byte) 0xD6, (byte) 0xCC, (byte) 0xE5, (byte) 0xD0, (byte) 0xA7, (byte) 0xB9, (byte) 0xFB, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D, 0x0A,
				//字体效果 商米科技
				//设置加粗、倍高、倍宽、下划线、反白 （Set bold, double height, double width, underline, highlight）
				0x1B, 0x21, 0x08, (byte) 0xBC, (byte) 0xD3, (byte) 0xB4, (byte) 0xD6, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1B, 0x21, 0x00,
				0x1B, 0x45, 0x01, (byte) 0xBC, (byte) 0xD3, (byte) 0xB4, (byte) 0xD6, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1B, 0x45, 0x00,
				0x1B, 0x21, 0x10, (byte) 0xB1, (byte) 0xB6, (byte) 0xB8, (byte) 0xDF, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1B, 0x21, 0x20, (byte) 0xB1, (byte) 0xB6, (byte) 0xBF, (byte) 0xED, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1D, 0x21, 0x11, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC,
				0x1D, 0x21, 0x22, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1D, 0x21, 0x00,
				0x1B, 0x21, (byte) 0x80, (byte) 0xCF, (byte) 0xC2, (byte) 0xBB, (byte) 0xAE, (byte) 0xCF, (byte) 0xDF, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1B, 0x21, 0x00,
				0x1B, 0x2D, 0x01, (byte) 0xCF, (byte) 0xC2, (byte) 0xBB, (byte) 0xAE, (byte) 0xCF, (byte) 0xDF, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1B, 0x2D, 0x00,
				0x1D, 0x42, 0x01,0x1B, 0x21, 0x08, (byte) 0xB7, (byte) 0xB4, (byte) 0xB0, (byte) 0xD7, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC, 0x0A,
				0x1D, 0x42, 0x00,0x1B, 0x21, 0x00,
				//分割线***	（Dividing line）
				0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A, 0x0A,
				(byte) 0xC5, (byte) 0xC5, (byte) 0xB0, (byte) 0xE6, (byte) 0xCE, (byte) 0xBB, (byte) 0xD6, (byte) 0xC3, 0x0A,
				0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A, 0x0A,
				//排版位置
				//设置绝对位置和设置相对位置	（Absolute and relative positions）
				(byte) 0xD5, (byte) 0xE2, (byte) 0xBE, (byte) 0xE4, (byte) 0xBB, (byte) 0xB0, (byte) 0xB4, (byte) 0xD3, (byte) 0xD0, (byte) 0xD0, (byte) 0xCA, (byte) 0xD7, (byte) 0xBF, (byte) 0xAA, (byte) 0xCA, (byte) 0xBC, 0x0A,
				(byte) 0xBE, (byte) 0xF8, (byte) 0xB6, (byte) 0xD4, (byte) 0xCE, (byte) 0xBB, (byte) 0xD6, (byte) 0xC3, 0x1B, 0x24, (byte) 0xC0, 0x00, (byte) 0xC6, (byte) 0xAB, (byte) 0xD2, (byte) 0xC6, 0x38, (byte) 0xB8, (byte) 0xF6, (byte) 0xD7, (byte) 0xD6, 0x0A,
				(byte) 0xCF, (byte) 0xE0, (byte) 0xB6, (byte) 0xD4, (byte) 0xCE, (byte) 0xBB, (byte) 0xD6, (byte) 0xC3, 0x1B, 0x5C, 0x30, 0x00, (byte) 0xC6, (byte) 0xAB, (byte) 0xD2, (byte) 0xC6, 0x32, (byte) 0xB8, (byte) 0xF6, (byte) 0xD7, (byte) 0xD6, 0x0A,
				//设置对齐方式	（align)
				(byte) 0xBE, (byte) 0xD3, (byte) 0xD7, (byte) 0xF3, 0x0A,
				0x1B, 0x61, 0x01, (byte) 0xBE, (byte) 0xD3, (byte) 0xD6, (byte) 0xD0, 0x0A,
				0x1B, 0x61, 0x02, (byte) 0xBE, (byte) 0xD3, (byte) 0xD3, (byte) 0xD2, 0x0A,
				0x1B, 0x61, 0x00,
				//设置左边距 （Left margin）
				0x1D, 0x4C, 0x30, 0x00,
				(byte) 0xC9, (byte) 0xE8, (byte) 0xD6, (byte) 0xC3, (byte) 0xD7, (byte) 0xF3, (byte) 0xB1, (byte) 0xDF, (byte) 0xBE, (byte) 0xE0, 0x34, 0x38, (byte) 0xCF, (byte) 0xF1, (byte) 0xCB, (byte) 0xD8, 0x0A,
				//设置打印区域宽度	（Printing area width)
				0x1D, 0x57, (byte) 0xF0, 0x00,
				(byte) 0xB8, (byte) 0xC4, (byte) 0xB1, (byte) 0xE4, (byte) 0xBF, (byte) 0xC9, (byte) 0xB4, (byte) 0xF2, (byte) 0xD3, (byte) 0xA1, (byte) 0xC7, (byte) 0xF8, (byte) 0xD3, (byte) 0xF2, (byte) 0xCE, (byte) 0xAA, 0x32, 0x34, 0x30, (byte) 0xCF, (byte) 0xF1, (byte) 0xCB, (byte) 0xD8, 0x0A,
				//设置左边距（Left margin）
				0x1D, 0x4C, 0x60, 0x00,
				(byte) 0xC9, (byte) 0xE8, (byte) 0xD6, (byte) 0xC3, (byte) 0xD7, (byte) 0xF3, (byte) 0xB1, (byte) 0xDF, (byte) 0xBE, (byte) 0xE0, 0x39, 0x36, (byte) 0xCF, (byte) 0xF1, (byte) 0xCB, (byte) 0xD8, 0x0A,
				//设置打印区域宽度	（Printing area width)
				0x1D, 0x57, (byte) 0x78, 0x00,
				(byte) 0xB8, (byte) 0xC4, (byte) 0xB1, (byte) 0xE4, (byte) 0xBF, (byte) 0xC9, (byte) 0xB4, (byte) 0xF2, (byte) 0xD3, (byte) 0xA1, (byte) 0xC7, (byte) 0xF8, (byte) 0xD3, (byte) 0xF2, (byte) 0xCE, (byte) 0xAA, 0x31, 0x32, 0x30, (byte) 0xCF, (byte) 0xF1, (byte) 0xCB, (byte) 0xD8, 0x0A,
				0x1D, 0x4C, 0x00, 0x00,
				0x1D, 0x57, (byte) 0x80, 0x01,
				//水平制表符-跳格
				(byte) 0xC4, (byte) 0xAC, 0x09, (byte) 0xC8, (byte) 0xCF, 0x09, (byte) 0xCC, (byte) 0xF8, 0x09, (byte) 0xB8, (byte) 0xF1, 0x09, 0x0A,
				0x1B, 0x44, 0x01, 0x02, 0x04, 0x08, 0x0A, 0x00,
				(byte) 0xD7, (byte) 0xD4, 0x09, (byte) 0xB6, (byte) 0xA8, 0x09, (byte) 0xD2, (byte) 0xE5, 0x09, (byte) 0xCC, (byte) 0xF8, 0x09, (byte) 0xB8, (byte) 0xF1, 0x09, 0x0A,
				//设置行高 (line height)
				0x1B, 0x33, 0x60, (byte) 0xC9, (byte) 0xE8, (byte) 0xD6, (byte) 0xC3, (byte) 0xD0, (byte) 0xD0, (byte) 0xB8, (byte) 0xDF, 0x3A, 0x39, 0x36, (byte) 0xB5, (byte) 0xE3, (byte) 0xD0, (byte) 0xD0, 0x0A,
				0x1B, 0x33, 0x40, (byte) 0xC9, (byte) 0xE8, (byte) 0xD6, (byte) 0xC3, (byte) 0xD0, (byte) 0xD0, (byte) 0xB8, (byte) 0xDF, 0x3A, 0x36, 0x34, (byte) 0xB5, (byte) 0xE3, (byte) 0xD0, (byte) 0xD0, 0x0A,
				0x1B, 0x33, 0x00, (byte) 0xC9, (byte) 0xE8, (byte) 0xD6, (byte) 0xC3, (byte) 0xD0, (byte) 0xD0, (byte) 0xB8, (byte) 0xDF, 0x3A, 0x30, (byte) 0xB5, (byte) 0xE3, (byte) 0xD0, (byte) 0xD0, 0x0A,
				0x1B, 0x32, (byte) 0xC4, (byte) 0xAC, (byte) 0xC8, (byte) 0xCF, (byte) 0xD0, (byte) 0xD0, (byte) 0xB8, (byte) 0xDF, 0x0A,
				//分割线--- （Dividing line）
				0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D, 0x0A,
				(byte) 0xD6, (byte) 0xAE, (byte) 0xBA, (byte) 0xF3, (byte) 0xBD, (byte) 0xAB, (byte) 0xD7, (byte) 0xDF, (byte) 0xD6, (byte) 0xBD, 0x0A,
				0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D, 0x0A,
				//打印并走纸 (printing and feed paper)
				0x1B, 0x4A, 0x40, (byte) 0xD7, (byte) 0xDF, (byte) 0xD6, (byte) 0xBD, 0x36, 0x34, (byte) 0xB5, (byte) 0xE3, (byte) 0xD0, (byte) 0xD0, 0x0A,
				0x1B, 0x4A, 0x60, (byte) 0xD7, (byte) 0xDF, (byte) 0xD6, (byte) 0xBD, 0x39, 0x36, (byte) 0xB5, (byte) 0xE3, (byte) 0xD0, (byte) 0xD0, 0x0A,
				0x1B, 0x64, 0x0A, (byte) 0xD7, (byte) 0xDF, (byte) 0xD6, (byte) 0xBD, 0x31, 0x30, (byte) 0xD0, (byte) 0xD0, 0x0A,
				0x1B, 0x64, 0x01, (byte) 0xD7, (byte) 0xDF, (byte) 0xD6, (byte) 0xBD, 0x31, (byte) 0xD0, (byte) 0xD0, 0x0A,
				//分割线===（Dividing line）
				0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D, 0x0A,
				(byte) 0xB4, (byte) 0xF2, (byte) 0xD3, (byte) 0xA1, (byte) 0xCC, (byte) 0xF5, (byte) 0xC2, (byte) 0xEB, 0x0A,
				0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D, 0x0A,
				//打印条码	(barcode printing)
				0x1D, 0x48, 0x02,
				//upca
				0x1B, 0x61, 0x00, 0x1D, 0x68, 0x20, 0x1D, 0x77, 0x02,
				0x1D, 0x6B, 0x41, 0x0c, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x30, 0x31, 0x32, 0x0A,
				0x1D, 0x6B, 0x00, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x30, 0x31, 0x32, 0x00, 0x0A,
				//upce
				0x1B, 0x61, 0x01, 0x1D, 0x68, 0x40, 0x1D, 0x77, 0x04,
				0x1D, 0x6B, 0x42, 0x08, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x35, 0x0A,
				0x1D, 0x6B, 0x01, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x35, 0x00, 0x0A,
				//ean13
				0x1B, 0x61, 0x02, 0x1D, 0x68, 0x60, 0x1D, 0x77, 0x02,
				0x1D, 0x6B, 0x43, 0x0D, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x30, 0x31, 0x32, 0x0A,
				0x1D, 0x6B, 0x02, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x30, 0x31, 0x32, 0x00, 0x0A,
				//ean8
				0x1B, 0x61, 0x00, 0x1D, 0x68, (byte) 0x80, 0x1D, 0x77, 0x05,
				0x1D, 0x6B, 0x44, 0x08, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x35, 0x0A,
				0x1D, 0x6B, 0x03, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x35, 0x00, 0x0A,
				//code39
				0x1B, 0x61, 0x01, 0x1D, 0x68, (byte) 0xA0, 0x1D, 0x77, 0x02,
				0x1D, 0x6B, 0x45, 0x0A, 0x33, 0x36, 0x39, 0x53, 0x55, 0x4E, 0x4D, 0x49, 0x25, 0x24, 0x0A,
				0x1D, 0x6B, 0x04, 0x33, 0x36, 0x39, 0x53, 0x55, 0x4E, 0x4D, 0x49, 0x25, 0x24, 0x00, 0x0A,
				//itf
				0x1B, 0x61, 0x02, 0x1D, 0x68, (byte) 0xC0, 0x1D, 0x77, 0x03,
				0x1D, 0x6B, 0x46, 0x0C, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x30, 0x31, 0x0A,
				0x1D, 0x6B, 0x05, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x30, 0x31, 0x00, 0x0A,
				//codebar
				0x1B, 0x61, 0x00, 0x1D, 0x68, (byte) 0xE0, 0x1D, 0x77, 0x03,
				0x1D, 0x6B, 0x47, 0x0A, 0x41, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x41, 0x0A,
				//0x1D, 0x6B, 0x06, 0x41, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x41, 0x00, 0x0A,
				//code93
				0x1B, 0x61, 0x01, 0x1D, 0x68, (byte) 0xFF, 0x1D, 0x77, 0x02,
				0x1D, 0x6B, 0x48, 0x0C, 0x53, 0x55, 0x4E, 0x4D, 0x49, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x0A,
				//code128
				0x1B, 0x61, 0x00, 0x1D, 0x68, (byte) 0xB0, 0x1D, 0x77, 0x02,
				0x1D, 0x6B, 0x49, 0x0A, 0x7B, 0x41, 0x53, 0x55, 0x4E, 0x4D, 0x49, 0x30, 0x31, 0x32, 0x0A,
				0x1D, 0x6B, 0x49, 0x0C, 0x7B, 0x42, 0x53, 0x55, 0x4E, 0x4D, 0x49, 0x73, 0x75, 0x6E, 0x6D, 0x69, 0x0A,
				0x1D, 0x6B, 0x49, 0x0B, 0x7B, 0x43, 0x01, 0xc, 0x17, 0x22, 0x2d, 0x38, 0x4e, 0x59, 0x5a, 0x0A,
				//分割线***
				0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A, 0x0A,
				(byte) 0xB4, (byte) 0xF2, (byte) 0xD3, (byte) 0xA1, (byte) 0xB6, (byte) 0xFE, (byte) 0xCE, (byte) 0xAC, (byte) 0xC2, (byte) 0xEB, 0x0A,
				0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A,0x2A, 0x2A, 0x2A, 0x2A, 0x0A,
				//打印二维码	(qrcode printing)
				0x1B, 0x61, 0x01,
				0x1D, 0x28, 0x6B, 0x03, 0x00, 0x31, 0x43, 0x09,
				0x1D, 0x28, 0x6B, 0x03, 0x00, 0x31, 0x45, 0x32,
				0x1D, 0x28, 0x6B, 0x0B, 0x00, 0x31, 0x50, 0x30, (byte) 0xC9, (byte) 0xCC, (byte) 0xC3, (byte) 0xD7, (byte) 0xBF, (byte) 0xC6, (byte) 0xBC, (byte) 0xBC,
				0x1D, 0x28, 0x6B, 0x03, 0x00, 0x31, 0x51, 0x30, 0x0A,
				0x1B, 0x61, 0x00,
				//分割线---	  （之后将实现打印光栅位图的方法，再次之前使用1b 61 01 居中）
				0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D, 0x0A,
				(byte) 0xB4, (byte) 0xF2, (byte) 0xD3, (byte) 0xA1, (byte) 0xB9, (byte) 0xE2, (byte) 0xD5, (byte) 0xA4, (byte) 0xCD, (byte) 0xBC, (byte) 0xCF, (byte) 0xF1, 0x0A,
				0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D,0x2D, 0x2D,0x2D,0x2D,0x2D,0x2D, 0x0A,
				0x1B, 0x61, 0x01,
		};
		return rv;
	}

	//部分字符打印的分割线标志
	public static byte[] wordData(){
		byte rv[] = new byte[]{
				//分割线===
				0x1B, 0x61, 0x00,
				0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D, 0x0A,
				(byte) 0xD7, (byte) 0xD6, (byte) 0xB7, (byte) 0xFB, (byte) 0xBC, (byte) 0xAF, (byte) 0xC9, (byte) 0xE8, (byte) 0xD6, (byte) 0xC3, 0x0A,
				0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D,0x3D, 0x3D, 0x3D, 0x3D, 0x0A,
				0x1C, 0x26, 0x1C, 0x43, 0x00,
		};
		return rv;
	}
}



