package com.turinggear.ssa_hand.sunmi

import com.turinggear.ssa_hand.BuildConfig
import com.turinggear.ssa_hand.DEFAULT_RULES_TICKET
import com.turinggear.ssa_hand.R
import com.turinggear.ssa_hand.RECEIPT_TITLE
import com.turinggear.ssa_shared.CATEGORY_BUS
import com.turinggear.ssa_shared.CATEGORY_TRAIN
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.GOOD_CATEGORY_MAP
import com.turinggear.ssa_shared.PAY_PLATFORM_MAP
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.Order
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.util.TicketQrcode
import com.turinggear.ssa_shared.util.formatTicketQrcode

object SunmiPrinterManager {

    fun print(qrcodeFormat: Int, ticket: Ticket, order: Order? = null, good: Good? = null, header: String ="") {

        var goodId = good?.id
        var goodName = good?.name
        var goodPrice = good?.price
        var goodCategory = good?.category
        var orderNo = order?.order_no
        ticket.goods?.let {
            goodId = it.id
            goodName = it.name
            goodPrice = it.price
            goodCategory = it.category
        }
        ticket.order?.let {
            orderNo = it.order_no
        }

        SunmiPrintHelper.getInstance().setAlign(1)
        if (header != "") {
            SunmiPrintHelper.getInstance()
                .printText("${header} \n", 37f, false, false, null)
        }
        SunmiPrintHelper.getInstance()
            .printText("$RECEIPT_TITLE\n", 24f, false, false, null)
        ticket.order?.pay_platform?.let {
            val value = PAY_PLATFORM_MAP.getOrDefault(it, "-")
            SunmiPrintHelper.getInstance().printText("支付方式: ${value}\n", 22f, false, false, null)
        }
        var qrcode = formatTicketQrcode(goodId, orderNo, ticket, qrcodeFormat)

        // 3 为最高的纠错级别
        //
        //modulesize ! QR码块⼤⼩，单位:点, 取值 4 ⾄ 16。
        //errorlevel ! ⼆维码纠错等级(0 - 3)：
        //0 ! 纠错级别 L ( 7%)
        //1 ! 纠错级别 M (15%)
        //2 ! 纠错级别 Q (25%)
        //3 ! 纠错级别 H (30%)
        SunmiPrintHelper.getInstance().printQr(qrcode, 9, 3)
        SunmiPrintHelper.getInstance()
            .printText("\n", 36f, true, false, null)
        if (goodCategory in arrayOf(CATEGORY_BUS, CATEGORY_TRAIN)) {
            goodName?.let {
                if (!it.contains("单")) {
                    SunmiPrintHelper.getInstance()
                        .printText("[在自助售票机扫码叫车]\n\n", 24f, false, false, null)
                }
            }
        }
        val category = GOOD_CATEGORY_MAP[goodCategory] ?: ""
        SunmiPrintHelper.getInstance().printText("$category\n", 30f, false, false, null)
        SunmiPrintHelper.getInstance()
            .printText("$goodName\n", 38f, true, true, null)
        SunmiPrintHelper.getInstance().setAlign(0)
        SunmiPrintHelper.getInstance()
            .printText("------------------------------------------------\n", 16f, false, false, null)
        SunmiPrintHelper.getInstance()
            .printText("单价:${goodPrice}元\n编号:${ticket.ticket_no}\n购票时间:${ticket.created_at}\n", 24f, false, false, null)
        SunmiPrintHelper.getInstance()
            .printText("------------------------------------------------\n", 16f, false, false, null)
        val ticketRules = GLOBAL_POLL?.data?.machineStaticConfig?.ticket_rules ?: DEFAULT_RULES_TICKET ?: ""
        SunmiPrintHelper.getInstance()
            .printText(ticketRules, 22f, false, false, null)
        SunmiPrintHelper.getInstance()
            .printText("\n------------------------------------------------\n", 16f, false, false, null)
        SunmiPrintHelper.getInstance().print3Line()
    }
}
