package com.turinggear.ssa_hand

import android.annotation.SuppressLint
import android.content.Context
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.annotation.RequiresApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.turinggear.ssa_hand.sunmi.SunmiPrintHelper
import com.turinggear.ssa_hand.ui.*
import com.turinggear.ssa_hand.ui.theme.HandssaTheme
import com.turinggear.ssa_hand.util.AppReceiver
import com.turinggear.ssa_hand.util.RequestManager
import com.turinggear.ssa_hand.util.UserPermissionUtil
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.model.Good
import com.turinggear.ssa_shared.model.Ticket
import com.turinggear.ssa_shared.model.UNCERTAIN_MAX_USES
import com.turinggear.ssa_shared.ui.RegisterAlertDialog
import com.turinggear.ssa_shared.util.TicketQrcode
import com.turinggear.ssa_shared.util.TicketQrcodeParser
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import android.app.AlertDialog
import android.widget.Toast.LENGTH_SHORT
import com.turinggear.ssa_shared.util.UpgradeUtil
import com.turinggear.ssa_shared.util.cleanScannedQrcode
import com.turinggear.ssa_shared.R

@Suppress("DEPRECATION")
class MainActivity : ComponentActivity() {

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ApplicationContext = this.applicationContext
        API_BASE = this.applicationContext?.getString(R.string.api_endpoint) ?: ""
        SCAN_TYPE = SCANTYPE.CHECK_TICKET

        val appReceiver = AppReceiver(this, lifecycleScope)
        // 监控网络连接情况
        application.registerReceiver(appReceiver, IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION))

        if (!HasTriedToRegisterAfterLaunched) {
            lifecycleScope.launch {
                RequestManager.registerDevice()
            }
        }

        SunmiPrintHelper.getInstance().initSunmiPrinterService(this)
        val sharedPref = this.getSharedPreferences(BuildConfig.APPLICATION_ID, Context.MODE_PRIVATE)
        sharedPref.getString("ACCESS_TOKEN", "")?.let {
            ACCESS_TOKEN = it
        }

        setContent {
            HandssaTheme {
                Box {
                    if (ACCESS_TOKEN.isNotEmpty()) {
                        MainView()
                        LaunchedEffect(Unit) {
                            lifecycleScope.launch {
                                var appVersion = STARTUP?.data?.appVersion
                                if (appVersion == null) {
                                    appVersion = RequestManager.startup(BuildConfig.VERSION_CODE)
                                }

                                Log.e("XXX", "appVersion: $appVersion")
                                appVersion?.let {
                                    // 弹出对话框询问是否现在更新
                                    AlertDialog.Builder(this@MainActivity)
                                        .setTitle("更新提示")
                                        .setMessage("注意: 有新版本(${it.code}), 是否现在更新?")
                                        .setPositiveButton("更新") { _, _ ->
                                            val message = "开始到版本(${appVersion.code})的更新"
                                            Toast.makeText(this@MainActivity, message, Toast.LENGTH_LONG).show()

                                            lifecycleScope.launch {
                                                val url = appVersion.down_url
//                                                UpgradeManager.downloadAndUpgrade(
//                                                    this@MainActivity,
//                                                    url,
//                                                    isSilent = false
//                                                )
                                                val ctx = this@MainActivity
                                                var ret = false
                                                try {
                                                    ret = UpgradeUtil.download(ctx, ctx.packageName, url)
                                                    Log.e("XXX", "downloaded")
                                                    if (ret) {
                                                        ret = UpgradeUtil.installInteractively(ctx, ctx.packageName)
                                                    }
                                                } catch (e: Exception) {
                                                    Log.e("XXX", "error ${e.localizedMessage}")
                                                    Toast.makeText(ctx, e.localizedMessage, LENGTH_SHORT).show()

                                                    ret = false
                                                }
                                                Log.e("XXX", "download result $ret")
                                                if (ret) {
                                                    Toast.makeText(ctx, "更新成功", LENGTH_SHORT).show()
                                                }
                                            }
                                        }
                                        .setNegativeButton("暂不更新", null)
                                        .show()
                                }
                            }


                            while (true) {
                                GLOBAL_POLL = RequestManager.poll()
                                StatusCodeFromBaidu = RequestManager.requestInternet()
                                StatusCodeFromHealthApi = RequestManager.requestHealthApi()
                                delay(10_000)
                            }
                        }
                    } else {
                        LoginView()
                    }
                    if (SHOW_ALERT_FOR_REGISTER) {
                        RegisterAlertDialog(
                            code = RANDOM_REGISTER_CODE,
                            error = REGISTER_ERROR_STRING
                        )
                    }
                }
            }
        }
    }

    // 监控扫码
    private var sequenceString = ""

    @SuppressLint("RestrictedApi")
    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.action != KeyEvent.ACTION_UP) {
            sequenceString += event.unicodeChar.toChar()
            return if (SHOW_POPUP or ACCESS_TOKEN.isNotEmpty()) {
                true // 不触发屏幕按键
            } else {
                super.dispatchKeyEvent(event)
            }
        }
        if (event.keyCode == KeyEvent.KEYCODE_ENTER) {
            // 异常情况
//            BARCODE raw:????1.2024020313183619492499783.82
//            BARCODE raw:0000312e323032343032303331333138333631393439323439393738332e38320a
            Log.e("XXX", "BARCODE raw:" + sequenceString)
            Log.e("XXX", "BARCODE raw:" + sequenceString.toByteArray().joinToString("") { "%02x".format(it) })

            val processed = cleanScannedQrcode(sequenceString)

            Log.e("XXX", "BARCODE processed:" + processed)

            // 比如二维码尾部带多个换行会多次触发, 这里过滤掉
            if (processed.length == 0) {
                return true
            }
            // 非空 才修改状态
            BARCODE = processed
            sequenceString = ""

            // FOR 验票
            if (BARCODE.isNotEmpty() and !SHOW_POPUP and ACCESS_TOKEN.isNotEmpty() and (SCAN_TYPE == SCANTYPE.CHECK_TICKET)) {
                lifecycleScope.launch {
                    val parsed = TicketQrcodeParser.parse(BARCODE)
                    val msg = if (parsed?.format == TicketQrcode.FORMAT_V2) {
                        RequestManager.queryTicketDetail(BARCODE)
                    } else {
                        RequestManager.verifyTicketWith(BARCODE)
                    }
                    msg?.let {
                        Toast.makeText(
                            applicationContext,
                            it,
                            Toast.LENGTH_LONG
                        ).show()
                    }
                }
                return true // 不触发屏幕按键
            }
            // FOR 微信支付
            if (BARCODE.isNotEmpty()
                and ACCESS_TOKEN.isNotEmpty()
                and (SCAN_TYPE == SCANTYPE.WECHAT_PAY)
                and ((PAYMENT_STATE == PAYMENTSTATE.UNSTARTED) or (PAYMENT_STATE == PAYMENTSTATE.USER_TAPPED) or (PAYMENT_STATE == PAYMENTSTATE.FAILED))
            ) {
                val pay_playform = this.applicationContext?.resources?.getInteger(R.integer.pay_platform) ?: 10
                lifecycleScope.launch {
                    SELECTED_GOOD?.let {
                        RequestManager.wechatPayWith(this@MainActivity, BARCODE, it.id, SELECTED_AMOUNT, pay_playform)
                    }
                }
                return true // 不触发屏幕按键
            }
            // FOR 补打小票
            if (BARCODE.isNotEmpty()
                and ACCESS_TOKEN.isNotEmpty()
                and (SCAN_TYPE == SCANTYPE.REPRINT_TICKET)
            ) {
                lifecycleScope.launch {
                    RequestManager.requestTicketsWith(orderNo = BARCODE)
                }
                return true // 不触发屏幕按键
            }
        }
        return super.dispatchKeyEvent(event)
    }
}

@RequiresApi(Build.VERSION_CODES.O)
@Composable
fun MainView() {
    val navController = rememberNavController()
    Box(
        Modifier
            .fillMaxSize()
            .background(MaterialTheme.colors.background),
        contentAlignment = Alignment.Center
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        Column {
                            val mobileTitle = GLOBAL_POLL?.data?.machineStaticConfig?.mobile_title ?: APP_TITLE ?: ""
                            Text(
                                mobileTitle,
                                style = MaterialTheme.typography.subtitle1.copy(fontWeight = FontWeight.Bold)
                            )
                            Text(
                                "Ver ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                                color = MaterialTheme.colors.onPrimary.copy(0.7f),
                                style = MaterialTheme.typography.overline.copy(fontWeight = FontWeight.Medium)
                            )
                        }
                    },
                    actions = {
                        IconButton(onClick = { }, enabled = false) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                var networkingText = ""
                                var networkingIcon = Icons.Filled.Wifi
                                var networkingColor = MaterialTheme.colors.onPrimary
                                if (LocalNetworkConnected == false) {
                                    networkingText = "设备未联网"
                                    networkingIcon = Icons.Filled.WifiOff
                                    networkingColor = Color.Yellow
                                }
                                else if ((StatusCodeFromBaidu != null) and !StatusCodeFromBaidu.toString().startsWith("2")) {
                                    networkingText = "网络连接异常"
                                    networkingIcon = Icons.Filled.WifiOff
                                    networkingColor = Color.Yellow
                                }
                                else if ((StatusCodeFromHealthApi != null) and !StatusCodeFromHealthApi.toString().startsWith("2")) {
                                    networkingText = "服务接口异常"
                                    networkingIcon = Icons.Filled.WifiOff
                                    networkingColor = Color.Yellow
                                }
                                Text(
                                    text = networkingText,
                                    color = networkingColor,
                                    style = MaterialTheme.typography.subtitle2
                                )
                                Spacer(Modifier.width(6.dp))
                                Icon(
                                    networkingIcon,
                                    contentDescription = null,
                                    modifier = Modifier.size(22.dp),
                                    tint = networkingColor
                                )
                                Spacer(Modifier.width(12.dp))
                            }
                        }
                    }
                )
            },
            bottomBar = {
                BottomNavigation {
                    val navBackStackEntry by navController.currentBackStackEntryAsState()
                    val currentDestination = navBackStackEntry?.destination
                    if (UserPermissionUtil.hasPermission(PERM_TICKET_CHECK, CURRENT_USER?.permissions)) {
                        BottomNavigationItem(
                            icon = {
                                Icon(
                                    Icons.Filled.QrCodeScanner,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp)
                                )
                            },
                            label = { Text(text = "验票") },
                            selected = currentDestination?.hierarchy?.any { it.route == "check" } == true,
                            unselectedContentColor = LocalContentColor.current.copy(alpha = ContentAlpha.disabled),
                            onClick = {
                                SCAN_TYPE = SCANTYPE.CHECK_TICKET
                                navController.navigate("check") {
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true

                                }
                            }
                        )
                    }
                    if (UserPermissionUtil.hasPermission(PERM_BUY_TICKET, CURRENT_USER?.permissions)) {
                        BottomNavigationItem(
                            icon = {
                                Icon(
                                    Icons.Filled.Payments,
                                    contentDescription = null,
                                    modifier = Modifier.size(25.dp)
                                )
                            },
                            label = { Text(text = "购票") },
                            selected = currentDestination?.hierarchy?.any { it.route == "buy" } == true,
                            unselectedContentColor = LocalContentColor.current.copy(alpha = ContentAlpha.disabled),
                            onClick = {
                                SCAN_TYPE = SCANTYPE.NONE
                                navController.navigate("buy") {
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        )
                    }
                    if (UserPermissionUtil.hasPermission(PERM_REPRINT, CURRENT_USER?.permissions)) {
                        BottomNavigationItem(
                            icon = {
                                Icon(
                                    Icons.Filled.Print,
                                    contentDescription = null,
                                    modifier = Modifier.size(25.dp)
                                )
                            },
                            label = { Text(text = "补票") },
                            selected = currentDestination?.hierarchy?.any { it.route == "reprint" } == true,
                            unselectedContentColor = LocalContentColor.current.copy(alpha = ContentAlpha.disabled),
                            onClick = {
                                SCAN_TYPE = SCANTYPE.REPRINT_TICKET
                                navController.navigate("reprint") {
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        )
                    }

                    val context = LocalContext.current
                    var showExchangeForTicket = context.resources.getBoolean(R.bool.show_exchange_for_ticket)
                    showExchangeForTicket = showExchangeForTicket and UserPermissionUtil.hasPermission(PERM_EXCHANGE_FOR_TICKET, CURRENT_USER?.permissions)
                    if (showExchangeForTicket) {
                        BottomNavigationItem(
                            icon = {
                                Icon(
                                    Icons.Filled.SyncAlt,
                                    contentDescription = null,
                                    modifier = Modifier.size(25.dp)
                                )
                            },
                            label = { Text(text = "换票") },
                            selected = currentDestination?.hierarchy?.any { it.route == "exchange" } == true,
                            unselectedContentColor = LocalContentColor.current.copy(alpha = ContentAlpha.disabled),
                            onClick = {
                                navController.navigate("exchange") {
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        )
                    }
                    BottomNavigationItem(
                        icon = { Icon(Icons.Filled.Settings, contentDescription = null, modifier = Modifier.size(25.dp)) },
                        label = { Text(text = "设置") },
                        selected = currentDestination?.hierarchy?.any { it.route == "settings" } == true,
                        unselectedContentColor = LocalContentColor.current.copy(alpha = ContentAlpha.disabled),
                        onClick = {
                            navController.navigate("settings") {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    )
                }
            },
            content = { innerPadding ->
                NavHost(
                    navController = navController,
                    startDestination = "check",
                    modifier = Modifier.padding(innerPadding),
                    builder = {
                        composable("check") {
                            CheckScreen()
                        }
                        composable("buy") {
                            BuyScreen()
                        }
                        composable("reprint") {
                            ReprintScreen()
                        }
                        composable("exchange") {
                            ExchangeForTicketScreen()
                        }
                        composable("settings") {
                            SettingsScreen()
                        }
                    })
            }
        )
        // 点击购票后的弹窗
        if (SHOW_POPUP and (SELECTED_GOOD != null)) {
            PopupView()
        }
        // 退票页面
        if (SHOW_REFUND) {
            RefundView()
        }
    }
}
